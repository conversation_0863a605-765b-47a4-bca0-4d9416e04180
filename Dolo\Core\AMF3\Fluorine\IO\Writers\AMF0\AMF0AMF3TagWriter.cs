﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF0;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF0AMF3TagWriter : IAMFWriter
{

    #region IAMFWriter Members

    public bool IsPrimitive => false;

    public void WriteData(AMFWriter writer, object data)
    {
        writer.WriteByte(AMF0TypeCode.AMF3Tag);
        writer.WriteAMF3Data(data);
    }

    #endregion
}