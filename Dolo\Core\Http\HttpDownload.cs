﻿namespace Dolo.Core.Http;

/// <summary>
/// Represents the result of an HTTP download operation.
/// </summary>
public class HttpDownload
{
    /// <summary>
    /// Gets or sets the stream containing the downloaded data.
    /// </summary>
    public Stream? Stream { get; set; }

    /// <summary>
    /// Gets or sets the exception that occurred during the download, if any.
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the download was successful.
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether the download was cancelled.
    /// </summary>
    public bool IsCancelled { get; set; }
}