﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Text.RegularExpressions;
using System.Web;
namespace Dolo.Core.AMF3.Fluorine.Util;

/// <summary>
///     Provides an object representation of a uniform resource identifier (URI) and easy access to the parts of the URI.
///     protocol://user:password?host:port/path?param1=value&amp;param2=value2&amp;...
/// </summary>
internal class UriBase
{
    private string _host;
    private NameValueCollection _parameters;
    private string _password;
    private string _path;
    private string _port;
    private string _protocol;
    private string _user;

    /// <summary>
    ///     Initializes a new instance of the UriBase class.
    /// </summary>
    /// <param name="uri"></param>
    public UriBase(UriBase uri)
    {
        Clear();
        ParseUri(uri.Uri);
    }

    /// <summary>
    ///     Initializes a new instance of the UriBase class.
    /// </summary>
    public UriBase()
    {
        Clear();
    }

    /// <summary>
    ///     Initializes a new instance of the UriBase class.
    /// </summary>
    /// <param name="uri"></param>
    public UriBase(string uri)
    {
        Clear();
        ParseUri(uri);
    }

    /// <summary>
    ///     Initializes a new instance of the UriBase class.
    /// </summary>
    /// <param name="user"></param>
    /// <param name="password"></param>
    /// <param name="path"></param>
    /// <param name="host"></param>
    /// <param name="protocol"></param>
    /// <param name="port"></param>
    /// <param name="parameters"></param>
    public UriBase(string user, string password, string path, string host, string protocol, string port,
        NameValueCollection parameters)
    {
        _user = user;
        _password = password;
        _path = path;
        _host = host;
        _protocol = protocol;
        _port = port;
        _parameters = parameters;
    }

    /// <summary>
    ///     Initializes a new instance of the UriBase class.
    /// </summary>
    /// <param name="user"></param>
    /// <param name="password"></param>
    /// <param name="path"></param>
    /// <param name="host"></param>
    /// <param name="protocol"></param>
    /// <param name="port"></param>
    public UriBase(string user, string password, string path, string host, string protocol, string port)
    {
        _user = user;
        _password = password;
        _path = path;
        _host = host;
        _protocol = protocol;
        _port = port;
    }

    /// <summary>
    ///     Gets or sets the path specified in the url.
    /// </summary>
    public string Path
    {
        get => _path;
        set
        {
            if (_path != value)
                _path = value;
        }
    }

    /// <summary>
    ///     Gets or sets the host specified in the url.
    /// </summary>
    public string Host
    {
        get => _host;
        set
        {
            if (_host != value)
                _host = value;
        }
    }

    /// <summary>
    ///     Gets or sets the additional parameters specified in the url.
    /// </summary>
    public NameValueCollection Parameters
    {
        get => _parameters;
        set
        {
            if (_parameters != value)
                _parameters = value;
        }
    }

    /// <summary>
    ///     Gets or sets the host password in the url.
    /// </summary>
    public string Password
    {
        get => _password;
        set
        {
            if (_password != value)
                _password = value;
        }
    }

    /// <summary>
    ///     Gets or sets the port specified in the url.
    /// </summary>
    public string Port
    {
        get => _port;
        set
        {
            if (_port != value)
                _port = value;
        }
    }

    /// <summary>
    ///     Gets or sets the protocol specified in the url.
    /// </summary>
    public string Protocol
    {
        get => _protocol;
        set
        {
            if (_protocol != value)
                _protocol = value;
        }
    }

    /// <summary>
    ///     Gets or sets user specified in the url.
    /// </summary>
    public string User
    {
        get => _user;
        set
        {
            if (_user != value)
                _user = value;
        }
    }

    /// <summary>
    ///     Gets or sets the url.
    /// </summary>
    public string Uri
    {
        get
        {
            var tempUrl = new StringBuilder();
            if (Protocol != null)
            {
                tempUrl.Append(Protocol);
                tempUrl.Append("://");
                if (User != null && User != string.Empty)
                    tempUrl.Append(string.Format("{0}:{1}@", User, Password));
                if (Host != null)
                    tempUrl.Append(Host);
                if (Port != null)
                {
                    tempUrl.Append(":");
                    tempUrl.Append(Port);
                }

                if (Path != null && Path != string.Empty)
                    tempUrl.Append(string.Format("/{0}", Path));
                else
                    tempUrl.Append("/");
            }

            if (_parameters != null)
                for (var i = 0; i < _parameters.Count; i++)
                {
                    var key = _parameters.GetKey(i);
                    var value = _parameters.Get(i);
                    if (i == 0)
                        tempUrl.Append("?");
                    else
                        tempUrl.Append("&");
                    tempUrl.Append(key);
                    tempUrl.Append("=");
                    tempUrl.Append(value);
                }

            return tempUrl.ToString();
        }
        set => ParseUri(value);
    }

    /// <summary>
    ///     Clears the url.
    /// </summary>
    public void Clear()
    {
        _protocol = "";
        _host = "";
        _port = null;
        _path = "";
        _user = "";
        _password = "";
        _parameters = null;
    }

    private void InternalParseUri(string url)
    {
        string tempUrl;
        Regex regex;
        Match match;
        string user;
        string password;
        string path;
        string host;
        string protocol;
        string port;
        string parameters;
        NameValueCollection parametersCollection;
        string parameterPart;
        string[] parameter;
        char[] separator;
        IEnumerator enumerator;
        try
        {
            tempUrl = url;
            if (tempUrl?.Length == 0)
                tempUrl = ":///";

            regex = new(@"^(?<protocol>[\w\%]*)://((?'username'[\w\%]*)(:(?'password'[\w\%]*))?@)?(?'host'[\{\}\w\.\(\)\-\%\\\$]*)(:?(?'port'[\{\}\w\.]+))?(/(?'path'[^?]*)?(\?(?'params'.*))?)?");
            match = regex.Match(tempUrl);
            if (!match.Success) throw new ApplicationException("This Uri cannot be parsed.");

            user = HttpUtility.UrlDecode(match.Result("${username}"));
            password = HttpUtility.UrlDecode(match.Result("${password}"));
            path = HttpUtility.UrlDecode(match.Result("${path}"));
            host = HttpUtility.UrlDecode(match.Result("${host}"));
            protocol = HttpUtility.UrlDecode(match.Result("${protocol}"));
            port = null;
            if (match.Result("${port}").Length != 0)
                //port = int.Parse(match.Result("${port}"));
                port = match.Result("${port}");
            //if (port < 0 || port > 65535)
            //	throw new ApplicationException("This Uri cannot be parsed. Invalid Port number.");

            parameters = match.Result("${params}");
            parametersCollection =
                [];
            if (parameters != null && parameters != string.Empty)
            {
                separator =
                [
                    '&'
                ];
                var splittedParameters = parameters.Split(separator);
                enumerator = splittedParameters.GetEnumerator();
                while (enumerator.MoveNext())
                {
                    parameterPart = (string)enumerator.Current;
                    separator =
                    [
                        '='
                    ];
                    parameter = parameterPart.Split(separator, 2);
                    if (parameter.Length != 2)
                        throw new ApplicationException("This Uri cannot be parsed. Invalid parameter.");
                    parametersCollection.Add(HttpUtility.UrlDecode(parameter[0]), HttpUtility.UrlDecode(parameter[1]));
                }
            }

            _user = user;
            _password = password;
            _path = path;
            _host = host;
            _protocol = protocol;
            _port = port;
            _parameters = parametersCollection;
        }
        catch (Exception ex)
        {
            if (ex is ApplicationException)
                throw;
            throw new ApplicationException("This Uri cannot be parsed.", ex);
        }
    }

    /// <summary>
    ///     Parse the uri.
    /// </summary>
    /// <param name="uri"></param>
    protected void ParseUri(string uri)
    {
        InternalParseUri(uri);
    }

    /// <summary>
    ///     Returns whether the value of the called object is equal to that of the given object.
    ///     Equality here means if all the fields are the same.
    /// </summary>
    /// <param name="uri"></param>
    /// <returns></returns>
    public bool EqualTo(UriBase uri)
    {
        if (uri == null)
            return false;
        return Uri == uri.Uri;
    }

    /// <summary>
    ///     Copy content of this object into the given object.
    /// </summary>
    /// <param name="uri"></param>
    public void CopyTo(UriBase uri)
    {
        Uri = uri.Uri;
    }
}