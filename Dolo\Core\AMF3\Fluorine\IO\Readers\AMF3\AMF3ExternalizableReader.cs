﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO.Bytecode;
namespace Dolo.Core.AMF3.Fluorine.IO.Readers.AMF3;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF3ExternalizableReader : IReflectionOptimizer
{

    #region IReflectionOptimizer Members

    public object CreateInstance() => default;

    public object ReadData(AMFReader reader, ClassDefinition classDefinition)
    {
        var instance = ObjectFactory.CreateInstance(classDefinition.ClassName);
        if (instance == null)
        {
            var msg = __Res.GetString(__Res.Type_InitError, classDefinition.ClassName);
            throw new AMFException(msg);
        }
        reader.AddAMF3ObjectReference(instance);
        if (instance is IExternalizable)
        {
            var externalizable = instance as IExternalizable;
            var dataInput = new DataInput(reader);
            externalizable.ReadExternal(dataInput);
            return instance;
        }
        {
            var msg = __Res.GetString(__Res.Externalizable_CastFail, instance.GetType().FullName);
            throw new AMFException(msg);
        }
    }

    #endregion
}