﻿namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("event")]
    [Description("pick a random user for event")]
    public async Task VoteAsync(SlashCommandContext ctx,
        [Description("the event message to pick reactions")] string message,
        [Description("the text what the user won")] string text)
    {
        if (HubChannel.Event is null)
            return;

        await ctx.Interaction.DeferAsync();

        // try to parse the message
        if (!ulong.TryParse(message, out var id))
        {
            await ctx.TryEditResponseAsync("invalid message id");
            return;
        }

        // get the message from the event chanel
        var msg = await HubChannel.Event.TryGetMessageAsync(id);

        // return if null
        if (msg is null)
        {
            await ctx.TryEditResponseAsync("message not found");
            return;
        }

        // get the reactions from the first emoji
        var users = await msg.TryGetReactionsAsync(msg.Reactions.Select(a => a.Count).Count(), msg.Reactions[0]?.Emoji);

        // return if null
        if (users is null)
        {
            await ctx.TryEditResponseAsync($"no users found on the message {msg.Id}");
            return;
        }

        // randomize the users
        var user = users
            .ToList()
            .Shuffle()
            .FirstOrDefault();

        // return if null
        if (user is null)
        {
            await ctx.TryEditResponseAsync("could not find a user for the first reaction");
            return;
        }

        if (!msg.Channel.Threads.Any())
        {
            await ctx.TryEditResponseAsync("no thread found");
            return;
        }

        // print the message
        await msg.Channel.Threads.First()
            .TrySendMessageAsync($"<@{user.Id}> Congratulation! {HubEmoji.WhiteHeart} You won {text}! Please contact dolo.");

        // print response
        await ctx.TryEditResponseAsync($"Winner picked! in <#{user.Id}>");
    }
}