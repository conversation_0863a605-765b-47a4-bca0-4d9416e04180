﻿using System.Diagnostics.CodeAnalysis;
using System.Drawing;
using System.Runtime.InteropServices;
namespace Dolo.Core.Windows;

[SuppressMessage("Interoperability", "CA1416:Plattformkompatibilität überprüfen")]
public static partial class Windows
{
    [DllImport("gdi32.dll", EntryPoint = "GetDeviceCaps", SetLastError = true)]
    private static extern int GetDeviceCaps(nint hdc, int nIndex);

    public static double GetScale()
    {
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            return 1.0;

        using var g = Graphics.FromHwnd(nint.Zero);

        var desktop = g.GetHdc();
        var screenScalingFactor = (double)GetDeviceCaps(desktop, 117) / GetDeviceCaps(desktop, 10);

        return screenScalingFactor;
    }
}