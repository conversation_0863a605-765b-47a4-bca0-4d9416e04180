namespace Dolo.Core.Interceptor.Models;

public sealed class TransactionPairedEventArgs : EventArgs
{
    public HttpTransaction Transaction { get; }
    public DateTime PairedAt { get; } = DateTime.UtcNow;

    public TransactionPairedEventArgs(HttpTransaction transaction)
    {
        Transaction = transaction;
    }
}

public sealed class TransactionTimeoutEventArgs : EventArgs
{
    public string TransactionId { get; }
    public string Reason { get; }
    public DateTime TimeoutAt { get; } = DateTime.UtcNow;

    public TransactionTimeoutEventArgs(string transactionId, string reason)
    {
        TransactionId = transactionId;
        Reason = reason;
    }
}

public sealed class CompletedTransaction
{
    public HttpTransaction Transaction { get; init; } = new();
    public ProcessedResponse? ProcessedResponse { get; init; }
    public DateTime CompletedAt { get; init; }
    public TransactionStatus Status { get; init; }
    public string? ErrorMessage { get; init; }
}

public enum TransactionStatus
{
    Completed,
    Failed,
    Timeout
}
