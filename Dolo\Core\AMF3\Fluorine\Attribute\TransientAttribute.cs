﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Attribute;

/// <summary>
///     Indicates that serialization is turned off on a certain field or property.
///     Member variables marked by the transient attribute are not transferred.
/// </summary>
[AttributeUsage(AttributeTargets.Field | AttributeTargets.Property)]
internal class TransientAttribute : System.Attribute
{
    /// <summary>
    ///     Initializes a new instance of the TransientAttribute class.
    /// </summary>
    public TransientAttribute()
    {}
}