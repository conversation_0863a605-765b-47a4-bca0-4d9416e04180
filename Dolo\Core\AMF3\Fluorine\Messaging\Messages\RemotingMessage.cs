﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Messages;

/// <summary>
///     RemotingMessages are used to send RPC requests to a remote endpoint. These messages use the operation property to
///     specify which method to call on the remote object. The destination property indicates what object/service should be
///     used.
/// </summary>
internal class RemotingMessage : MessageBase
{
    /// <summary>
    ///     Initializes a new instance of the RemotingMessage class.
    /// </summary>
    public RemotingMessage()
    {}
    /// <summary>
    ///     Gets or sets the underlying source of a RemoteObject destination.
    /// </summary>
    /// <remarks>
    ///     This property is provided for backwards compatibility. The best practice, however, is
    ///     to not expose the underlying source of a RemoteObject destination on the client
    ///     and only one source to a destination. Some types of Remoting Services may even ignore
    ///     this property for security reasons.
    /// </remarks>
    public string source
    {
        get;
        set;
    }
    /// <summary>
    ///     Gets or sets the name of the remote method/operation that should be called.
    /// </summary>
    public string operation
    {
        get;
        set;
    }
}