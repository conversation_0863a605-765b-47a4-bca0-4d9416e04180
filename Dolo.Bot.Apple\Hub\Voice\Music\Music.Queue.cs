using Dolo.Bot.Apple.Hub.Voice.Music.Services;
using Dolo.Database;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

public partial class Music
{
    [Command("queue")]
    [Description("show the current music queue")]
    public async Task QueueAsync(SlashCommandContext ctx, 
        [Description("page number")] int page = 1)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        await ctx.TryEditResponseAsync(MusicEmbeds.Queue(player, page));
    }

    [Command("nowplaying")]
    [Description("show information about the current track")]
    public async Task NowPlayingAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        if (player.CurrentTrack == null)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("Nothing is currently playing."));
            return;
        }

        await ctx.TryEditResponseAsync(MusicEmbeds.NowPlaying(player.CurrentTrack, player));
    }

    [Command("clear")]
    [Description("clear the music queue")]
    public async Task ClearAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        var queueCount = player.Queue.Count;
        player.Queue.Clear();
        
        await ctx.TryEditResponseAsync(MusicEmbeds.Success($"🗑️ Cleared {queueCount} track(s) from the queue."));
    }

    [Command("shuffle")]
    [Description("shuffle the music queue")]
    public async Task ShuffleAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        if (player.Queue.Count < 2)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("Need at least 2 tracks in queue to shuffle."));
            return;
        }

        player.Queue.Shuffle();
        await ctx.TryEditResponseAsync(MusicEmbeds.Success("🔀 Queue shuffled!"));
    }

    [Command("loop")]
    [Description("toggle loop mode for the current track")]
    public async Task LoopAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        player.Queue.IsLooping = !player.Queue.IsLooping;
        var status = player.Queue.IsLooping ? "enabled" : "disabled";
        var emoji = player.Queue.IsLooping ? "🔂" : "➡️";
        
        await ctx.TryEditResponseAsync(MusicEmbeds.Success($"{emoji} Loop mode {status}."));
    }

    [Command("remove")]
    [Description("remove a track from the queue")]
    public async Task RemoveAsync(SlashCommandContext ctx, 
        [Description("position in queue to remove")] int position)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        if (position < 1 || position > player.Queue.Count)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error($"Invalid position. Queue has {player.Queue.Count} track(s)."));
            return;
        }

        var tracks = player.Queue.GetTracks();
        var trackToRemove = tracks[position - 1];
        
        if (player.Queue.RemoveTrack(position - 1))
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Success($"🗑️ Removed: **{trackToRemove.FormattedTitle}**"));
        }
        else
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("Failed to remove track from queue."));
        }
    }

    [Command("status")]
    [Description("show the music player status")]
    public async Task StatusAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        await ctx.TryEditResponseAsync(MusicEmbeds.PlayerStatus(player));
    }
}
