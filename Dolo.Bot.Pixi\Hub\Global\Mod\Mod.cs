﻿using DSharpPlus.Commands.ContextChecks;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

[RequireApplicationOwner]
[Command("mod")]
[Description("moderator commands")]
public partial class Mod
{
    [Command("blacklist")]
    [Description("blacklist commands")]
    public partial class Blacklist
    {}

    [Command("embed")]
    [Description("configuration commands")]
    public partial class Embed
    {}

    [Command("maintenance")]
    [Description("maintenance commands")]
    public partial class Maintenance
    {}

    [Command("feedback")]
    [Description("feedback commands")]
    public partial class Feedback
    {}

    [Command("cheater")]
    [Description("cheater commands")]
    public partial class Cheater
    {}

    [Command("donation")]
    [Description("donation commands")]
    public partial class Donation
    {}
}