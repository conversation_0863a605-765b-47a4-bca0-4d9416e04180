﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Collections.Generic;
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using Dolo.Core.AMF3.Fluorine.Messaging.Api.Event;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class BasicScope : PersistableAttributeStore, IBasicScope
{
    /// <summary>
    ///     Set to true to prevent the scope from being freed upon disconnect.
    /// </summary>
    protected bool _keepOnDisconnect = false;

    protected CopyOnWriteArray<IEventListener> _listeners = new();
    protected IScope _parent;

    public BasicScope(IScope parent, string type, string name, bool persistent) : base(type, name, null, persistent) => _parent = parent;

    public virtual void AddEventListener(IEventListener listener)
    {
        _listeners.Add(listener);
    }

    public virtual void RemoveEventListener(IEventListener listener)
    {
        _listeners.Remove(listener);
        if (!_keepOnDisconnect && _listeners.Count == 0)
            // Delete empty rooms
            _parent.RemoveChildScope(this);
    }

    public ICollection GetEventListeners() => _listeners;

    public bool HandleEvent(IEvent evt) => false;

    public void NotifyEvent(IEvent evt)
    {}

    public virtual void DispatchEvent(IEvent evt)
    {
        foreach (var listener in _listeners)
            if (evt.Source == null || evt.Source != listener)
                listener.NotifyEvent(evt);
    }

    #region IEnumerable Members

    public virtual IEnumerator GetEnumerator() => null;

    #endregion


    #region IBasicScope Members

    /// <summary>
    ///     Gets an object that can be used to synchronize access.
    /// </summary>
    public object SyncRoot { get; } = new();

    /// <summary>
    ///     Checks whether the scope has a parent.
    ///     You can think of scopes as of tree items
    ///     where scope may have a parent and children (child).
    /// </summary>
    public bool HasParent => _parent != null;

    /// <summary>
    ///     Get this scope's parent.
    /// </summary>
    public virtual IScope Parent
    {
        get => _parent;
        set => _parent = value;
    }

    /// <summary>
    ///     Get the scopes depth, how far down the scope tree is it. The lowest depth
    ///     is 0x00, the depth of Global scope. Application scope depth is 0x01. Room
    ///     depth is 0x02, 0x03 and so forth.
    /// </summary>
    public int Depth
    {
        get
        {
            if (HasParent)
                return _parent.Depth + 1;
            return 0;
        }
    }

    /// <summary>
    ///     Gets the full absolute path.
    /// </summary>
    public override string Path
    {
        get
        {
            if (HasParent)
                return _parent.Path + "/" + _parent.Name;
            return string.Empty;
        }
    }

    #endregion
}