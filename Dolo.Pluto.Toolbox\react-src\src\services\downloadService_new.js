/**
 * Download Service for Pluto Toolbox
 * Handles file downloads with progress tracking using streaming and chunked writes
 */
import neuService from './neuService';

class DownloadService {
  /**
   * Download a file directly to the destination path using streaming and chunked writes.
   * 
   * WHY CHUNKING IS FASTER:
   * 1. Memory Efficiency: Never loads entire file into memory at once
   * 2. Parallel Processing: Download and write operations happen simultaneously
   * 3. Smaller I/O Operations: 64KB writes are much faster than 100MB+ writes
   * 4. Better Error Recovery: If one chunk fails, we don't lose the entire download
   * 5. Neutralino Optimization: appendBinaryFile is optimized for incremental writes
   * 
   * @param {string} fileUrl - URL of the file to download
   * @param {string} destinationPath - Path to save the downloaded file
   * @param {Function} onProgress - Optional callback function for progress updates
   * @returns {Promise<boolean>} - Whether the download was successful
   */
  async downloadFile(fileUrl, destinationPath, onProgress = null) {
    console.info(`[DownloadService] Starting streaming download: ${fileUrl} to ${destinationPath}`);
    
    // Configuration for chunked streaming
    const CHUNK_SIZE = 64 * 1024; // 64KB chunks - optimal balance of memory vs performance
    const TIMEOUT_MS = 60000; // 60 seconds timeout
    const STALL_TIMEOUT_MS = 30000; // 30 seconds for stall detection
    const MAX_RETRY_ATTEMPTS = 10; // Maximum retries for failed reads

    try {
      // Ensure destination directory exists
      const lastSlashIndex = destinationPath.lastIndexOf('/');
      if (lastSlashIndex > 0) {
        const directoryPath = destinationPath.substring(0, lastSlashIndex);
        try {
          await neuService.createDirectory(directoryPath);
        } catch (dirError) {
          console.error(`[DownloadService] Failed to create directory: ${directoryPath}`, dirError);
          throw new Error(`Cannot create download directory. Please check permissions.`);
        }
      }

      console.info(`[DownloadService] Fetching stream from: ${fileUrl}`);

      // Initialize progress callback
      if (typeof onProgress === 'function') {
        onProgress({
          progress: 0,
          phase: 'Starting download...',
          loaded: 0,
          total: 0
        });
      }

      // Remove any pre-existing file
      try {
        await neuService.remove(destinationPath);
      } catch (error) {
        // Ignore error if file does not exist
      }

      // Begin fetch with timeout
      const response = await Promise.race([
        fetch(fileUrl, {
          method: 'GET',
          mode: 'cors',
          cache: 'no-cache'
        }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Download timed out')), TIMEOUT_MS))
      ]);

      if (!response.ok) {
        throw new Error(`HTTP error: ${response.status} ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const contentLength = parseInt(response.headers.get('Content-Length')) || 0;

      console.info(`[DownloadService] Starting chunked streaming download of ${contentLength ? `${(contentLength / (1024 * 1024)).toFixed(2)} MB` : 'unknown size'}...`);
      
      // Create empty file first - this is much faster than a single large write
      console.info(`[DownloadService] Creating empty file for chunked writing...`);
      await neuService.writeBinaryFile(destinationPath, '');
      
      let receivedLength = 0;
      let lastProgressTime = Date.now();
      let consecutiveFailures = 0;
      let totalReads = 0;
      let chunkBuffer = new Uint8Array(CHUNK_SIZE);
      let chunkBufferOffset = 0;

      // Stream and write chunks in real-time
      while (true) {
        totalReads++;
        
        try {
          const { done, value } = await Promise.race([
            reader.read(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Read operation timed out')), TIMEOUT_MS))
          ]);

          if (done) {
            // Write any remaining data in the chunk buffer
            if (chunkBufferOffset > 0) {
              const finalChunk = chunkBuffer.slice(0, chunkBufferOffset);
              const base64Chunk = this.uint8ArrayToBase64(finalChunk);
              await neuService.appendBinaryFile(destinationPath, base64Chunk);
              console.debug(`[DownloadService] Wrote final chunk: ${chunkBufferOffset} bytes`);
            }
            console.info(`[DownloadService] Streaming download complete after ${totalReads} read operations`);
            break;
          }

          if (value && value.length > 0) {
            receivedLength += value.length;
            lastProgressTime = Date.now();
            consecutiveFailures = 0; // Reset failure counter on successful read

            // Process the received data in fixed-size chunks
            let valueOffset = 0;
            while (valueOffset < value.length) {
              const remainingInChunk = CHUNK_SIZE - chunkBufferOffset;
              const remainingInValue = value.length - valueOffset;
              const bytesToCopy = Math.min(remainingInChunk, remainingInValue);

              // Copy data to chunk buffer
              chunkBuffer.set(value.subarray(valueOffset, valueOffset + bytesToCopy), chunkBufferOffset);
              chunkBufferOffset += bytesToCopy;
              valueOffset += bytesToCopy;

              // If chunk buffer is full, write it to file
              if (chunkBufferOffset === CHUNK_SIZE) {
                const base64Chunk = this.uint8ArrayToBase64(chunkBuffer);
                await neuService.appendBinaryFile(destinationPath, base64Chunk);
                console.debug(`[DownloadService] Wrote chunk: ${CHUNK_SIZE} bytes`);
                
                // Reset chunk buffer for next chunk
                chunkBufferOffset = 0;
              }
            }

            // Report progress frequently for smooth UI updates
            if (contentLength && typeof onProgress === 'function' && totalReads % 10 === 0) {
              const progress = Math.round((receivedLength / contentLength) * 100);
              onProgress({
                progress,
                phase: 'Downloading and writing...',
                loaded: receivedLength,
                total: contentLength,
                size: `${(receivedLength / (1024 * 1024)).toFixed(2)} MB / ${(contentLength / (1024 * 1024)).toFixed(2)} MB`
              });
            }
          }

          // Stall detection - check if we haven't made progress in a while
          const timeSinceProgress = Date.now() - lastProgressTime;
          if (timeSinceProgress > STALL_TIMEOUT_MS) {
            throw new Error(`Download stalled - no progress for ${Math.round(timeSinceProgress/1000)}s`);
          }
          
        } catch (readError) {
          consecutiveFailures++;
          
          // If it's a timeout or stall, throw immediately
          if (readError.message.includes('timed out') || readError.message.includes('stalled')) {
            throw readError;
          }
          
          // If we've had too many consecutive failures, give up
          if (consecutiveFailures >= MAX_RETRY_ATTEMPTS) {
            throw new Error(`Download failed after ${consecutiveFailures} consecutive read failures: ${readError.message}`);
          }
          
          console.warn(`[DownloadService] Read failure ${consecutiveFailures}/${MAX_RETRY_ATTEMPTS}, retrying:`, readError.message);
          const backoffDelay = 100 * consecutiveFailures;
          await new Promise(resolve => setTimeout(resolve, backoffDelay)); // Exponential backoff
        }
      }

      // Verify file creation and completeness
      const fileExists = await neuService.exists(destinationPath);
      if (!fileExists) {
        throw new Error('File download failed - file was not created');
      }
      const stats = await neuService.getStats(destinationPath);
      console.info(`[DownloadService] Streaming download complete: ${destinationPath}, size: ${stats.size} bytes`);

      // Final progress update
      if (typeof onProgress === 'function') {
        onProgress({ 
          progress: 100, 
          phase: 'Download complete', 
          loaded: receivedLength, 
          total: contentLength || receivedLength,
          size: `${(receivedLength / (1024 * 1024)).toFixed(2)} MB`
        });
      }

      return true;
    } catch (error) {
      console.error(`[DownloadService] Download failed:`, error);
      
      // Attempt to clean up the partial file on error
      try {
        await neuService.remove(destinationPath);
      } catch (cleanupError) {
        console.warn(`[DownloadService] Could not cleanup partial file: ${cleanupError.message}`);
      }
      
      // Provide user-friendly error message
      let userFriendlyMessage = 'Download failed';
      
      if (error.message?.includes('timeout') || error.message?.includes('timed out')) {
        userFriendlyMessage = 'Download timed out. Please check your internet connection.';
      } else if (error.message?.includes('HTTP error: 404')) {
        userFriendlyMessage = 'File not found. This tool may no longer be available.';
      } else if (error.message?.includes('HTTP error: 403')) {
        userFriendlyMessage = 'Access denied. You may not have permission to download this file.';
      } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
        userFriendlyMessage = 'Network error. Please check your internet connection.';
      } else if (error.message?.includes('space') || error.message?.includes('ENOSPC')) {
        userFriendlyMessage = 'Not enough disk space. Please free up some space and try again.';
      } else if (error.message?.includes('Maximum read attempts')) {
        userFriendlyMessage = 'Download stalled. Please try again.';
      }
      
      if (typeof onProgress === 'function') {
        onProgress({ 
          progress: 0, 
          phase: userFriendlyMessage,
          error: userFriendlyMessage,
          resetState: true
        });
      }
      
      throw new Error(userFriendlyMessage);
    }
  }

  /**
   * Convert Uint8Array to base64 string efficiently for small chunks
   * @param {Uint8Array} uint8Array - The binary data to convert
   * @returns {string} - Base64 encoded string
   */
  uint8ArrayToBase64(uint8Array) {
    // For small chunks (64KB), we can use the simple approach
    let binaryString = '';
    for (let i = 0; i < uint8Array.length; i++) {
      binaryString += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binaryString);
  }
}

const downloadService = new DownloadService();
export default downloadService;
