﻿namespace Dolo.Bot.Apple.Hub;

public static class HubMessage
{
    /// <summary>
    ///     Get the activator message
    /// </summary>
    public static async Task<DiscordMessage?> GetActivatorAsync(ulong? id = default)
        => HubChannel.Activator is {} ? await HubChannel.Activator.TryGetMessageAsync(id ?? 961269735352332299)
               : default;

    /// <summary>
    ///     Get the birthday message
    /// </summary>
    public static async Task<DiscordMessage?> GetBirthdayAsync(ulong? id = default)
        => HubChannel.Birthday is {} ? await HubChannel.Birthday.TryGetMessageAsync(id ?? 813048025273466920)
               : default;

    /// <summary>
    ///     Get the welcome message
    /// </summary>
    public static async Task<DiscordMessage?> GetWelcomeAsync(ulong? id = default)
        => HubChannel.Welcome is {} ? await HubChannel.Welcome.TryGetMessageAsync(id ?? 772233477688655912)
               : default;
    /// <summary>
    ///     Get the rewards message
    /// </summary>
    public static async Task<DiscordMessage?> GetRewardAsync(ulong? id = default)
        => HubChannel.Reward is {} ? await HubChannel.Reward.TryGetMessageAsync(id ?? 1207108766118576200)
               : default;
}