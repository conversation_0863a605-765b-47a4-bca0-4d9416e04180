# Dolo Log Bot - Enhanced Logging Features

## New Features Added

### 🔧 Slash Command Logging
- **Event**: `InteractionCreated`
- **Channel**: Configure `HubChannel.SlashCommands` 
- **Logs**: User, command name, parameters, timestamp
- **Color**: Discord Blue (#5865F2)

### 📎 Attachment Logging
- **Events**: `MessageCreated` (attachments posted), `MessageDeleted` (attachments deleted)
- **Channel**: Configure `HubChannel.Attachments`
- **Logs**: File names, URLs, sizes, dimensions, content types
- **Features**: 
  - Distinguishes between images 🖼️, videos 🎥, and files 📎
  - Shows file sizes in KB
  - Displays image/video dimensions
  - Preserves attachment URLs for deleted content

## Setup Instructions

### 1. Create Discord Channels
Create two new channels in your Discord server:
- `#slash-commands-log` - For logging slash command executions
- `#attachments-log` - For logging file attachments

### 2. Update Channel IDs
In `Dolo.Bot.Log\Hub\HubChannel.cs`, replace the `0` values with your actual channel IDs:

```csharp
public static DiscordChannel? SlashCommands
    => Hub.Guild?.TryGetChannel(YOUR_SLASH_COMMANDS_CHANNEL_ID);
public static DiscordChannel? Attachments
    => Hub.Guild?.TryGetChannel(YOUR_ATTACHMENTS_CHANNEL_ID);
```

### 3. Get Channel IDs
To get channel IDs:
1. Enable Developer Mode in Discord (User Settings > Advanced > Developer Mode)
2. Right-click on the channel and select "Copy ID"
3. Replace the `0` with the copied ID (without quotes)

## Features Overview

### Enhanced Message Caching
- Messages now cache attachment metadata separately
- Preserves attachment information even after deletion
- Tracks file names, URLs, sizes, and dimensions

### Slash Command Tracking
- Logs all slash command executions
- Shows command parameters and values
- Includes user information and channel context
- Filters out bot commands automatically

### Attachment Management
- **Posted Attachments**: Logs when users post files/images
- **Deleted Attachments**: Preserves attachment info when messages are deleted
- **Rich Information**: File types, sizes, dimensions, URLs
- **Smart Filtering**: Ignores bot attachments

### Error Handling
- Graceful error handling for all new features
- Console logging for debugging issues
- Null-safe operations throughout

## Log Examples

### Slash Command Log
```
🔵 Slash Command Executed
User: @username
Name: username
Channel: #general
Command: /voice help

Parameters:
```
None
```
```

### Attachment Log
```
🖼️ Attachments Posted/Deleted
User: @username  
Name: username
Channel: #general
Message ID: 123456789

2 Attachment(s) Posted:
```
🖼️ screenshot.png (1920x1080) - 245.3 KB
   URL: https://cdn.discordapp.com/attachments/...
🎥 video.mp4 - 1024.7 KB  
   URL: https://cdn.discordapp.com/attachments/...
```
```

## Benefits

1. **Complete Audit Trail**: Track all user interactions and file sharing
2. **Moderation Support**: Quickly identify problematic content or commands
3. **Data Preservation**: Keep attachment URLs even after deletion
4. **Performance Optimized**: Efficient caching and logging system
5. **User-Friendly**: Clear, readable log formats with emojis and formatting

## Technical Details

- **Thread-Safe**: Uses proper async/await patterns
- **Memory Efficient**: Automatic cleanup of old cached data
- **Error Resilient**: Continues logging even if individual operations fail
- **Extensible**: Easy to add new logging features in the future
