﻿namespace Dolo.Bot.Apple.Hub.Voice;

[Command("voice")]
[Description("group commands for voice system")]
public partial class Voice 
{
    [Command("help")]

[Description("get all commands of the voice system")]
    public async Task VoiceHelpAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.TryCreateResponseAsync(HubEmbed.Voice(ctx.User).Embeds[0]);
    }
}