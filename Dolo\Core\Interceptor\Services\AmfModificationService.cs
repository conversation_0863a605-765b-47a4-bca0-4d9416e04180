using System.Text;
using Dolo.Core.AMF3;
using Dolo.Core.AMF3.Fluorine.IO;
using Dolo.Planet.NET.Utils;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// Service for modifying AMF content while maintaining proper hash validation
/// </summary>
public class AmfModificationService
{
    /// <summary>
    /// Modifies AMF content and automatically updates the hash in the AMF header
    /// </summary>
    /// <param name="originalAmfBytes">Original AMF content as bytes</param>
    /// <param name="newData">New data to replace in the AMF content</param>
    /// <param name="isTicketRequired">Whether ticket validation is required for hash calculation</param>
    /// <returns>Modified AMF content with updated hash</returns>
    public async Task<byte[]> ModifyAmfContentAsync(byte[] originalAmfBytes, object?[] newData, bool isTicketRequired = false)
    {
        try
        {
            // Decode the original AMF content
            var originalAmfContent = await AMFBuilder.DecodeAsync(originalAmfBytes).ConfigureAwait(false);
            
            if (originalAmfContent?.Headers == null || !originalAmfContent.Headers.Any())
            {
                throw new InvalidOperationException("AMF content does not contain valid headers");
            }

            // Calculate new hash for the modified data
            var newHash = AMFHash.HashContent(newData, isTicketRequired);
            
            if (string.IsNullOrEmpty(newHash))
            {
                throw new InvalidOperationException("Failed to calculate hash for new data");
            }

            // Find and update the hash in the headers
            var headers = new List<AMFHeader>();
            foreach (var header in originalAmfContent.Headers)
            {
                if (header.Name == "id")
                {
                    // Update the hash
                    headers.Add(new AMFHeader("id", header.MustUnderstand, newHash));
                }
                else
                {
                    // Keep other headers unchanged
                    headers.Add(header);
                }
            }

            // Get the original endpoint from the first body
            string? endpoint = null;
            if (originalAmfContent.Bodies?.Any() == true)
            {
                var firstBody = originalAmfContent.Bodies[0];
                endpoint = firstBody.Target;
            }

            // Create new AMF message with updated hash and new data
            var newAmfMessage = AMFBuilder.Encode<object>(headers, endpoint, newData);
            
            // Serialize the new AMF message
            var serializer = new AMFSerializer(newAmfMessage);
            return serializer.GetData();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to modify AMF content: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Modifies AMF content by replacing specific data objects
    /// </summary>
    /// <param name="originalAmfBytes">Original AMF content as bytes</param>
    /// <param name="dataModifier">Function to modify the data array</param>
    /// <param name="isTicketRequired">Whether ticket validation is required for hash calculation</param>
    /// <returns>Modified AMF content with updated hash</returns>
    public async Task<byte[]> ModifyAmfContentAsync(byte[] originalAmfBytes, Func<object?[], object?[]> dataModifier, bool isTicketRequired = false)
    {
        try
        {
            // Decode the original AMF content
            var originalAmfContent = await AMFBuilder.DecodeAsync(originalAmfBytes).ConfigureAwait(false);
            
            if (originalAmfContent?.Content == null)
            {
                throw new InvalidOperationException("AMF content does not contain valid data");
            }

            // Extract original data
            object?[] originalData;
            if (originalAmfContent.Content is object[] dataArray)
            {
                originalData = dataArray;
            }
            else
            {
                originalData = [originalAmfContent.Content];
            }

            // Apply the modification function
            var newData = dataModifier(originalData);
            
            // Use the main modification method
            return await ModifyAmfContentAsync(originalAmfBytes, newData, isTicketRequired).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to modify AMF content with modifier function: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// Extracts data from AMF content for inspection
    /// </summary>
    /// <param name="amfBytes">AMF content as bytes</param>
    /// <returns>Extracted data array</returns>
    public async Task<object?[]?> ExtractAmfDataAsync(byte[] amfBytes)
    {
        try
        {
            var amfContent = await AMFBuilder.DecodeAsync(amfBytes).ConfigureAwait(false);
            
            if (amfContent?.Content == null)
            {
                return null;
            }

            if (amfContent.Content is object[] dataArray)
            {
                return dataArray;
            }
            
            return [amfContent.Content];
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Checks if the provided bytes contain valid AMF content
    /// </summary>
    /// <param name="bytes">Bytes to check</param>
    /// <returns>True if valid AMF content</returns>
    public static bool IsValidAmfContent(byte[] bytes)
    {
        if (bytes == null || bytes.Length < 6)
            return false;

        try
        {
            // Check for AMF version header (first 2 bytes should be version)
            // AMF3 version is typically 0x0003
            return bytes[0] == 0x00 && (bytes[1] == 0x03 || bytes[1] == 0x00);
        }
        catch
        {
            return false;
        }
    }
}
