﻿using Dolo.Bot.Apple.Hub.Global.Crypto.Entities;
namespace Dolo.Bot.Apple.Hub.Global.Crypto;

public partial class Crypto
{
    [Command("get")]

[Description("get the current crypto price")]
    public async Task GetAsync(SlashCommandContext e, [Description("crypto type")] CryptoType type, [Description("duration")] Duration duration)
    {
        await e.TryDeferAsync();

        if (type is CryptoType.WLD)
            await GetWorldCoinAsync(e, duration);
    }

    private async Task GetWorldCoinAsync(SlashCommandContext e, Duration duration)
    {
        var http = await Http.TrySendAsync<WorldCoin>(a => {
            a.Method = HttpMethod.Get;
            a.Referer = "https://worldcoin.org/worldcoin-token/";
            a.Url = $"https://worldcoin.org/api/price?interval={duration switch
            {
                Duration.Day   => "1d",
                Duration.Week  => "7d",
                Duration.Month => "30d",
                Duration.Year  => "365d"
            }}";
        });

        if (!http.IsSuccess || (!http.Body?.Success ?? false))
        {
            await e.TryEditResponseAsync("WorldCoin » Unknown error");
            return;
        }

        var negativeOrPositive = http.Body?.Delta < 0 ? "-" : "+";
        await e.TryEditResponseAsync($"WorldCoin » ${http.Body?.Price:N2} ({negativeOrPositive}{http.Body?.Delta:N2}%)");
    }
}