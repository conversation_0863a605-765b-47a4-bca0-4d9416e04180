namespace Dolo.Bot.Apple.Hub.Mod;

public class Warn
{
    [RequirePermissions(DiscordPermission.ManageMessages)]
    [Command("warn")]
    [Description("warn a member from the server")]
    public async Task WarnAsync(SlashCommandContext ctx, [Description("the user that will be warned")] DiscordUser user, [Description("the warn message")] string message)
    {
        await ctx.Interaction.DeferAsync(true);
        await ctx.Channel.TrySendMessageAsync($"{user.Mention} {message}");
        await ctx.TryCreateResponseAsync("User warned");
        await ctx.TryDeleteResponseAsync();
    }
}