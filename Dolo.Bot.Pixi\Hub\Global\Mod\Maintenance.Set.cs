﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Maintenance
    {
        [Command("set")]

[Description("set the maintenance message")]
        public async Task SetAsync(SlashCommandContext ctx, [Description("the message to set")] string message)
        {
            if (ctx.User.Id != 440584675740876810) return;

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(a => a.Maintenance, message));

            await ctx.TryCreateResponseAsync($"Maintenance message set to {message}");
        }
    }
}