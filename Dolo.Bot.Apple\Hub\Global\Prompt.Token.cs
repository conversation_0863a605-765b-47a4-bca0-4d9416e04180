﻿namespace Dolo.Bot.Apple.Hub.Global;

public class PromptToken 
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("SetPromptToken")]

[Description("set the prompt token")]
    public async Task SetPromptTokenAsync(SlashCommandContext ctx, [Description("the prompt token")] string token)
    {
        await ctx.Interaction.DeferAsync(true);

        if (string.IsNullOrEmpty(token))
        {
            await ctx.TryEditResponseAsync("Please provide a token");
            return;
        }

        if (!File.Exists("prompt.token"))
            File.Create("prompt.token").Close();

        await File.WriteAllTextAsync("prompt.token", token);
        await ctx.TryEditResponseAsync("Prompt token set");
    }
}