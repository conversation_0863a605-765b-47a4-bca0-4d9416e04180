﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
using System.Security;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class MessageFilter : AbstractFilter
{
    private readonly EndpointBase _endpoint;

    public MessageFilter(EndpointBase endpoint)
        => _endpoint = endpoint;

    #region IFilter Members

    public override async Task Invoke(AMFContext context)
    {
        var messageOutput = context.MessageOutput;
        for (var i = 0; i < context.AMFMessage.BodyCount; i++)
        {
            var amfBody = context.AMFMessage.TryGetBodyAt(i);

            if (!amfBody.IsEmptyTarget)
                continue;

            var content = amfBody.Content;
            if (content is IList)
                content = (content as IList)[0];
            var message = content as IMessage;

            //Check for Flex2 messages and handle
            if (message != null)
            {
                if (Context.AMFContext.Current.Client == null)
                    Context.AMFContext.Current.SetCurrentClient(_endpoint.GetMessageBroker().ClientRegistry
                        .GetClient(message));

                if (message.clientId == null)
                    message.clientId = Guid.NewGuid().ToString("D");

                //Check if response exists.
                var responseBody = messageOutput.GetResponse(amfBody);
                if (responseBody != null) continue;

                try
                {
                    if (context.AMFMessage.BodyCount > 1)
                    {
                        var commandMessage = message as CommandMessage;
                        //Suppress poll wait if there are more messages to process
                        if (commandMessage is { operation: CommandMessage.PollOperation })
                            commandMessage.SetHeader(CommandMessage.AMFSuppressPollWaitHeader, null);
                    }

                    var resultMessage = _endpoint.ServiceMessage(message);
                    await resultMessage;
                    if (resultMessage.Result is ErrorMessage)
                    {
                        var errorMessage = resultMessage.Result as ErrorMessage;
                        responseBody = new ErrorResponseBody(amfBody, message, resultMessage.Result as ErrorMessage);
                        if (errorMessage.faultCode == ErrorMessage.ClientAuthenticationError)
                        {
                            messageOutput.AddBody(responseBody);
                            for (var j = i + 1; j < context.AMFMessage.BodyCount; j++)
                            {
                                amfBody = context.AMFMessage.TryGetBodyAt(j);

                                if (!amfBody.IsEmptyTarget)
                                    continue;

                                content = amfBody.Content;
                                if (content is IList)
                                    content = (content as IList)[0];
                                message = content as IMessage;

                                //Check for Flex2 messages and handle
                                if (message != null)
                                {
                                    responseBody = new ErrorResponseBody(amfBody, message,
                                    new SecurityException(errorMessage.faultString));
                                    messageOutput.AddBody(responseBody);
                                }
                            }

                            //Leave further processing
                            return;
                        }
                    }
                    else
                        responseBody = new(amfBody, resultMessage.Result);
                }
                catch (Exception exception)
                {
                    responseBody = new ErrorResponseBody(amfBody, message, exception);
                }

                messageOutput.AddBody(responseBody);
            }
        }
    }

    #endregion
}