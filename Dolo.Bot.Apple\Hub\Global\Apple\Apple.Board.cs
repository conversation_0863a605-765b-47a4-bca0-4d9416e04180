﻿using Dolo.Bot.Apple.Hub.Enums;
using Dolo.Database;
using System.Text;
namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [RequireGuild]
    [Command("board")]
    [Description("shows the top 10 users/invites of the server")]
    public async Task BoardAsync(SlashCommandContext ctx, [Description("the type can be invites or level")] BoardType type = BoardType.Level)
    {
        await ctx.Interaction.DeferAsync();

        var users = type == BoardType.Level ? await Mongo.ServerMembers.GetAsync(a =>
                                                  a.Limit(10).OrderByDescending(b => b.Level.Points)) : await Mongo.ServerMembers.GetAsync(a =>
                                                                                                            a.Limit(10).OrderByDescending(b => b.Invites.Uses));

        // if not any users, return
        if (users.Count == 0)
        {
            await ctx.TryEditResponseAsync($"-# {HubEmoji.GhostLove} » No users found");
            return;
        }

        // create string builder
        var content = new StringBuilder()
            .Append($"**Top `10` / `{ctx.Guild.MemberCount:N0}` users**\n\n");

        // illiterate users
        for (var i = 0; i < users.Count; i++)
            if (type == BoardType.Level)
                content.Append($"{HubEmoji.WhiteHeart?.ToString()} {i + 1} » **{users[i].Member.Username}** with `{users[i].Level.Points:N0}` points\n");
            else
                content.Append($"{HubEmoji.WhiteHeart?.ToString()} {i + 1} » **{users[i].Member.Username}** with `{users[i].Invites.Uses:N0}` invites\n");

        // send the message
        await ctx.TryEditResponseAsync(content.ToString());
    }
}