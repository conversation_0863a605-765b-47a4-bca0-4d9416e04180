namespace Dolo.Core.Interceptor.Interfaces;

using System.Text;

public interface IHttpInterceptedMessage
{
    Dictionary<string, string[]> Headers { get; }
    byte[]? Content { get; }
    string? ContentType { get; }
    long? ContentLength { get; }
    bool IsAmf { get; }
    IAmfInterceptedData? Amf { get; }
    string Method { get; }
    Uri? Uri { get; }

    string GetContentAsString(Encoding? encoding = null);
    T? GetContentAsJson<T>() where T : class;
    string GetHeaderValue(string name);
    string[] GetHeaderValues(string name);
}
