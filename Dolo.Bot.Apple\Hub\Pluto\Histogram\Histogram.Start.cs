﻿using System.Diagnostics;
namespace Dolo.Bot.Apple.Hub.Pluto.Histogram;

public partial class Histogram
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("start")]

[Description("start the histogram machine")]
    public async Task StartHistogramAsync(SlashCommandContext ctx, [Description("how many histograms should be generated")] long count = 30)
    {
        await ctx.Interaction.DeferAsync();

        var app = "Dolo.Planet.Histogram.exe";
        Process.Start(new ProcessStartInfo(app)
        {
            FileName = app,
            UseShellExecute = false,
            RedirectStandardOutput = true,
            CreateNoWindow = false,
            WorkingDirectory = Path.GetDirectoryName(app),
            Arguments = $"-count={count}"
        });

        await ctx.TryEditResponseAsync($"Started histogram machine with {count} histograms");
    }
}