﻿// ReSharper disable ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
using Dolo.Core.Consola;
namespace Dolo.Bot.Apple.Hub.System;

public static class InviteSystem
{
    public static Task StartAsync()
        => Task.Factory.StartNew(async () => {
            Consola.Information("Invite system started");

            // we are going to get all the invites in the guild and store them in the cache
            var invites = await Hub.Guild!.GetInvitesAsync();

            // we are going to store the invites in the cache
            invites.ToList()
                .ForEach(invite => HubCache.TryAddInvite(invite.Code, invite));

            // we are going to start the invite system
            await Hub.MemberSearchSystem.StartAsync();
        });
}