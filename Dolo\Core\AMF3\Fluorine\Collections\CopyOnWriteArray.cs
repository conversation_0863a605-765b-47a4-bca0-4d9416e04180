﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     A thread-safe variant of ArrayList in which all mutative operations (add, set, and so on) are implemented by making
///     a fresh
///     copy of the underlying array.
///     This is ordinarily too costly, but may be more efficient than alternatives when traversal operations vastly
///     outnumber
///     mutations, and is useful when you cannot or don't want to synchronize traversals, yet need to preclude interference
///     among
///     concurrent threads.  The "snapshot" style iterator method uses a reference to the state of the array at the point
///     that the iterator
///     was created. This array never changes during the lifetime of the iterator, so interference is impossible.
///     The iterator will not reflect additions, removals, or changes to the list since the iterator was created.
/// </summary>
internal class CopyOnWriteArray : IList, ICollection, IEnumerable
{
    private static readonly object _objLock = new();
    private object[] _array;

    /// <summary>
    ///     Initializes a new instance of the CopyOnWriteArray class.
    /// </summary>
    public CopyOnWriteArray()
        => _array = new object[0];

    /// <summary>
    ///     Creates a CopyOnWriteArray wrapper for a specific IList.
    /// </summary>
    /// <param name="list">The IList to wrap.</param>
    public CopyOnWriteArray(IList list)
    {
        if (list == null)
            throw new ArgumentNullException("list");
        _array = new object[list.Count];
        var i = 0;
        foreach (var element in list)
            _array[i++] = element;
    }

    #region IEnumerable Members

    /// <summary>
    ///     Returns an enumerator that iterates through an CopyOnWriteArray.
    /// </summary>
    /// <returns>An IEnumerator object that can be used to iterate through the collection.</returns>
    public IEnumerator GetEnumerator()
        => _array.GetEnumerator();

    #endregion

    private void Copy(object[] src, int offset, int count)
    {
        lock (SyncRoot)
        {
            _array = new object[count];
            Array.Copy(src, offset, _array, 0, count);
        }
    }

    /// <summary>
    ///     Static version allows repeated call without needed to grab lock for array each time.
    /// </summary>
    /// <param name="elem"></param>
    /// <param name="elementData"></param>
    /// <param name="length"></param>
    /// <returns></returns>
    private static int IndexOf(object elem, object[] elementData, int length)
    {
        if (elem == null)
        {
            for (var i = 0; i < length; i++)
                if (elementData[i] == null)
                    return i;
        }
        else
            for (var i = 0; i < length; i++)
                if (elem.Equals(elementData[i]))
                    return i;

        return -1;
    }


    #region IList Members

    /// <summary>
    ///     Adds an object to the end of the CopyOnWriteArray.
    /// </summary>
    /// <param name="value">The Object to add to the CopyOnWriteArray.</param>
    /// <returns>The position into which the new element was inserted.</returns>
    public int Add(object value)
    {
        lock (SyncRoot)
        {
            var len = _array.Length;
            var newArray = new object[len + 1];
            Array.Copy(_array, 0, newArray, 0, len);
            newArray[len] = value;
            _array = newArray;
            return len;
        }
    }

    /// <summary>
    ///     Removes all elements from the CopyOnWriteArray.
    /// </summary>
    public void Clear()
    {
        lock (SyncRoot) _array = new object[0];
    }

    /// <summary>
    ///     Determines whether the CopyOnWriteArray contains a specific value.
    /// </summary>
    /// <param name="value">The Object to locate in the CopyOnWriteArray.</param>
    /// <returns>true if the Object is found in the CopyOnWriteArray; otherwise, false.</returns>
    public bool Contains(object value)
        => IndexOf(value) > -1;

    /// <summary>
    ///     Determines the index of a specific item in the CopyOnWriteArray.
    /// </summary>
    /// <param name="value">The Object to locate in the CopyOnWriteArray.</param>
    /// <returns>The index of value if found in the CopyOnWriteArray; otherwise, -1.</returns>
    public int IndexOf(object value)
    {
        var elementData = _array;
        var length = elementData.Length;
        return IndexOf(value, elementData, length);
    }

    /// <summary>
    ///     Inserts an element into the CopyOnWriteArray at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index at which value should be inserted.</param>
    /// <param name="value">The Object to insert into the CopyOnWriteArray.</param>
    public void Insert(int index, object value)
    {
        lock (SyncRoot)
        {
            var len = _array.Length;
            var newArray = new object[len + 1];
            Array.Copy(_array, 0, newArray, 0, index);
            newArray[index] = value;
            Array.Copy(_array, index, newArray, index + 1, len - index);
            _array = newArray;
        }
    }

    /// <summary>
    ///     Gets a value indicating whether the CopyOnWriteArray has a fixed size.
    /// </summary>
    public bool IsFixedSize
        => false;

    /// <summary>
    ///     Gets a value indicating whether the CopyOnWriteArray is read-only.
    /// </summary>
    public bool IsReadOnly
        => false;

    /// <summary>
    ///     Removes the first occurrence of a specific object from the CopyOnWriteArray.
    /// </summary>
    /// <param name="value">The Object to remove from the CopyOnWriteArray.</param>
    public void Remove(object value)
    {
        lock (SyncRoot)
        {
            var len = _array.Length;
            if (len == 0)
                return;
            // Copy while searching for element to remove
            // This wins in the normal case of element being present
            var newlen = len - 1;
            var newArray = new object[newlen];

            for (var i = 0; i < newlen; ++i)
                if (value == _array[i] || value != null && value.Equals(_array[i]))
                {
                    // found one;  copy remaining and exit
                    for (var k = i + 1; k < len; ++k)
                        newArray[k - 1] = _array[k];
                    _array = newArray;
                    return;
                }
                else
                    newArray[i] = _array[i];

            // special handling for last cell
            if (value == _array[newlen] || value != null && value.Equals(_array[newlen])) _array = newArray;
        }
    }

    /// <summary>
    ///     Removes the element at the specified index of the CopyOnWriteArray.
    /// </summary>
    /// <param name="index">The zero-based index of the item to remove.</param>
    public void RemoveAt(int index)
    {
        lock (SyncRoot)
        {
            var len = _array.Length;
            var oldValue = _array[index];
            var newArray = new object[len - 1];
            Array.Copy(_array, 0, newArray, 0, index);
            var numMoved = len - index - 1;
            if (numMoved > 0)
                Array.Copy(_array, index + 1, newArray, index, numMoved);
            _array = newArray;
        }
    }

    /// <summary>
    ///     Gets or sets the element at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the element to get or set.</param>
    /// <returns>The element at the specified index.</returns>
    public object this[int index]
    {
        get => _array[index];
        set
        {
            lock (SyncRoot)
            {
                var oldValue = _array[index];
                var isSame = oldValue == value || value != null && value.Equals(oldValue);
                if (!isSame)
                {
                    var newArray = new object[_array.Length];
                    Array.Copy(_array, 0, newArray, 0, _array.Length);
                    newArray[index] = value;
                    _array = newArray;
                }
            }
        }
    }

    #endregion

    #region ICollection Members

    /// <summary>
    ///     Copies the elements of the CopyOnWriteArray to an Array, starting at a particular Array index.
    /// </summary>
    /// <param name="array">
    ///     The one-dimensional Array that is the destination of the elements copied from CopyOnWriteArray. The
    ///     Array must have zero-based indexing.
    /// </param>
    /// <param name="index">The zero-based index in array at which copying begins.</param>
    public void CopyTo(Array array, int index)
    {
        Array.Copy(_array, 0, array, index, array.Length - index);
    }

    /// <summary>
    ///     Gets the number of elements contained in the CopyOnWriteArray.
    /// </summary>
    public int Count
        => _array.Length;

    /// <summary>
    ///     Gets a value indicating whether access to the CopyOnWriteArray is synchronized (thread safe).
    /// </summary>
    public bool IsSynchronized
        => true;

    /// <summary>
    ///     Gets an object that can be used to synchronize access to the CopyOnWriteArray.
    /// </summary>
    public object SyncRoot
        => _objLock;

    #endregion
}