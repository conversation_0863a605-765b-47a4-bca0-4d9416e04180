﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     The flex-client element of the services configuration file.
/// </summary>
internal sealed class FlexClientSettings
{
    internal FlexClientSettings()
    {}

    internal FlexClientSettings(XmlNode flexClientNode)
    {
        var timeoutNode = flexClientNode.SelectSingleNode("timeout-minutes");
        if (timeoutNode != null) TimeoutMinutes = System.Convert.ToInt32(timeoutNode.InnerXml);
    }

    /// <summary>
    ///     Gets or sets the number of minutes before an idle FlexClient is timed out.
    /// </summary>
    public int TimeoutMinutes { get; set; }
}