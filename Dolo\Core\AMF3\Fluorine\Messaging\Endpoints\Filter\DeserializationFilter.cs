﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class DeserializationFilter : AbstractFilter
{
    /// <summary>
    ///     Initializes a new instance of the DeserializationFilter class.
    /// </summary>
    public DeserializationFilter()
    {}

    public bool UseLegacyCollection
    {
        get;
        set;
    } = false;

    #region IFilter Members

    public override Task Invoke(AMFContext context)
    {
        var deserializer = new AMFDeserializer(context.InputStream);
        deserializer.UseLegacyCollection = UseLegacyCollection;
        var amfMessage = deserializer.ReadAMFMessage();
        context.AMFMessage = amfMessage;
        context.MessageOutput = new(amfMessage.Version);
        if (deserializer.FailedAMFBodies.Length > 0)
        {
            var failedAMFBodies = deserializer.FailedAMFBodies;
            //Write out failed AMFBodies
            for (var i = 0; i < failedAMFBodies.Length; i++)
                context.MessageOutput.AddBody(failedAMFBodies[i]);
        }
        return Task.FromResult<object>(null);
    }

    #endregion
}