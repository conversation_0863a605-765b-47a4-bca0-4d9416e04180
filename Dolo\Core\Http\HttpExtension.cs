﻿using System.Text.Json;
using System.Buffers;

namespace Dolo.Core.Http;

public static class HttpExtension {

    /// <summary>
    ///     Clones a HttpRequestMessage
    /// </summary>
    /// <param name="req"></param>
    /// <returns></returns>
    public static HttpRequestMessage Clone(this HttpRequestMessage req)
    {
        var clone = new HttpRequestMessage(req.Method, req.RequestUri) {
            Content = req.Content,
            Version = req.Version,
            VersionPolicy = req.VersionPolicy
        };

        foreach (var prop in req.Options)
            clone.Options.TryAdd(prop.Key, prop.Value);

        foreach (var header in req.Headers)
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value); return clone;
    }

    /// <summary>
    ///     Reads the http-content as type-argument with enhanced performance
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="content"></param>
    /// <returns></returns>
    public static async Task<T?> ReadAsAsync<T>(this HttpContent content) {
        var json = await content.ReadAsStringAsync();

        if (string.IsNullOrEmpty(json))
            return default;

        if (IsJsonContentType(content.Headers.ContentType?.MediaType) || IsLikelyJson(json))
            return JsonConvert.DeserializeObject<T>(json);

        if (typeof(T) == typeof(string))
            return (T)(object)json;

        if (typeof(T) == typeof(byte[]))
            return (T)(object)Encoding.UTF8.GetBytes(json);

        return default;

    }

    private static bool IsJsonContentType(string? contentType) =>
        contentType?.Contains("json") == true;

    // Helper method to detect if a string looks like JSON
    private static bool IsLikelyJson(string content) {
        var trimmed = content.Trim();
        return (trimmed.StartsWith('{') && trimmed.EndsWith('}')) ||
               (trimmed.StartsWith('[') && trimmed.EndsWith(']')) ||
               trimmed.Equals("null", StringComparison.OrdinalIgnoreCase) ||
               trimmed.Equals("true", StringComparison.OrdinalIgnoreCase) ||
               trimmed.Equals("false", StringComparison.OrdinalIgnoreCase) ||
               (trimmed.StartsWith('"') && trimmed.EndsWith('"'));
    }
}
