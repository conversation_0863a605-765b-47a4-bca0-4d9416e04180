﻿using Dolo.Core.Extension;
using Dolo.Core.Http;
using Newtonsoft.Json;
using System.Drawing;
using System.Runtime.Versioning;
namespace Dolo.Flash;

public class Direct
{
    private readonly string _desktop;
    private readonly DirectoryInfo _directoryInfo;
    private readonly string _directoryName;
    private byte[]? _bytes;

    private MemoryStream? _stream;
    public Direct()
    {
        _desktop = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
        _directoryName = Path.GetRandomFileName();
        _directoryInfo = new(Path.Combine(_desktop, _directoryName));
    }

    private async Task<MemoryStream> TryGetImageAsync()
    {
        var files = GetFiles("*.png");
        var image = Path.Combine(GetPath(), files.Length > 1 ? "2.png" : "1.png");
        _bytes = await File.ReadAllBytesAsync(image);
        _stream = new(_bytes);
        return _stream;
    }

    public async Task TryRemoveBackgroundAsync()
    {
        var imageMemory = await TryGetImageAsync();
        var image = System.Convert.ToBase64String(imageMemory.ToArray());

        if (image.Length == 0) return;

        var http = await Http.TrySendAsync<ImageRemoverResult>(a =>
        {
            a.Method = HttpMethod.Post;
            a.Url = "https://bgremover.zyro.com/v1/ai/background-remover";
            a.ContentType = HttpContentType.ApplicationJson;
            a.Accept = "application/json, text/plain, */*";
            a.Content = new StringContent(JsonConvert.SerializeObject(new Dictionary<string, string>
            {
                { "image_data", image }
            }));
        });

        if (!http.IsSuccess || http.Body is null) return;

        var base64Bytes = System.Convert.FromBase64String(http.Body!.GetBase64Url()!);
        _stream = new(base64Bytes);
    }


    [SupportedOSPlatform("windows")]
    public async Task<Direct> TryApplyColorAsync()
    {
        var files = GetFiles("*.png");
        var image = Path.Combine(GetPath(), files.Length > 1 ? "2.png" : "1.png");
        _bytes = await File.ReadAllBytesAsync(image);
        _bytes = _bytes.ToTransparent(Color.FromArgb(153, 153, 204));
        _bytes = _bytes.ToTransparent(Color.FromArgb(0, 204, 153));
        _stream = new(_bytes);
        return this;
    }

    public Stream GetStream() => _stream ??= new();

    /// <summary>
    ///     Tries to export a swf file
    /// </summary>
    /// <returns></returns>
    public async Task<Direct> TryExportAsync(Uri uri)
    {
        await JPEX.ExportAsync(GetPath(), Path.Combine(GetPath(), uri.Segments.Last()));
        return this;
    }

    /// <summary>
    ///     Tries to download a file from the given Uri
    /// </summary>
    /// <param name="uri"></param>
    /// <returns></returns>
    public async Task<Stream?> TryDownloadAsync(Uri uri)
    {
        var download = await Http.TryDownloadAsync(a =>
        {
            a.Url = uri.ToString();
        });

        return download.Stream;
    }

    /// <summary>
    ///   Tries to convert the stream to a file
    /// </summary>
    /// <param name="stream"></param>
    /// <param name="filePath"></param>
    /// <returns></returns>
    public bool TryConvertStreamToFile(Stream stream, string filePath)
    {
        try
        {
            using var fileStream = File.Create(filePath);
            stream.CopyTo(fileStream);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     Gets the full path of the Directory
    /// </summary>
    /// <returns></returns>
    public string GetPath() => _directoryInfo.FullName;

    /// <summary>
    ///    Gets the full path of the file
    /// </summary>
    public string GetFilePath(Uri uri) => Path.Combine(GetPath(), uri.Segments.Last());

    /// <summary>
    ///     Gets all the files in the Directory
    /// </summary>
    /// <returns></returns>
    public FileInfo[] GetFiles() => _directoryInfo.GetFiles();

    /// <summary>
    ///     Gets all the files in the Directory
    /// </summary>
    /// <returns></returns>
    public FileInfo[] GetFiles(string searchPattern) => _directoryInfo.GetFiles(searchPattern);

    /// <summary>
    ///     Create a new Directory using a random name
    /// </summary>
    /// <returns></returns>
    public Direct Create()
    {
        Directory.CreateDirectory(_directoryInfo.FullName);
        return this;
    }

    /// <summary>
    ///     Delete the Directory and all its contents
    /// </summary>
    /// <returns></returns>
    public Direct Delete()
    {
        Directory.Delete(_directoryInfo.FullName, true);
        return this;
    }

    /// <summary>
    ///     Place a file in the Directory
    /// </summary>
    /// <param name="path">the file path</param>
    /// <returns></returns>
    private Direct Place(string path)
    {
        File.Copy(path, Path.Combine(_directoryInfo.FullName, Path.GetFileName(path)));
        return this;
    }

    /// <summary>
    ///     Place multiple files in the Directory
    /// </summary>
    /// <param name="paths">all the file paths</param>
    /// <returns></returns>
    public Direct Place(params string[] paths)
    {
        foreach (var path in paths)
            Place(path);

        return this;
    }

    private class ImageRemoverResult
    {
        [JsonProperty("result", NullValueHandling = NullValueHandling.Ignore)]
        public string? Image { get; set; }
        public string? GetBase64Url() => Image?.Split(",").Last();
    }
}