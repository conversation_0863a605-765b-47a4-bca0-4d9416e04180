using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// Simple console logger for internal use
/// </summary>
public sealed class ConsoleLogger<T> : ILogger<T>
{
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

    public bool IsEnabled(LogLevel logLevel) => logLevel >= LogLevel.Information;

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        if (!IsEnabled(logLevel)) return;

        var message = formatter(state, exception);
        var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
        var level = logLevel switch
        {
            LogLevel.Debug => "DBG",
            LogLevel.Information => "INF",
            LogLevel.Warning => "WRN",
            LogLevel.Error => "ERR",
            LogLevel.Critical => "CRT",
            _ => "LOG"
        };

        Console.WriteLine($"[{timestamp}] {level} [{typeof(T).Name}] {message}");
        
        if (exception != null)
        {
            Console.WriteLine($"[{timestamp}] {level} [{typeof(T).Name}] Exception: {exception}");
        }
    }
}
