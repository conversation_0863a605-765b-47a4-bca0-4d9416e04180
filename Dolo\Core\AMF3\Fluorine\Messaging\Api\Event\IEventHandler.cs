﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Event;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal interface IEventHandler
{
    /// <summary>
    ///     Handle an event.
    /// </summary>
    /// <param name="evt">Event to handle.</param>
    /// <returns>true if event was handled, false if it should bubble.</returns>
    bool HandleEvent(IEvent evt);
}