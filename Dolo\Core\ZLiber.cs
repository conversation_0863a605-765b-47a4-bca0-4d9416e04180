﻿using SharpCompress.Compressors;
using SharpCompress.Compressors.Deflate;
namespace Dolo.Core;

public static class <PERSON><PERSON><PERSON>
{
    /// <summary>
    ///     Compress byte array into a stream using zlib
    /// </summary>
    public static async Task CompressAsync(byte[] data, Stream output, CompressionLevel level = CompressionLevel.BestCompression)
    {
        await using var compressor = new DeflateStream(output, CompressionMode.Compress, level);
        await compressor.WriteAsync(data);
    }

    /// <summary>
    ///     Compress byte array into a file using zlib
    /// </summary>
    public static async Task CompressAsync(byte[] data, string path, CompressionLevel level = CompressionLevel.BestCompression)
    {
        await using var file = File.OpenWrite(path);
        await CompressAsync(data, file, level);
    }

    /// <summary>
    ///     Compress a stream into a stream using zlib
    /// </summary>
    /// asdasdadadsa
    /// assfsdssdasdas
    public static async Task CompressAsync(Stream input, Stream output, CompressionLevel level = CompressionLevel.BestCompression)
    {
        await using var compressor = new DeflateStream(output, CompressionMode.Compress, level);
        await input.CopyToAsync(compressor);
    }

    /// <summary>
    ///     Compress a stream into a file using zlib
    /// </summary>
    public static async Task CompressAsync(Stream input, string path, CompressionLevel level = CompressionLevel.BestCompression)
    {
        await using var file = File.OpenWrite(path);
        await CompressAsync(input, file, level);
    }

    /// <summary>
    ///     Compress a file into a stream using zlib
    /// </summary>
    public static async Task CompressAsync(string path, Stream output, CompressionLevel level = CompressionLevel.BestCompression)
    {
        await using var file = File.OpenRead(path);
        await CompressAsync(file, output, level);
    }

    /// <summary>
    ///     Decompress byte array into a stream using zlib
    /// </summary>
    public static async Task DecompressAsync(byte[] data, Stream output)
    {
        await using var decompressor = new DeflateStream(output, CompressionMode.Decompress);
        await decompressor.WriteAsync(data);
    }

    /// <summary>
    ///     Decompress byte array into a file using zlib
    /// </summary>
    public static async Task DecompressAsync(byte[] data, string path)
    {
        await using var file = File.OpenWrite(path);
        await DecompressAsync(data, file);
    }

    /// <summary>
    ///     Decompress a stream into a stream using zlib
    /// </summary>
    public static async Task DecompressAsync(Stream input, Stream output)
    {
        await using var decompressor = new DeflateStream(output, CompressionMode.Decompress);
        await input.CopyToAsync(decompressor);
    }

    /// <summary>
    ///     Decompress a stream into a file using zlib
    /// </summary>
    public static async Task DecompressAsync(Stream input, string path)
    {
        await using var file = File.OpenWrite(path);
        await DecompressAsync(input, file);
    }

    /// <summary>
    ///     Decompress a file into a stream using zlib
    /// </summary>
    public static async Task DecompressAsync(string path, Stream output)
    {
        await using var file = File.OpenRead(path);
        await DecompressAsync(file, output);
    }
}