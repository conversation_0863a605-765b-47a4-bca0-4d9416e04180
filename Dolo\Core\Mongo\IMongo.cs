﻿namespace Dolo.Core.Mongo;

public interface IMongo<T>
{
    Task<List<T>> GetAsync(FilterDefinition<T> filterDefinition);
    Task<List<T>> GetAsync(Expression<Func<T, bool>> filter);
    Task<List<T>> GetAsync();
    Task<List<T>> GetAsync(Action<MongoFilter<T>> options);
    Task<List<T>> GetAsync(FilterDefinition<T> filterDefinition, MongoFilter<T> options);
    Task<List<T>> GetAsync(Expression<Func<T, bool>> filter, MongoFilter<T> options);

    Task<T?> GetOneAsync(FilterDefinition<T> filterDefinition);
    Task<T?> GetOneAsync(Expression<Func<T, bool>> filter);

    Task<T?> GetFirstAsync();

    Task<long> CountAsync(FilterDefinition<T> filterDefinition);
    Task<long> CountAsync(Expression<Func<T, bool>> filter);
    Task<long> CountAsync();

    Task DeleteAsync(FilterDefinition<T> filterDefinition);
    Task DeleteAsync(Expression<Func<T, bool>> filter);
    Task DeleteManyAsync(FilterDefinition<T> filterDefinition);
    Task DeleteManyAsync(Expression<Func<T, bool>> filter);

    Task<T?> UpdateAsync(FilterDefinition<T> filterDefinition, UpdateDefinition<T> updateDefinition, FindOneAndUpdateOptions<T>? options);
    Task<UpdateResult> UpdateManyAsync(FilterDefinition<T> filterDefinition, UpdateDefinition<T> updateDefinition);

    Task AddAsync(T entity);
    Task AddAsync(IEnumerable<T> entities);

    Task ClearAsync();

    Task<List<T>> DistinctAsync(Expression<Func<T, T>> field, FilterDefinition<T>? filter = null);
    Task<List<T>> DistinctAsync(Expression<Func<T, T>> field, string filter);

    Task<T?> UpdateAsync(UpdateDefinition<T> updateDefinition);
    Task ReplaceAsync(FilterDefinition<T> filterDefinition, T document);
}
