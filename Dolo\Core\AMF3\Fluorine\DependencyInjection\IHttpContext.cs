﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.DependencyInjection;

internal interface IHttpContext
{
    Stream GetInputStream();
    Stream GetOutputStream();
    void Clear(object context);
    string GetContentType();
    void SetContentType(string contentType);
    void Finish(object context);
}