﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("set")]
    [Description("set points to a member")]
    public async Task SetAsync(SlashCommandContext ctx, [Description("the user to set the points to")] DiscordUser user, [Description("the points to set")] long points)
    {
        await ctx.Interaction.DeferAsync(true);

        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);
        if (member is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.GhostLove} » User not found.");
            return;
        }

        member.Level.Points = points.To<int>();
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, user.Id), Builders<ServerMember>.Update.Set(a => a.Level.Points, points));
        await ctx.TryEditResponseAsync($"{HubEmoji.GhostLove} » {member.Member.Username} now has `{points:N0}` points.");

    }
}