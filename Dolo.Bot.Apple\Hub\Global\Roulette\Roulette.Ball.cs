﻿namespace Dolo.Bot.Apple.Hub.Global.Roulette;

public partial class Roulette
{
    [Command("8ball")]

[Description("roulette an 8ball")]
    public async Task Roulette8BallAsync(SlashCommandContext ctx, [Description("roulette 8ball message")] string message)
    {
        await ctx.Interaction.DeferAsync();

        string[] answer = ["Yes", "No", "Absolutely", "Absolutely not", "Maybe", "I don't know", "I don't think so", "ask again later"];

        // send response to server
        await ctx.TryEditResponseAsync(string.Format($"> {HubEmoji.RosePulse} **{{0}}**, {{1}} \n*{{2}}*", ctx.Member.Username, message, answer[RandomNumberGenerator.GetInt32(0, answer.Length)]));
    }
}