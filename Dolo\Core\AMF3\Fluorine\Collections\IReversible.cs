﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     Reversible container.
/// </summary>
/// <remarks>
///     Reversible container can be traversed from end to beginning.
/// </remarks>
internal interface IReversible : IEnumerable
{
    /// <summary>
    ///     Gets enumerable that traverses the container in reversed order.
    /// </summary>
    /// <example>
    ///     IReversible container = ...;
    ///     foreach (object obj in container.Reversed) ...;
    /// </example>
    IEnumerable Reversed { get; }
}