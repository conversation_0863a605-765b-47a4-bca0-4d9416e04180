﻿using Dolo.Core.Discord;
using Dolo.Core.Extension;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Embed
    {
        [Command("set")]
        [Description("set the embed color")]
        public async Task AddAsync(SlashCommandContext ctx,
            [Description("the the embed color even in hex or decimal")] string? embed)
        {
            if (ctx.User.Id != 440584675740876810) return;

            if (string.IsNullOrEmpty(embed) || !embed.IsValidHexColor())
            {
                await ctx.TryCreateResponseAsync($"You specified an invalid color `{embed}`");
                return;
            }

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set("EmbedColor", embed));

            await ctx.TryCreateResponseAsync($"Embed color to `{embed}`");
        }
    }
}