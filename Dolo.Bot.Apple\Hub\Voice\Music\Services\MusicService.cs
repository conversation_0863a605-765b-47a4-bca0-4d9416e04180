using System.Collections;
using System.Collections.Concurrent;
using System.Diagnostics;
using Dolo.Bot.Apple.Hub.Voice.Music.Entities;
using DSharpPlus.VoiceNext;

namespace Dolo.Bot.Apple.Hub.Voice.Music.Services;

public static class MusicService
{
    private static readonly ConcurrentDictionary<ulong, MusicPlayer> _players = new();
    private static bool? _toolsAvailable = null; // Cache tool availability
    private static DateTime _lastToolCheck = DateTime.MinValue;

    public static async Task<MusicPlayer?> GetOrCreatePlayerAsync(ulong guildId, ulong voiceChannelId, ulong textChannelId)
    {
        var player = _players.GetOrAdd(guildId, _ => new MusicPlayer(guildId, voiceChannelId, textChannelId));

        if (!player.IsConnected)
        {
            try
            {
                var guild = Hub.Discord.Guilds[guildId];
                var channel = guild.Channels[voiceChannelId];

                // Use the correct VoiceNext API - ConnectAsync directly on the channel
                player.Connection = await channel.ConnectAsync();
                player.ChannelId = voiceChannelId;
                player.TextChannelId = textChannelId;

                Console.WriteLine($"[Music] Connected to voice channel {channel.Name} in {guild.Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Music] Failed to connect to voice channel: {ex.Message}");
                return null;
            }
        }

        player.UpdateActivity();
        return player;
    }

    public static MusicPlayer? GetPlayer(ulong guildId)
    {
        return _players.TryGetValue(guildId, out var player) ? player : null;
    }

    public static async Task<bool> DisconnectPlayerAsync(ulong guildId)
    {
        if (_players.TryRemove(guildId, out var player))
        {
            await player.DisconnectAsync();
            player.Dispose();
            Console.WriteLine($"[Music] Disconnected player for guild {guildId}");
            return true;
        }
        return false;
    }

    public static async Task PlayAsync(MusicPlayer player, Track track)
    {
        if (player.Connection == null || !player.IsConnected)
        {
            Console.WriteLine("[Music] No voice connection available");
            return;
        }

        try
        {
            player.State = PlayerState.Buffering;
            player.PlaybackCancellation?.Cancel();
            player.PlaybackCancellation = new CancellationTokenSource();

            // Get audio stream and play it
            await PlayAudioAsync(player, track);

            player.State = PlayerState.Playing;
            player.UpdateActivity();

            Console.WriteLine($"[Music] Now playing: {track.FormattedTitle}");
            
            // Auto-play next track
            if (!player.PlaybackCancellation.Token.IsCancellationRequested)
            {
                await PlayNextAsync(player);
            }
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("[Music] Playback cancelled");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error during playback: {ex.Message}");
            player.State = PlayerState.Stopped;
        }
    }

    public static async Task PlayNextAsync(MusicPlayer player)
    {
        var nextTrack = player.Queue.Dequeue();
        if (nextTrack != null)
        {
            await PlayAsync(player, nextTrack);
        }
        else
        {
            player.State = PlayerState.Stopped;
            Console.WriteLine("[Music] Queue empty, playback stopped");
        }
    }

    public static void PausePlayer(MusicPlayer player)
    {
        if (player.IsPlaying)
        {
            player.PlaybackCancellation?.Cancel();
            player.State = PlayerState.Paused;
            Console.WriteLine("[Music] Playback paused");
        }
    }

    public static async Task ResumePlayerAsync(MusicPlayer player)
    {
        if (player.IsPaused && player.CurrentTrack != null)
        {
            player.State = PlayerState.Playing;
            await PlayAsync(player, player.CurrentTrack);
            Console.WriteLine("[Music] Playback resumed");
        }
    }

    public static async Task SkipTrackAsync(MusicPlayer player)
    {
        player.PlaybackCancellation?.Cancel();
        await PlayNextAsync(player);
        Console.WriteLine("[Music] Track skipped");
    }

    public static void StopPlayer(MusicPlayer player)
    {
        player.PlaybackCancellation?.Cancel();
        player.State = PlayerState.Stopped;
        player.Queue.Clear();
        Console.WriteLine("[Music] Playback stopped and queue cleared");
    }

    public static void SetVolume(MusicPlayer player, float volume)
    {
        player.Volume = Math.Clamp(volume, 0f, 1f);
        Console.WriteLine($"[Music] Volume set to {player.Volume * 100:F0}%");
    }

    public static Dictionary<ulong, MusicPlayer> GetAllPlayers()
    {
        return _players.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    private static async Task PlayAudioAsync(MusicPlayer player, Track track)
    {
        try
        {
            // Configure bot to not listen to its own audio output (prevent feedback)
            if (AudioConfig.MuteHeadphones)
            {
                await ConfigureBotAudioAsync(player);
            }

            // Use the tools we know work
            var ytDlpCmd = "yt-dlp.exe";
            var ffmpegCmd = @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links\ffmpeg.exe";

            Console.WriteLine($"[Music] Using yt-dlp: {ytDlpCmd}");
            Console.WriteLine($"[Music] Using ffmpeg: {ffmpegCmd}");

            // First, get the direct audio URL
            Console.WriteLine($"[Music] Track URL being processed: {track.Url}");
            Console.WriteLine($"[Music] Track Title: {track.FormattedTitle}");
            Console.WriteLine($"[Music] Track Thumbnail: {track.Thumbnail}");

            var audioUrl = await GetAudioUrlAsync(ytDlpCmd, track.Url);
            if (string.IsNullOrEmpty(audioUrl))
            {
                Console.WriteLine("[Music] Failed to get audio URL");
                return;
            }

            Console.WriteLine($"[Music] Got audio URL: {audioUrl}");

            // Now stream the audio using FFmpeg
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = ffmpegCmd,
                    Arguments = $"-i \"{audioUrl}\" -ac 2 -f s16le -ar 48000 -loglevel quiet pipe:1",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            // Get transmit sink for audio
            var transmit = player.Connection!.GetTransmitSink();

            Console.WriteLine($"[Music] Starting FFmpeg stream for: {track.FormattedTitle}");
            Console.WriteLine($"[Music] FFmpeg command: {ffmpegCmd} -i \"{audioUrl}\" -ac 2 -f s16le -ar 48000 -loglevel quiet pipe:1");

            process.Start();

            // Read any error output
            var errorTask = process.StandardError.ReadToEndAsync();

            // Stream audio data with optimized buffer
            var buffer = new byte[AudioConfig.BufferSize];
            var pcmStream = process.StandardOutput.BaseStream;
            var totalBytesRead = 0;

            while (!player.PlaybackCancellation!.Token.IsCancellationRequested && !process.HasExited)
            {
                var bytesRead = await pcmStream.ReadAsync(buffer.AsMemory(0, buffer.Length), player.PlaybackCancellation.Token);
                if (bytesRead == 0) break;

                totalBytesRead += bytesRead;
                await transmit.WriteAsync(buffer.AsMemory(0, bytesRead), player.PlaybackCancellation.Token);
            }

            if (!process.HasExited)
            {
                process.Kill();
                await process.WaitForExitAsync();
            }

            var errorOutput = await errorTask;
            if (!string.IsNullOrEmpty(errorOutput))
            {
                Console.WriteLine($"[Music] Process errors: {errorOutput}");
            }

            Console.WriteLine($"[Music] Finished playing: {track.FormattedTitle} (streamed {totalBytesRead} bytes)");
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("[Music] Audio playback cancelled");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error playing audio: {ex.Message}");
        }
    }

    private static async Task ConfigureBotAudioAsync(MusicPlayer player)
    {
        try
        {
            // Configure the bot's voice connection for optimal music playback
            // The bot should only transmit audio, not process incoming audio
            if (player.Connection != null)
            {
                // In DSharpPlus VoiceNext, the bot automatically handles this
                // The receive sink is separate from the transmit sink
                // By only using the transmit sink, we effectively "mute the bot's headphones"

                // This prevents:
                // 1. Audio feedback loops
                // 2. Unnecessary processing of incoming voice data
                // 3. Performance issues from handling multiple audio streams

                Console.WriteLine("[Music] Bot audio optimized - only transmitting, not receiving");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Could not configure bot audio: {ex.Message}");
        }

        // Small delay to ensure configuration is applied
        await Task.Delay(50);
    }

    private static async Task<bool> CheckToolsAvailableAsync()
    {
        // Cache tool availability for 5 minutes to improve performance
        if (_toolsAvailable.HasValue && DateTime.UtcNow - _lastToolCheck < TimeSpan.FromMinutes(5))
        {
            Console.WriteLine($"[Music] Using cached tool availability: {_toolsAvailable.Value}");
            return _toolsAvailable.Value;
        }

        Console.WriteLine("[Music] Checking tool availability...");
        _lastToolCheck = DateTime.UtcNow;

        try
        {
            // Check yt-dlp (try both yt-dlp and yt-dlp.exe)
            var ytDlpPaths = new[] { "yt-dlp", "yt-dlp.exe", ".\\yt-dlp.exe" };
            var ytDlpFound = false;

            foreach (var ytDlpPath in ytDlpPaths)
            {
                try
                {
                    var ytDlpCheck = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = ytDlpPath,
                            Arguments = "--version",
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            UseShellExecute = false,
                            CreateNoWindow = true
                        }
                    };

                    ytDlpCheck.Start();
                    await ytDlpCheck.WaitForExitAsync();

                    if (ytDlpCheck.ExitCode == 0)
                    {
                        Console.WriteLine($"[Music] Found yt-dlp at: {ytDlpPath}");
                        ytDlpFound = true;
                        break;
                    }
                }
                catch
                {
                    // Try next path
                }
            }

            if (!ytDlpFound)
            {
                Console.WriteLine("[Music] yt-dlp not found in PATH or current directory");
                return false;
            }

            // Check ffmpeg - prioritize the WinGet Links path that we know works
            var ffmpegPaths = new[]
            {
                @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links\ffmpeg.exe", // WinGet Links (priority)
                "ffmpeg",
                "ffmpeg.exe",
                ".\\ffmpeg.exe",
                @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin\ffmpeg.exe",
                @"C:\ProgramData\chocolatey\bin\ffmpeg.exe",
                @"C:\ffmpeg\bin\ffmpeg.exe"
            };
            var ffmpegFound = false;

            foreach (var ffmpegPath in ffmpegPaths)
            {
                try
                {
                    var ffmpegCheck = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = ffmpegPath,
                            Arguments = "-version",
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            UseShellExecute = false,
                            CreateNoWindow = true
                        }
                    };

                    ffmpegCheck.Start();
                    await ffmpegCheck.WaitForExitAsync();

                    if (ffmpegCheck.ExitCode == 0)
                    {
                        Console.WriteLine($"[Music] Found ffmpeg at: {ffmpegPath}");
                        ffmpegFound = true;
                        break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[Music] Could not check ffmpeg at {ffmpegPath}: {ex.Message}");
                }
            }

            if (!ffmpegFound)
            {
                Console.WriteLine("[Music] ffmpeg not found in PATH or current directory");
                Console.WriteLine("[Music] Try: winget install ffmpeg or download from https://ffmpeg.org/");
                return false;
            }

            Console.WriteLine("[Music] Both yt-dlp and ffmpeg are available");
            _toolsAvailable = true;
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error checking tools: {ex.Message}");
            _toolsAvailable = false;
            return false;
        }
    }

    private static async Task<string?> GetAudioUrlAsync(string ytDlpCmd, string videoUrl)
    {
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = ytDlpCmd,
                    Arguments = $"-f bestaudio --get-url \"{videoUrl}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                Console.WriteLine($"[Music] yt-dlp failed to get audio URL: {error}");
                return null;
            }

            var audioUrl = output.Trim();
            if (string.IsNullOrEmpty(audioUrl))
            {
                Console.WriteLine("[Music] yt-dlp returned empty audio URL");
                return null;
            }

            return audioUrl;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error getting audio URL: {ex.Message}");
            return null;
        }
    }

    private static async Task<string?> GetWorkingYtDlpPathAsync()
    {
        var ytDlpPaths = new[] { "yt-dlp", "yt-dlp.exe", ".\\yt-dlp.exe" };

        foreach (var path in ytDlpPaths)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = path,
                        Arguments = "--version",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                    return path;
            }
            catch { }
        }

        return null;
    }

    private static async Task<string?> GetWorkingFfmpegPathAsync()
    {
        // First try using cmd with explicit shell execution
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "cmd",
                    Arguments = "/C ffmpeg -version",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    // Explicitly inherit environment variables
                    EnvironmentVariables = { ["PATH"] = Environment.GetEnvironmentVariable("PATH") ?? "" }
                }
            };

            // Copy all environment variables to ensure PATH is properly inherited
            foreach (DictionaryEntry env in Environment.GetEnvironmentVariables())
            {
                if (env.Key != null && env.Value != null)
                {
                    process.StartInfo.EnvironmentVariables[env.Key.ToString()!] = env.Value.ToString()!;
                }
            }

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync();

            Console.WriteLine($"[Music] CMD test - Exit code: {process.ExitCode}");
            if (!string.IsNullOrEmpty(error))
            {
                Console.WriteLine($"[Music] CMD error: {error.Substring(0, Math.Min(200, error.Length))}");
            }

            if (process.ExitCode == 0)
            {
                Console.WriteLine("[Music] Found ffmpeg in PATH via cmd with inherited environment");
                return "cmd-ffmpeg"; // Special marker for cmd wrapper
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] CMD test failed: {ex.Message}");
        }

        // Try PowerShell as fallback
        try
        {
            var psProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = "-Command \"ffmpeg -version\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            psProcess.Start();
            await psProcess.WaitForExitAsync();

            if (psProcess.ExitCode == 0)
            {
                Console.WriteLine("[Music] Found ffmpeg in PATH via PowerShell");
                return "ps-ffmpeg"; // Special marker for PowerShell wrapper
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] PowerShell test failed: {ex.Message}");
        }

        // Check the exact path that the test found working
        var wingetLinksPath = @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links\ffmpeg.exe";

        Console.WriteLine($"[Music] Checking WinGet Links path: {wingetLinksPath}");
        if (File.Exists(wingetLinksPath))
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = wingetLinksPath,
                        Arguments = "-version",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    Console.WriteLine($"[Music] Found ffmpeg at WinGet Links: {wingetLinksPath}");
                    return wingetLinksPath;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Music] Error testing WinGet Links ffmpeg: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine($"[Music] WinGet Links ffmpeg not found at: {wingetLinksPath}");
        }

        // Fallback to other direct paths
        var ffmpegPaths = new[]
        {
            ".\\ffmpeg.exe",
            @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin\ffmpeg.exe",
            @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe",
            @"C:\ProgramData\chocolatey\bin\ffmpeg.exe",
            @"C:\ffmpeg\bin\ffmpeg.exe",
            @"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            @"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe"
        };

        foreach (var path in ffmpegPaths)
        {
            try
            {
                Console.WriteLine($"[Music] Checking: {path} - {(File.Exists(path) ? "EXISTS" : "NOT FOUND")}");

                if (File.Exists(path))
                {
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = path,
                            Arguments = "-version",
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            UseShellExecute = false,
                            CreateNoWindow = true
                        }
                    };

                    process.Start();
                    await process.WaitForExitAsync();

                    if (process.ExitCode == 0)
                    {
                        Console.WriteLine($"[Music] Found ffmpeg at: {path}");
                        return path;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Music] Error checking {path}: {ex.Message}");
            }
        }

        // Last resort: search WinGet packages directory
        try
        {
            var wingetDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "Microsoft", "WinGet", "Packages");

            if (Directory.Exists(wingetDir))
            {
                var ffmpegDirs = Directory.GetDirectories(wingetDir, "*ffmpeg*", SearchOption.TopDirectoryOnly);

                foreach (var dir in ffmpegDirs)
                {
                    var ffmpegExes = Directory.GetFiles(dir, "ffmpeg.exe", SearchOption.AllDirectories);

                    foreach (var exe in ffmpegExes)
                    {
                        try
                        {
                            var process = new Process
                            {
                                StartInfo = new ProcessStartInfo
                                {
                                    FileName = exe,
                                    Arguments = "-version",
                                    RedirectStandardOutput = true,
                                    RedirectStandardError = true,
                                    UseShellExecute = false,
                                    CreateNoWindow = true
                                }
                            };

                            process.Start();
                            await process.WaitForExitAsync();

                            if (process.ExitCode == 0)
                            {
                                Console.WriteLine($"[Music] Found ffmpeg via WinGet search: {exe}");
                                return exe;
                            }
                        }
                        catch { }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] WinGet search failed: {ex.Message}");
        }

        return null;
    }

    public static void Cleanup()
    {
        foreach (var player in _players.Values)
        {
            _ = Task.Run(async () => await player.DisconnectAsync());
            player.Dispose();
        }
        _players.Clear();
        Console.WriteLine("[Music] All players cleaned up");
    }
}
