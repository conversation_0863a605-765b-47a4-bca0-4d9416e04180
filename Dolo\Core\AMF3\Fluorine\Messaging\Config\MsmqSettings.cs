﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Contains the properties for configuring MSQM service adapters.
///     This is the <b>msmq</b> element in the services-config.xml file.
/// </summary>
internal sealed class MsmqSettings : Hashtable
{
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string BinaryMessageFormatter = "BinaryMessageFormatter";

    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string XmlMessageFormatter = "XmlMessageFormatter";

    private MsmqSettings()
    {}

    internal MsmqSettings(XmlNode msmqDefinitionNode)
    {
        foreach (XmlNode propertyNode in msmqDefinitionNode.SelectNodes("*"))
            if (propertyNode.InnerXml != null && propertyNode.InnerXml != string.Empty)
                this[propertyNode.Name] = propertyNode.InnerXml;
            else
            {
                if (propertyNode.Attributes != null)
                    foreach (XmlAttribute attribute in propertyNode.Attributes)
                        this[propertyNode.Name + "_" + attribute.Name] = attribute.Value;
            }
    }

    /// <summary>
    ///     Gets the name of the MSMQ queue.
    /// </summary>
    public string Name
    {
        get
        {
            if (ContainsKey("name"))
                return this["name"] as string;
            return null;
        }
    }

    /// <summary>
    ///     Gets the message formatter type.
    /// </summary>
    public string Formatter
    {
        get
        {
            if (ContainsKey("formatter"))
                return this["formatter"] as string;
            return null;
        }
    }

    /// <summary>
    ///     Gets the message label.
    /// </summary>
    public string Label
    {
        get
        {
            if (ContainsKey("label"))
                return this["label"] as string;
            return null;
        }
    }
}