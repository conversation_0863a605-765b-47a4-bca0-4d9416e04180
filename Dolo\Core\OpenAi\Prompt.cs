﻿namespace Dolo.Core.OpenAi;

public class Prompt
{
    internal List<BypassAIMessages.Message> Messages;
    public Prompt()
        => Messages = new();
    public string? Token { get; set; }

    public Prompt AddMessage(string? message, Role role = Role.User)
    {
        Messages.Add(new()
        {
            Role = role.ToString(),
            Text = message
        });
        return this;
    }

    public Prompt SetToken(string? token)
    {
        Token = token;
        return this;
    }
}