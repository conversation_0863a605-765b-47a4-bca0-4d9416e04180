﻿namespace Dolo.Authentication;

using System.IO;

public class Authenticator
{
    private const string _fileName = "Authenticator.ini";
    private static readonly Dictionary<string, string?> _authenticator = new();

    private static Dictionary<string, string?> GetAuthenticator()
    {
        if (_authenticator.Count != 0)
            return _authenticator;

        // Get the directory of the executable file
        var exeDirectory = AppContext.BaseDirectory;
        var pathCombine = Path.Combine(exeDirectory, _fileName);
        Console.WriteLine($"Looking for file at: {pathCombine}"); // Log the path for debugging

        // Check if the file exists in the executable directory
        if (!File.Exists(pathCombine))
        {
            // If not found, check the Desktop directory
            var desktopPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), _fileName);
            Console.WriteLine($"File not found. Looking for file at: {desktopPath}"); // Log the path for debugging

            if (!File.Exists(desktopPath))
                throw new FileNotFoundException($"Authenticator.ini not found at {pathCombine} or {desktopPath}");

            pathCombine = desktopPath;
        }

        // Read the file and split it into lines
        var lines = File.ReadAllText(pathCombine)
            .Split(new[] { "\r\n\r\n" }, StringSplitOptions.None);

        foreach (var line in lines)
        {
            var l = line.Split(new[] { "\r\n" }, StringSplitOptions.None);
            var key = l[0];
            var value = l[1];

            var match = Regex.Match(key, @"\[(.*?)\]");
            if (match.Success)
                key = match.Groups[1].Value;

            _authenticator.Add(key, value);
        }

        return _authenticator;
    }

    public static string? GetAuthValue(string key) => GetAuthenticator()[key];
}