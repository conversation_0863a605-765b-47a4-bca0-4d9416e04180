﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Services.Remoting;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class RemotingDestination : Destination
{
    public RemotingDestination(IService service, DestinationSettings destinationSettings) : base(service, destinationSettings)
    {}
}