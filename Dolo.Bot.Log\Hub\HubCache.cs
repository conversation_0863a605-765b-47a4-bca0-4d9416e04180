﻿namespace Dolo.Bot.Log.Hub;

public static class HubCache
{
    private static readonly Dictionary<ulong, DiscordMessage> Messages = new();
    private static readonly Dictionary<ulong, List<CachedAttachment>> MessageAttachments = new();

    public class CachedAttachment
    {
        public string FileName { get; set; } = string.Empty;
        public string OriginalUrl { get; set; } = string.Empty;
        public string ProxyUrl { get; set; } = string.Empty;
        public string? ClonedUrl { get; set; } = null; // Our preserved copy
        public int Size { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public bool IsImage => ContentType.StartsWith("image/");
        public bool IsVideo => ContentType.StartsWith("video/");
        public bool IsCloned => !string.IsNullOrEmpty(ClonedUrl);
    }

    /// <summary>
    ///     Adds a message to the cache.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="message"></param>
    public static void AddMessage(ulong id, DiscordMessage message)
    {
        if (Messages.ContainsKey(id))
            Messages[id] = message;
        else
            Messages.Add(id, message);

        // Cache attachments separately for better logging
        if (message.Attachments.Any())
        {
            var attachments = message.Attachments.Select(a => new CachedAttachment
            {
                FileName = a.FileName,
                OriginalUrl = a.Url,
                ProxyUrl = a.ProxyUrl,
                Size = a.FileSize,
                Width = a.Width,
                Height = a.Height,
                ContentType = a.MediaType ?? "unknown"
            }).ToList();

            if (MessageAttachments.ContainsKey(id))
                MessageAttachments[id] = attachments;
            else
                MessageAttachments.Add(id, attachments);
        }
    }

    /// <summary>
    ///     Removes a message from the cache.
    /// </summary>
    /// <param name="id"></param>
    public static void RemoveMessage(ulong id)
    {
        if (Messages.ContainsKey(id))
            Messages.Remove(id);

        if (MessageAttachments.ContainsKey(id))
            MessageAttachments.Remove(id);
    }

    /// <summary>
    ///     Get a message from the cache.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static DiscordMessage? GetMessage(ulong id)
        => Messages.ContainsKey(id) ? Messages[id] : null;

    /// <summary>
    ///     Get cached attachments for a message.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static List<CachedAttachment> GetMessageAttachments(ulong id)
        => MessageAttachments.ContainsKey(id) ? MessageAttachments[id] : new List<CachedAttachment>();

    /// <summary>
    ///     Check if a message has cached attachments.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static bool HasAttachments(ulong id)
        => MessageAttachments.ContainsKey(id) && MessageAttachments[id].Any();

    /// <summary>
    ///     Get total number of cached attachments.
    /// </summary>
    /// <returns></returns>
    public static int GetTotalCachedAttachments()
        => MessageAttachments.Values.Sum(list => list.Count);

    /// <summary>
    ///     Get number of successfully cloned attachments.
    /// </summary>
    /// <returns></returns>
    public static int GetClonedAttachmentCount()
        => MessageAttachments.Values.SelectMany(list => list).Count(a => a.IsCloned);

    /// <summary>
    ///     Clean up old cached attachments.
    /// </summary>
    /// <param name="maxAge"></param>
    /// <returns>Number of cleaned up attachments</returns>
    public static int CleanupOldAttachments(TimeSpan maxAge)
    {
        var cutoffTime = DateTime.UtcNow - maxAge;
        var cleanedCount = 0;

        // Get messages older than cutoff (we don't have creation time, so use a simple approach)
        var messagesToRemove = Messages
            .Where(kvp => kvp.Value.CreationTimestamp < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var messageId in messagesToRemove)
        {
            if (MessageAttachments.ContainsKey(messageId))
            {
                cleanedCount += MessageAttachments[messageId].Count;
                MessageAttachments.Remove(messageId);
            }
            Messages.Remove(messageId);
        }

        return cleanedCount;
    }

    /// <summary>
    ///     Update cloned URL for an attachment.
    /// </summary>
    /// <param name="messageId"></param>
    /// <param name="originalUrl"></param>
    /// <param name="clonedUrl"></param>
    public static void UpdateClonedUrl(ulong messageId, string originalUrl, string clonedUrl)
    {
        if (MessageAttachments.TryGetValue(messageId, out var attachments))
        {
            var attachment = attachments.FirstOrDefault(a => a.OriginalUrl == originalUrl);
            if (attachment != null)
            {
                attachment.ClonedUrl = clonedUrl;
            }
        }
    }
}
