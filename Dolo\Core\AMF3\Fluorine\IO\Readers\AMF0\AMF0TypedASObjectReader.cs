﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO.Bytecode;
namespace Dolo.Core.AMF3.Fluorine.IO.Readers.AMF0;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
/// <remarks>
///     This reader is used only in case that a requested type is not found and the gateway choose to represent
///     typed objects with ActionScript typed objects.
/// </remarks>
internal class AMF0TypedASObjectReader : IReflectionOptimizer
{
    private readonly string _typeIdentifier;

    public AMF0TypedASObjectReader(string typeIdentifier)
        => _typeIdentifier = typeIdentifier;

    #region IReflectionOptimizer Members

    public object CreateInstance()
    {
        #pragma warning disable CS8603
        return default;
        #pragma warning restore CS8603
    }

    public object ReadData(AMFReader reader, ClassDefinition classDefinition)
    {
        var asObject = reader.ReadASObject();
        asObject.TypeName = _typeIdentifier;
        return asObject;
    }

    #endregion
}