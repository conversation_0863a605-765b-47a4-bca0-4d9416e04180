﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Maintenance
    {
        [Command("disable")]

[Description("disable the maintenance")]
        public async Task DisableAsync(SlashCommandContext ctx)
        {
            if (ctx.User.Id != 440584675740876810) return;

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(a => a.Maintenance, string.Empty));

            await ctx.TryCreateResponseAsync("Maintenance disabled");
        }
    }
}