using System;

namespace Dolo.Bot.Apple.Hub.Global.Interaction;

public static class Avatar
{
    public static async Task AvatarAsync(this MessageArgs e)
    {
        var content = e.Message.Content;
        var contentSplit = content.Split(" ");
        var split = contentSplit.Length > 1 ? contentSplit[1] : null;
        var mentionedUser = e.Message.MentionedUsers.FirstOrDefault();
        var id = mentionedUser?.Id ?? (ulong.TryParse(split, out var user) ? user : null);

        if (id != null)
        {
            var discordUser = await Hub.Discord!.GetUserAsync(id.GetValueOrDefault(), true);
            if (discordUser is null)
                return;

            await e.Channel.TrySendMessageAsync(discordUser.AvatarUrl);
            return;
        }

        await e.Channel.TrySendMessageAsync(e.Author.AvatarUrl);
    }
}
