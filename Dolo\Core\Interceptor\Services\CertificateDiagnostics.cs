using System.Security.Cryptography.X509Certificates;
using System.Text;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

/// <summary>
/// Provides detailed diagnostic logging for certificate creation and validation processes.
/// This helps in pinpointing issues related to chain building, trust, and caching.
/// </summary>
public static class CertificateDiagnostics
{
    /// <summary>
    /// Logs a detailed breakdown of why a certificate chain validation failed.
    /// </summary>
    public static void LogChainValidationFailure(ILogger logger, string context, string subject, X509Chain chain)
    {
        var errorBuilder = new StringBuilder();
        errorBuilder.AppendLine($"Chain validation failed for '{subject}' in context '{context}'.");

        // Log overall chain status
        foreach (var status in chain.ChainStatus)
        {
            errorBuilder.AppendLine($"  - Status: {status.Status}, Info: {status.StatusInformation.Trim()}");
        }

        // Log details of each element in the chain
        errorBuilder.AppendLine("  Chain Elements:");
        for (var i = 0; i < chain.ChainElements.Count; i++)
        {
            var element = chain.ChainElements[i];
            errorBuilder.AppendLine($"    [{i}] Subject: {element.Certificate.Subject}");
            errorBuilder.AppendLine($"        Issuer: {element.Certificate.Issuer}");
            errorBuilder.AppendLine($"        Thumbprint: {element.Certificate.Thumbprint}");
            errorBuilder.AppendLine($"        Status: {string.Join(", ", element.ChainElementStatus.Select(s => s.Status))}");
        }
        
        logger.LogWarning(errorBuilder.ToString());
    }

    /// <summary>
    /// Logs detailed information about a specific certificate.
    /// </summary>
    public static void LogCertificateDetails(ILogger logger, string role, X509Certificate2 certificate)
    {
        if (certificate == null)
        {
            logger.LogDebug("Certificate for role '{Role}' is null.", role);
            return;
        }
        
        var certInfo = new StringBuilder();
        certInfo.AppendLine($"Details for {role} certificate:");
        certInfo.AppendLine($"  - Subject: {certificate.Subject}");
        certInfo.AppendLine($"  - Issuer: {certificate.Issuer}");
        certInfo.AppendLine($"  - Thumbprint: {certificate.Thumbprint}");
        certInfo.AppendLine($"  - Serial Number: {certificate.SerialNumber}");
        certInfo.AppendLine($"  - Valid from: {certificate.NotBefore:O}");
        certInfo.AppendLine($"  - Valid to: {certificate.NotAfter:O}");
        certInfo.AppendLine($"  - Has Private Key: {certificate.HasPrivateKey}");
        
        logger.LogDebug(certInfo.ToString());
    }
}
