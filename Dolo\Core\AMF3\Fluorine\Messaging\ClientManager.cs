﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     ClientManager manages clients connected to the AMFCore server.
/// </summary>
/// <example>
///     <code lang="CS">
/// classChatAdapter : MessagingAdapter, ISessionListener
/// {
///     private Hashtable _clients;
///  
///     public ChatAdapter()
///     {
///         _clients = new Hashtable();
///         ClientManager.AddSessionCreatedListener(this);
///     }
///  
///     public void SessionCreated(IClient client)
///     {
///         lock (_clients.SyncRoot)
///         {
///             _clients.Add(client.Id, client);
///         }
///         client.AddSessionDestroyedListener(this);
///     }
///  
///     public void SessionDestroyed(IClient client)
///     {
///         lock (_clients.SyncRoot)
///         {
///             _clients.Remove(client.Id);
///         }
///     }
/// }
/// </code>
/// </example>
internal class ClientManager : IClientRegistry
{
    private static readonly object _objLock = new();
    private static Hashtable _sessionCreatedListeners = new();
    private readonly Hashtable _clients;

    private MessageBroker _messageBroker;

    private ClientManager()
    {}

    internal ClientManager(MessageBroker messageBroker)
    {
        _messageBroker = messageBroker;
        _clients = new();
    }

    internal string GetNextId()
        => Guid.NewGuid().ToString("N");

    internal void Renew(Client client, int clientLeaseTime)
    {
        lock (_objLock)
        {
            _clients[client.Id] = client;
            if (client.ClientLeaseTime < clientLeaseTime)
                client.SetClientLeaseTime(clientLeaseTime);
            if (clientLeaseTime == 0)
                client.SetClientLeaseTime(0);
        }
    }

    internal Client RemoveSubscriber(Client client)
    {
        lock (_objLock)
        {
            RemoveSubscriber(client.Id);
            return client;
        }
    }

    internal Client RemoveSubscriber(string clientId)
    {
        lock (_objLock)
        {
            var client = _clients[clientId] as Client;
            _clients.Remove(clientId);
            return client;
        }
    }

    #region IClientRegistry Members

    /// <summary>
    ///     Returns an existing client from the message header transporting the global FlexClient Id value or creates a new one
    ///     if not found.
    /// </summary>
    /// <param name="message">Message sent from client.</param>
    /// <returns>The client object.</returns>
    public IClient GetClient(IMessage message)
    {
        if (message.HeaderExists(MessageBase.FlexClientIdHeader))
        {
            var clientId = message.GetHeader(MessageBase.FlexClientIdHeader) as string;
            return GetClient(clientId);
        }

        return null;
    }

    /// <summary>
    ///     Returns an existing client from a client id or creates a new one if not found.
    /// </summary>
    /// <param name="id">The identity of the client to return.</param>
    /// <returns>The client object.</returns>
    public IClient GetClient(string id)
    {
        lock (_objLock)
        {
            if (_clients.ContainsKey(id)) return _clients[id] as Client;
            if (id == null || id == "nil" || id == string.Empty)
                id = Guid.NewGuid().ToString("N");
            var client = new Client(this, id);
            var clientLeaseTime = 1;
            Renew(client, clientLeaseTime);
            return client;
        }
    }

    /// <summary>
    ///     Check if a client with a given id exists.
    /// </summary>
    /// <param name="id">The identity of the client to check for.</param>
    /// <returns><c>true</c> if the client exists, <c>false</c> otherwise.</returns>
    public bool HasClient(string id)
    {
        if (id == null)
            return false;
        lock (_objLock) return _clients.ContainsKey(id);
    }

    /// <summary>
    ///     Returns an existing client from a client id.
    /// </summary>
    /// <param name="clientId">The identity of the client to return.</param>
    /// <returns>The client object if exists, null otherwise.</returns>
    public IClient LookupClient(string clientId)
    {
        if (clientId == null)
            return null;

        lock (_objLock)
        {
            Client client = null;
            if (_clients.Contains(clientId)) client = _clients[clientId] as Client;
            return client;
        }
    }

    #endregion
}