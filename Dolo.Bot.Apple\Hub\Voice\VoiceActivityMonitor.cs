using System.Collections.Concurrent;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Apple;

namespace Dolo.Bot.Apple.Hub.Voice;

public static class VoiceActivityMonitor
{
    // Track last activity time for each voice channel
    private static readonly ConcurrentDictionary<ulong, DateTime> _lastActivity = new();
    
    // Track channel statistics
    private static readonly ConcurrentDictionary<ulong, ChannelStats> _channelStats = new();
    
    // Configuration
    private static readonly TimeSpan InactiveChannelThreshold = TimeSpan.FromHours(24); // 24 hours of inactivity
    private static readonly TimeSpan CleanupInterval = TimeSpan.FromHours(1); // Check every hour
    
    private static Timer? _cleanupTimer;
    private static bool _isInitialized = false;

    public class ChannelStats
    {
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;
        public int TotalJoins { get; set; } = 0;
        public int TotalMessages { get; set; } = 0;
        public TimeSpan TotalActiveTime { get; set; } = TimeSpan.Zero;
        public int MaxConcurrentUsers { get; set; } = 0;
        public List<ulong> UniqueUsers { get; set; } = new();
    }

    /// <summary>
    /// Initialize the activity monitor
    /// </summary>
    public static void Initialize()
    {
        if (_isInitialized)
            return;

        _cleanupTimer = new Timer(async _ => await PerformCleanupAsync(), null, CleanupInterval, CleanupInterval);
        _isInitialized = true;
        
        Console.WriteLine("[Voice Activity] Monitor initialized");
    }

    /// <summary>
    /// Record user activity in a voice channel
    /// </summary>
    public static void RecordActivity(ulong channelId, ulong userId, ActivityType activityType)
    {
        var now = DateTime.UtcNow;
        _lastActivity[channelId] = now;

        if (!_channelStats.TryGetValue(channelId, out var stats))
        {
            stats = new ChannelStats();
            _channelStats[channelId] = stats;
        }

        stats.LastActivity = now;

        switch (activityType)
        {
            case ActivityType.UserJoined:
                stats.TotalJoins++;
                if (!stats.UniqueUsers.Contains(userId))
                    stats.UniqueUsers.Add(userId);
                break;
            case ActivityType.MessageSent:
                stats.TotalMessages++;
                break;
        }

        // Update max concurrent users if this is a join event
        if (activityType == ActivityType.UserJoined && Hub.Guild != null)
        {
            var channel = Hub.Guild.TryGetChannel(channelId);
            if (channel != null)
            {
                var currentUsers = channel.Users.Count();
                if (currentUsers > stats.MaxConcurrentUsers)
                    stats.MaxConcurrentUsers = currentUsers;
            }
        }
    }

    /// <summary>
    /// Get statistics for a voice channel
    /// </summary>
    public static ChannelStats? GetChannelStats(ulong channelId)
    {
        return _channelStats.TryGetValue(channelId, out var stats) ? stats : null;
    }

    /// <summary>
    /// Check if a channel is considered inactive
    /// </summary>
    public static bool IsChannelInactive(ulong channelId)
    {
        if (!_lastActivity.TryGetValue(channelId, out var lastActivity))
            return true; // No activity recorded = inactive

        return DateTime.UtcNow - lastActivity > InactiveChannelThreshold;
    }

    /// <summary>
    /// Perform cleanup of inactive channels
    /// </summary>
    private static async Task PerformCleanupAsync()
    {
        try
        {
            if (Hub.Guild == null)
                return;

            var allVoiceChannels = await Mongo.Voice.GetAsync(_ => true);
            var cleanedCount = 0;

            foreach (var voiceChannel in allVoiceChannels)
            {
                // Skip if channel has users
                var channel = Hub.Guild.TryGetChannel(voiceChannel.Channel);
                if (channel?.Users.Any() == true)
                    continue;

                // Check if inactive
                if (!IsChannelInactive(voiceChannel.Channel))
                    continue;

                // Check if owner still exists in server
                var ownerExists = Hub.Guild.TryGetMember(voiceChannel.Owner, out _);
                if (!ownerExists)
                {
                    Console.WriteLine($"[Voice Activity] Cleaning up channel {voiceChannel.Channel} - owner left server");
                    await CleanupChannelAsync(voiceChannel);
                    cleanedCount++;
                    continue;
                }

                // Clean up truly inactive channels (24+ hours with no activity)
                if (IsChannelInactive(voiceChannel.Channel))
                {
                    Console.WriteLine($"[Voice Activity] Cleaning up inactive channel {voiceChannel.Channel}");
                    await CleanupChannelAsync(voiceChannel);
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0)
            {
                Console.WriteLine($"[Voice Activity] Cleaned up {cleanedCount} inactive channels");
            }

            // Clean up old statistics and activity records
            CleanupOldData();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Voice Activity] Error during cleanup: {ex.Message}");
        }
    }

    /// <summary>
    /// Clean up a specific voice channel
    /// </summary>
    private static async Task CleanupChannelAsync(VoiceUser voiceChannel)
    {
        try
        {
            // Delete voice channel
            var vc = Hub.Guild?.TryGetChannel(voiceChannel.Channel);
            if (vc != null)
                await vc.TryDeleteAsync();

            // Delete text channel
            var tc = Hub.Guild?.TryGetChannel(voiceChannel.TextChannel);
            if (tc != null)
                await tc.TryDeleteAsync();

            // Remove from database
            await Mongo.Voice.DeleteAsync(a => a.Channel == voiceChannel.Channel);

            // Clean up tracking data
            _lastActivity.TryRemove(voiceChannel.Channel, out _);
            _channelStats.TryRemove(voiceChannel.Channel, out _);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Voice Activity] Error cleaning up channel {voiceChannel.Channel}: {ex.Message}");
        }
    }

    /// <summary>
    /// Clean up old data to prevent memory leaks
    /// </summary>
    private static void CleanupOldData()
    {
        var cutoffTime = DateTime.UtcNow - TimeSpan.FromDays(7);

        var expiredActivities = _lastActivity
            .Where(kvp => kvp.Value < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();

        var expiredStats = _channelStats
            .Where(kvp => kvp.Value.LastActivity < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var channelId in expiredActivities)
            _lastActivity.TryRemove(channelId, out _);

        foreach (var channelId in expiredStats)
            _channelStats.TryRemove(channelId, out _);

        // Also clean up utilities cooldowns
        VoiceUtilities.CleanupOldCooldowns();
    }

    /// <summary>
    /// Get overall voice system statistics
    /// </summary>
    public static (int ActiveChannels, int TotalChannels, int TotalUsers) GetOverallStats()
    {
        var activeChannels = _lastActivity.Count(kvp => DateTime.UtcNow - kvp.Value <= TimeSpan.FromHours(1));
        var totalChannels = _channelStats.Count;
        var totalUsers = _channelStats.Values.SelectMany(s => s.UniqueUsers).Distinct().Count();

        return (activeChannels, totalChannels, totalUsers);
    }

    /// <summary>
    /// Dispose of the monitor
    /// </summary>
    public static void Dispose()
    {
        _cleanupTimer?.Dispose();
        _isInitialized = false;
    }
}

public enum ActivityType
{
    UserJoined,
    UserLeft,
    MessageSent,
    ChannelModified
}
