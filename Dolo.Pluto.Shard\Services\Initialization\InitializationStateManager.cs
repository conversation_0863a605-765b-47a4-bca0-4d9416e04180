namespace Dolo.Pluto.Shard.Services.Initialization;

public class InitializationStateManager : IInitializationStateManager
{
    public bool IsHidden { get; set; } = false;
    public string StatusTitle { get; set; } = "Initializing";
    public string StatusBadgeText { get; set; } = "Please wait";
    public string OperationDetail { get; set; } = "Connecting to server...";
    public string MessageArea { get; set; } = "Setting up the environment...";
    public bool IsVerifying { get; set; } = false;
    public bool ShowInitializationContent { get; set; } = true;
    public bool ShowMaintenanceContent { get; set; } = false;
    public bool ShowUpdateContent { get; set; } = false;
    public bool ShowLicenseContent { get; set; } = false;
    public bool ShowRetryButton { get; set; } = false;
    public bool ShowCloseButton { get; set; } = false;

    public event Action? OnStateChanged;

    public void ShowInitializing(string toolName)
    {
        StatusTitle = "Initializing";
        StatusBadgeText = "Please wait";
        MessageArea = $"Setting up the {toolName} environment...";
        ShowInitializationContent = true;
        ShowMaintenanceContent = false;
        ShowUpdateContent = false;
        ShowLicenseContent = false;
        ShowRetryButton = false;
        ShowCloseButton = false;
        Show();
        NotifyStateChanged();
    }

    public void ShowMaintenanceState(string message)
    {
        StatusTitle = "Maintenance";
        StatusBadgeText = "Service Unavailable";
        MessageArea = message;
        ShowInitializationContent = false;
        ShowMaintenanceContent = true;
        ShowUpdateContent = false;
        ShowLicenseContent = false;
        ShowRetryButton = true;
        ShowCloseButton = true;
        NotifyStateChanged();
    }

    public void ShowUpdateState(string message)
    {
        StatusTitle = "Update Required";
        StatusBadgeText = "Action Needed";
        MessageArea = message;
        ShowInitializationContent = false;
        ShowMaintenanceContent = false;
        ShowUpdateContent = true;
        ShowLicenseContent = false;
        ShowRetryButton = true;
        ShowCloseButton = true;
        NotifyStateChanged();
    }

    public void ShowLicenseState(string message)
    {
        StatusTitle = "License Required";
        StatusBadgeText = "Verification Needed";
        MessageArea = message;
        ShowInitializationContent = false;
        ShowMaintenanceContent = false;
        ShowUpdateContent = false;
        ShowLicenseContent = true;
        ShowRetryButton = false;
        ShowCloseButton = false;
        IsVerifying = false;
        NotifyStateChanged();
        Show();
    }

    public void ShowErrorState(string message)
    {
        StatusTitle = "Error";
        StatusBadgeText = "Action Needed";
        MessageArea = message;
        ShowInitializationContent = false;
        ShowMaintenanceContent = false;
        ShowUpdateContent = false;
        ShowLicenseContent = false;
        ShowRetryButton = true;
        ShowCloseButton = true;
        NotifyStateChanged();
    }

    public void Hide()
    {
        IsHidden = true;
        NotifyStateChanged();
    }

    public void Show()
    {
        IsHidden = false;
        NotifyStateChanged();
    }

    private void NotifyStateChanged() => OnStateChanged?.Invoke();
}
