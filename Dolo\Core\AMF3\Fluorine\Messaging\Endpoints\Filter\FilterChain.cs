﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class FilterChain
{
    private readonly IFilter _filter;

    /// <summary>
    ///     Initializes a new instance of the FilterChain class.
    /// </summary>
    /// <param name="filter"></param>
    public FilterChain(IFilter filter)
        => _filter = filter;

    public async Task InvokeFilters(AMFContext context)
    {
        var filter = _filter;
        while (filter != null)
        {
            await filter.Invoke(context);
            filter = filter.Next;
        }
    }
}