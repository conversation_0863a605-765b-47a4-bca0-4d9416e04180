﻿namespace Dolo.Bot.Apple.Hub.Handler;

public static class GuildDownloadCompleted
{
    public static async Task InvokeAsync(this GuildDownloadCompletedEventArgs e)
    {
        if (Hub.Guild is null)
            return;
        
        if (HubChannel.VoiceTopic is null)
        {
            // create the category channel
            var topic = await Hub.Guild.TryCreateCategoryAsync("voice");

            // return if null
            if (topic is null)
                return;

            // create the voice channel
            await Hub.Guild.TryCreateVoiceChannelAsync("💎 » Create Voice", topic, default, 1);
            return;
        }

        // create voice channel if topic exist and voice not exist
        if (HubChannel.Voice is null)
            await Hub.Guild.TryCreateVoiceChannelAsync("💎 » Create Voice", HubChannel.VoiceTopic, default, 1);
    }
}