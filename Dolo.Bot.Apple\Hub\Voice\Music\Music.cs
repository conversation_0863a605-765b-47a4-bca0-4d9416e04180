﻿using Dolo.Bot.Apple.Hub.Voice.Music.Services;
using Dolo.Database;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

[Command("music")]
[Description("music player commands")]
public partial class Music
{
    [Command("play")]
    [Description("play a song or add it to the queue")]
    public async Task PlayAsync(SlashCommandContext ctx,
        [Description("song name, URL, or search query")] string query)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        // Get voice channel info
        var voiceUser = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (voiceUser == null)
        {
            await ctx.TryEditResponseAsync("This text channel is not linked to a voice channel.");
            return;
        }

        // Check if user is in the voice channel
        var member = ctx.Member;
        var memberVoiceChannel = member?.VoiceState != null ? await member.VoiceState.GetChannelAsync() : null;
        if (memberVoiceChannel?.Id != voiceUser.Channel)
        {
            await ctx.TryEditResponseAsync("You must be in the voice channel to use music commands.");
            return;
        }

        try
        {
            // Check if user is in a voice channel
            if (voiceUser.Channel == 0)
            {
                await ctx.TryEditResponseAsync("You need to be in a voice channel to use this command.");
                return;
            }

            // Get or create music player
            var player = await MusicService.GetOrCreatePlayerAsync(ctx.Guild.Id, voiceUser.Channel, ctx.Channel.Id);
            if (player == null)
            {
                await ctx.TryEditResponseAsync("Failed to connect to voice channel.");
                return;
            }

            // Search for tracks
            var tracks = await SearchService.SearchAsync(query, ctx.User);
            if (tracks.Count == 0)
            {
                await ctx.TryEditResponseAsync(MusicEmbeds.Error("No results found for your search query."));
                return;
            }

            var track = tracks.First();

            // Add to queue
            if (player.IsPlaying || player.IsPaused)
            {
                player.Queue.Enqueue(track);
                await ctx.TryEditResponseAsync(MusicEmbeds.TrackAdded(track, player.Queue.Count));
            }
            else
            {
                // Start playing immediately
                player.Queue.Enqueue(track);
                var currentTrack = player.Queue.Dequeue();
                if (currentTrack != null)
                {
                    _ = Task.Run(async () => await MusicService.PlayAsync(player, currentTrack));
                    await ctx.TryEditResponseAsync(MusicEmbeds.NowPlaying(currentTrack, player));
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error in play command: {ex.Message}");
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("An error occurred while trying to play the track."));
        }
    }
}
