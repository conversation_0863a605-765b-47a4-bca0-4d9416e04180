using System;
using DSharpPlus.Entities;

namespace Dolo.Core.Discord.Entities;

public class DiscordTextChannelOption
{
    public string Name { get; set; } = string.Empty;
    public DiscordChannel? Parent { get; set; }
    public DSharpPlus.Entities.Optional<string> Topic { get; set; }
    public IEnumerable<DiscordOverwriteBuilder>? Overwrites { get; set; }
    public bool? Nsfw { get; set; }
    public DSharpPlus.Entities.Optional<int?> PerUserRateLimit { get; set; }
    public int? Position { get; set; }
    public string? Reason { get; set; }
}
