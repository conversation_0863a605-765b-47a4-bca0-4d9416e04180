using System.Net;
using System.Text;
using System.Text.RegularExpressions;

namespace Dolo.Core.Interceptor.Models;

public static partial class HttpMessageParser
{
    private static readonly Regex RequestLineRegex = MyRegex();
    private static readonly Regex ResponseLineRegex = new(@"^HTTP/(\d+\.\d+)\s+(\d+)(?:\s+(.*))?", RegexOptions.Compiled);

    public static HttpInterceptedRequestMessage? ParseRequest(byte[] requestData, string hostname, int port, string protocol = "http")
    {
        if (requestData == null || requestData.Length == 0)
            return null;

        var headerEndIndex = FindHeaderEndIndex(requestData);
        if (headerEndIndex == -1)
            return null;

        var headerBytes = requestData[..headerEndIndex];
        var requestText = Encoding.UTF8.GetString(headerBytes);
        var lines = requestText.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None);
        
        if (lines.Length == 0)
            return null;

        var requestLineMatch = RequestLineRegex.Match(lines[0]);
        if (!requestLineMatch.Success)
            return null;

        var method = requestLineMatch.Groups[1].Value;
        var path = requestLineMatch.Groups[2].Value;
        var version = Version.Parse(requestLineMatch.Groups[3].Value);

        var uriBuilder = new UriBuilder(protocol, hostname, port, path);
        
        var headers = new Dictionary<string, string[]>();
        
        for (var i = 1; i < lines.Length; i++)
        {
            if (string.IsNullOrWhiteSpace(lines[i]))
                break;

            var colonIndex = lines[i].IndexOf(':');
            if (colonIndex > 0)
            {
                var headerName = lines[i][..colonIndex].Trim();
                var headerValue = lines[i][(colonIndex + 1)..].Trim();
                
                if (headers.ContainsKey(headerName))
                {
                    var existingValues = headers[headerName];
                    var newValues = new string[existingValues.Length + 1];
                    existingValues.CopyTo(newValues, 0);
                    newValues[^1] = headerValue;
                    headers[headerName] = newValues;
                }
                else
                {
                    headers[headerName] = [headerValue];
                }
            }
        }

        byte[]? content = null;
        var bodyStartIndex = headerEndIndex + GetHeaderSeparatorLength(requestData, headerEndIndex);
        
        if (bodyStartIndex < requestData.Length)
        {
            content = requestData[bodyStartIndex..];
        }

        return new HttpInterceptedRequestMessage
        {
            Method = method,
            Uri = uriBuilder.Uri,
            Version = version,
            Headers = headers,
            Content = content
        };
    }

    public static HttpInterceptedResponseMessage? ParseResponse(byte[] responseData)
    {
        if (responseData == null || responseData.Length == 0)
            return null;

        var headerEndIndex = FindHeaderEndIndex(responseData);
        if (headerEndIndex == -1)
            return null;

        var headerBytes = responseData[..headerEndIndex];
        var responseText = Encoding.UTF8.GetString(headerBytes);
        var lines = responseText.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None);
        
        if (lines.Length == 0)
            return null;

        var statusLineMatch = ResponseLineRegex.Match(lines[0]);
        if (!statusLineMatch.Success)
            return null;

        var version = Version.Parse(statusLineMatch.Groups[1].Value);
        var statusCode = (HttpStatusCode)int.Parse(statusLineMatch.Groups[2].Value);
        var reasonPhrase = statusLineMatch.Groups[3].Value ?? string.Empty;

        var headers = new Dictionary<string, string[]>();
        
        for (var i = 1; i < lines.Length; i++)
        {
            if (string.IsNullOrWhiteSpace(lines[i]))
                break;
                
            var colonIndex = lines[i].IndexOf(':');
            if (colonIndex > 0)
            {
                var headerName = lines[i][..colonIndex].Trim();
                var headerValue = lines[i][(colonIndex + 1)..].Trim();
                
                if (headers.ContainsKey(headerName))
                {
                    var existingValues = headers[headerName];
                    var newValues = new string[existingValues.Length + 1];
                    existingValues.CopyTo(newValues, 0);
                    newValues[^1] = headerValue;
                    headers[headerName] = newValues;
                }
                else
                {
                    headers[headerName] = [headerValue];
                }
            }
        }

        byte[]? content = null;
        var bodyStartIndex = headerEndIndex + GetHeaderSeparatorLength(responseData, headerEndIndex);

        if (bodyStartIndex < responseData.Length)
        {
            var rawContent = responseData[bodyStartIndex..];

            // Check for chunked transfer encoding and decode if necessary
            if (headers.TryGetValue("Transfer-Encoding", out var transferEncoding) &&
                transferEncoding.Any(te => te.Contains("chunked", StringComparison.OrdinalIgnoreCase)))
            {
                content = DecodeChunkedContent(rawContent);
            }
            else
            {
                content = rawContent;
            }
        }

        return new HttpInterceptedResponseMessage
        {
            StatusCode = statusCode,
            ReasonPhrase = reasonPhrase,
            Version = version,
            Headers = headers,
            Content = content
        };
    }

    public static HttpInterceptedResponseMessage? ParseResponse(byte[] responseData, string method, Uri uri)
    {
        var response = ParseResponse(responseData);
        if (response is null)
            return null;

        return new HttpInterceptedResponseMessage
        {
            StatusCode = response.StatusCode,
            ReasonPhrase = response.ReasonPhrase,
            Version = response.Version,
            Headers = response.Headers,
            Content = response.Content,
            Method = method,
            Uri = uri
        };
    }

    private static int FindHeaderEndIndex(byte[] data)
    {
        for (var i = 0; i < data.Length - 3; i++)
        {
            if (data[i] == '\r' && data[i + 1] == '\n' && data[i + 2] == '\r' && data[i + 3] == '\n')
                return i;
        }
        
        for (var i = 0; i < data.Length - 1; i++)
        {
            if (data[i] == '\n' && data[i + 1] == '\n')
                return i;
        }
        
        return -1;
    }

    private static int GetHeaderSeparatorLength(byte[] data, int headerEndIndex)
    {
        if (headerEndIndex + 3 < data.Length &&
            data[headerEndIndex] == '\r' && data[headerEndIndex + 1] == '\n' &&
            data[headerEndIndex + 2] == '\r' && data[headerEndIndex + 3] == '\n')
            return 4;

        if (headerEndIndex + 1 < data.Length &&
            data[headerEndIndex] == '\n' && data[headerEndIndex + 1] == '\n')
            return 2;

        return 0;
    }

    private static byte[] DecodeChunkedContent(byte[] chunkedData)
    {
        try
        {
            using var result = new MemoryStream();
            var position = 0;

            while (position < chunkedData.Length)
            {
                // Find the end of the chunk size line
                var chunkSizeEnd = FindLineEnd(chunkedData, position);
                if (chunkSizeEnd == -1) break;

                // Extract chunk size (hex string)
                var chunkSizeLine = Encoding.UTF8.GetString(chunkedData, position, chunkSizeEnd - position);
                if (!int.TryParse(chunkSizeLine.Trim(), System.Globalization.NumberStyles.HexNumber, null, out var chunkSize))
                    break;

                // If chunk size is 0, we've reached the end
                if (chunkSize == 0) break;

                // Move past the chunk size line and CRLF
                position = chunkSizeEnd + GetLineEndLength(chunkedData, chunkSizeEnd);

                // Read the chunk data
                if (position + chunkSize <= chunkedData.Length)
                {
                    result.Write(chunkedData, position, chunkSize);
                    position += chunkSize;

                    // Skip the trailing CRLF after chunk data
                    position += GetLineEndLength(chunkedData, position);
                }
                else
                {
                    break; // Incomplete chunk
                }
            }

            return result.ToArray();
        }
        catch
        {
            // If chunked decoding fails, return original data
            return chunkedData;
        }
    }

    private static int FindLineEnd(byte[] data, int startIndex)
    {
        for (var i = startIndex; i < data.Length - 1; i++)
        {
            if (data[i] == '\r' && data[i + 1] == '\n')
                return i;
            if (data[i] == '\n')
                return i;
        }
        return -1;
    }

    private static int GetLineEndLength(byte[] data, int position)
    {
        if (position < data.Length - 1 && data[position] == '\r' && data[position + 1] == '\n')
            return 2;
        if (position < data.Length && data[position] == '\n')
            return 1;
        return 0;
    }

    [GeneratedRegex(@"^(\w+)\s+(.+?)\s+HTTP/(\d+\.\d+)", RegexOptions.Compiled)]
    private static partial Regex MyRegex();
}
