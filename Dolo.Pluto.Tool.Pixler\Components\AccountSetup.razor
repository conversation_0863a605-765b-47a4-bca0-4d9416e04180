<!-- Account Setup with Server Selection -->
<div class="mb-4">
    <div class="flex items-center mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd" />
        </svg>
        <h3 class="text-xs font-bold text-text-main font-jakarta">Account Setup</h3>
    </div>

    <!-- Server Selection -->
    <div class="bg-bg-surface border border-border-l1 rounded-xl shadow-sm p-3 mb-3">
        <div class="flex items-center gap-3">
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clip-rule="evenodd" />
                </svg>
                <label class="text-xs font-medium text-text-secondary">Server for Both Accounts</label>
            </div>
            <div class="flex-1 relative">
                <select @bind="SelectedServer" class="w-full bg-bg-surface border border-border-l1 rounded-lg px-3 py-1.5 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main appearance-none pr-8">
                    @foreach(var server in Enum.GetValues(typeof(Server)))
                    {
                        <option value="@server">@server</option>
                    }
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center pr-2.5 pointer-events-none">
                    <svg class="w-3 h-3 text-text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 relative">
        <!-- Account A Login -->
        <div class="relative bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden">
            <!-- Account Header Section -->
            <div class="p-3 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-5 h-5 rounded-lg bg-primary/10 border border-primary/30 flex items-center justify-center mr-2">
                        <span class="text-xs font-bold text-primary">A</span>
                    </div>
                    <div>
                        <h4 class="text-xs font-bold text-text-main font-jakarta">Primary Account</h4>
                        <div class="flex items-center mt-1">
                            <span class="text-xs text-text-secondary">Item sender</span>
                            <span class="mx-1.5 text-xs text-text-secondary">•</span>
                            <div class="inline-flex items-center text-xs">
                                <div class="w-4 h-3 bg-bg-surface border border-border-l1 rounded-sm flex items-center justify-center mr-1">
                                    @((MarkupString)CurrentServerFlag)
                                </div>
                                <span class="text-text-secondary">@GetServerName(SelectedServer)</span>
                            </div>
                        </div>
                    </div>
                </div>
                @if (!AccountA.IsConnected)
                {
                    <div class="flex items-center px-2 py-1 rounded-md bg-bg-base border border-border-base">
                        <div class="w-2 h-2 rounded-full bg-error"></div>
                    </div>
                }
                else
                {
                    <!-- Ready Badge -->
                    <div class="flex items-center px-2 py-1 rounded-md bg-primary/10 border border-primary/30">
                        <span class="text-[10px] font-medium text-primary">READY</span>
                    </div>
                }
            </div>

            <!-- Login Form Section -->
            <div class="p-3 pt-0 space-y-2">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <input type="text" placeholder="Username" @bind="AccountA.Username" disabled="@AccountA.IsConnected"
                           class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary @(AccountA.IsConnected ? "opacity-50" : "")" />
                </div>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <input type="password" placeholder="Password" @bind="AccountA.Password" disabled="@AccountA.IsConnected"
                           class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary @(AccountA.IsConnected ? "opacity-50" : "")" />
                </div>
                <button @onclick="@(async () => await ToggleConnectionAsync("A"))"
                        disabled="@IsAccountConnecting("A")"
                        class="w-full bg-bg-surface-hover hover:brightness-110 text-text-main px-4 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow disabled:bg-bg-base disabled:cursor-not-allowed">
                    @if (IsAccountConnecting("A"))
                    {
                        <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Logging in...</span>
                    }
                    else
                    {
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            @if (AccountA.IsConnected)
                            {
                                <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                            }
                            else
                            {
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            }
                        </svg>
                        <span>@(AccountA.IsConnected ? "Logout" : "Login")</span>
                    }
                </button>
            </div>
        </div>

        <!-- Transfer Animation -->
        <div class="hidden sm:flex absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
            <!-- Simple Transfer Arrow -->
            <div class="relative flex items-center justify-center">
                <!-- Arrow Icon -->
                <div class="w-8 h-8 bg-bg-surface/90 backdrop-blur-sm border border-border-l1 rounded-lg flex items-center justify-center shadow-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Account B Login -->
        <div class="relative bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden">
            <!-- Account Header Section -->
            <div class="p-3 flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-5 h-5 rounded-lg bg-warning/10 border border-warning/30 flex items-center justify-center mr-2">
                        <span class="text-xs font-bold text-warning">B</span>
                    </div>
                    <div>
                        <h4 class="text-xs font-bold text-text-main font-jakarta">Secondary Account</h4>
                        <div class="flex items-center mt-1">
                            <span class="text-xs text-text-secondary">Item receiver</span>
                            <span class="mx-1.5 text-xs text-text-secondary">•</span>
                            <div class="inline-flex items-center text-xs">
                                <div class="w-4 h-3 bg-bg-surface border border-border-l1 rounded-sm flex items-center justify-center mr-1">
                                    @((MarkupString)CurrentServerFlag)
                                </div>
                                <span class="text-text-secondary">@GetServerName(SelectedServer)</span>
                            </div>
                        </div>
                    </div>
                </div>
                @if (!AccountB.IsConnected)
                {
                    <div class="flex items-center px-2 py-1 rounded-md bg-bg-base border border-border-base">
                        <div class="w-2 h-2 rounded-full bg-error"></div>
                    </div>
                }
                else
                {
                    <!-- Ready Badge -->
                    <div class="flex items-center px-2 py-1 rounded-md bg-warning/10 border border-warning/30">
                        <span class="text-[10px] font-medium text-warning">READY</span>
                    </div>
                }
            </div>

            <!-- Login Form Section -->
            <div class="p-3 pt-0 space-y-2">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <input type="text" placeholder="Username" @bind="AccountB.Username" disabled="@AccountB.IsConnected"
                           class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary @(AccountB.IsConnected ? "opacity-50" : "")" />
                </div>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <input type="password" placeholder="Password" @bind="AccountB.Password" disabled="@AccountB.IsConnected"
                           class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-all duration-200 text-text-main placeholder-text-secondary @(AccountB.IsConnected ? "opacity-50" : "")" />
                </div>
                <button @onclick="@(async () => await ToggleConnectionAsync("B"))"
                        disabled="@IsAccountConnecting("B")"
                        class="w-full bg-bg-surface-hover hover:brightness-110 text-text-main px-4 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow disabled:bg-bg-base disabled:cursor-not-allowed">
                    @if (IsAccountConnecting("B"))
                    {
                        <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Logging in...</span>
                    }
                    else
                    {
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            @if (AccountB.IsConnected)
                            {
                                <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd" />
                            }
                            else
                            {
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            }
                        </svg>
                        <span>@(AccountB.IsConnected ? "Logout" : "Login")</span>
                    }
                </button>
            </div>
        </div>
    </div>
</div>
