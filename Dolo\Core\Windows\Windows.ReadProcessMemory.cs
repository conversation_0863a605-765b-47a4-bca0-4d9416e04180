﻿using System.Runtime.InteropServices;
namespace Dolo.Core.Windows;

public partial class Windows
{
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool ReadProcessMemory(nint hProcess,
        nint lpBaseAddress,
        [Out] byte[] lpBuffer,
        int dwSize,
        out nint lpNumberOfBytesRead);


    [DllImport("kernel32.dll", SetLastError = true)]
    private static extern bool ReadProcessMemory(nint hProcess,
        nint lpBaseAddress,
        nint lpBuffer,
        int dwSize,
        out nint lpNumberOfBytesRead);
}