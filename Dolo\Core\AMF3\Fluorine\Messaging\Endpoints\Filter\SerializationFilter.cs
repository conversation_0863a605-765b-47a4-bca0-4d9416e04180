﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class SerializationFilter : AbstractFilter
{
    /// <summary>
    ///     Initializes a new instance of the SerializationFilter class.
    /// </summary>
    public SerializationFilter()
    {}

    public bool UseLegacyCollection
    {
        get;
        set;
    } = false;

    #region IFilter Members

    public override Task Invoke(AMFContext context)
    {
        var serializer = new AMFSerializer(context.MessageOutput);
        serializer.UseLegacyCollection = UseLegacyCollection;
        return Task.FromResult<object>(null);
    }

    #endregion
}