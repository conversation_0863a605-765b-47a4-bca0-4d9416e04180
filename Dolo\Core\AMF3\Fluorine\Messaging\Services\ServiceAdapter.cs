﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Services;

/// <summary>
///     A Service adapter bridges destinations to back-end systems.
///     The ServiceAdapter class is the base definition of a service adapter.
/// </summary>
internal abstract class ServiceAdapter
{

    /// <summary>
    ///     Initializes a new instance of the ServiceAdapter class.
    /// </summary>
    protected ServiceAdapter()
    {}
    /// <summary>
    ///     Gets whether the adapter performs custom subscription management. The default return value is false.
    /// </summary>
    public virtual bool HandlesSubscriptions => false;
    /// <summary>
    ///     Returns the Destination of the ServiceAdapter.
    /// </summary>
    public Destination Destination
    {
        get;
        private set;
    }
    /// <summary>
    ///     Gets the settings for the Destination of the ServiceAdapter.
    /// </summary>
    public DestinationSettings DestinationSettings
    {
        get;
        private set;
    }
    /// <summary>
    ///     Gets settings for the ServiceAdapter.
    /// </summary>
    public AdapterSettings AdapterSettings
    {
        get;
        private set;
    }
    /// <summary>
    ///     Gets an object that can be used to synchronize access.
    /// </summary>
    public object SyncRoot
    {
        get;
    } = new();

    /// <summary>
    ///     Process a message routed for this adapter.
    /// </summary>
    /// <param name="message">The message sent by the client.</param>
    /// <returns>The body of the acknowledge message (or null if there is no body).</returns>
    public virtual Task<object> Invoke(IMessage message) => Task.FromResult<object>(null);

    /// <summary>
    ///     Accept a command from the adapter's service (subscribe, unsubscribe and ping operations).
    /// </summary>
    /// <param name="commandMessage"></param>
    /// <returns></returns>
    public virtual object Manage(CommandMessage commandMessage) => new AcknowledgeMessage();
    /// <summary>
    ///     Adapter initialization.
    /// </summary>
    public virtual void Init()
    {}
    /// <summary>
    ///     Stops the adapter.
    /// </summary>
    public virtual void Stop()
    {}
    internal void SetDestination(Destination value)
    {
        Destination = value;
    }
    internal void SetDestinationSettings(DestinationSettings value)
    {
        DestinationSettings = value;
    }
    internal void SetAdapterSettings(AdapterSettings value)
    {
        AdapterSettings = value;
    }
}