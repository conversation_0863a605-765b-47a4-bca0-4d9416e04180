﻿using System.Runtime.InteropServices;
namespace Dolo.Core;

public static class ProxyDetector
{
    public static Action? ProxyChanged;
    public static bool IsDetected { get; private set; }
    public static Task DetectAsync() => Task.Factory.StartNew(async () => {
        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            return;

        var tmr = new PeriodicTimer(new(0, 0, 0, 0, 1));
        while (await tmr.WaitForNextTickAsync())
            if (Windows.Windows.HasProxy() && !IsDetected)
            {
                ProxyChanged?.Invoke();
                IsDetected = true;
            }
    });
}