﻿namespace Dolo.Core.Windows;

public partial class Windows
{
    /// <summary>
    ///     Get the os version majoryy number
    /// </summary>
    public static int GetVersion()
        => Environment.OSVersion.Version.Major;

    /// <summary>
    ///     Indicates whether the current operating system is Windows 7
    /// </summary>
    public static bool IsWindows7()
        => GetVersion() == 7;

    /// <summary>
    ///     Indicates whether the current operating system is Windows 8
    /// </summary>
    public static bool IsWindows8()
        => GetVersion() == 8;

    /// <summary>
    ///     Indicates whether the current operating system is Windows 10
    /// </summary>
    public static bool IsWindows10()
        => GetVersion() == 10;

    /// <summary>
    ///     Indicates whether the current operating system is Windows 11
    /// </summary>
    public static bool IsWindows11()
        => GetVersion() == 11;
}