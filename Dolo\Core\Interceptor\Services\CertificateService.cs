using System.Diagnostics;
using System.Net;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Collections.Concurrent;

namespace Dolo.Core.Interceptor.Services;

public class CertificateService : IDisposable
{
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _certificateGenerationLocks = new();
    private readonly ConcurrentDictionary<string, X509Certificate2> _memoryCertificateCache = new();
    
    private readonly InterceptorConfig _config;
    private readonly ILogger _logger;
    private X509Certificate2? _rootCertificate;
    private bool _disposed;
    private readonly string _certificateCachePath;    public CertificateService(InterceptorConfig config)
    {
        _config = config ?? throw new ArgumentNullException(nameof(config));
        _logger = _config.LoggerFactory?.CreateLogger("Dolo.Core.Interceptor.Certificate") ??
                 LoggerFactory.Create(builder => builder.SetMinimumLevel(LogLevel.None))
                     .CreateLogger("Dolo.Core.Interceptor.Certificate");
        
        _certificateCachePath = Path.Combine(AppContext.BaseDirectory, "cert_cache");
        Directory.CreateDirectory(_certificateCachePath);

        _rootCertificate = LoadOrGenerateRootCertificate();
        if (_rootCertificate != null)
        {
            CertificateDiagnostics.LogCertificateDetails(_logger, "Root CA", _rootCertificate);
            LogRootCATrust(_rootCertificate);
        }
    }

    public event EventHandler<CertificateGeneratedEventArgs>? CertificateGenerated;

    public X509Certificate2 RootCertificate => _rootCertificate ?? throw new InvalidOperationException("Root certificate not initialized");

    private X509Certificate2 LoadOrGenerateRootCertificate()
    {
        var rootCertPath = Path.Combine(_certificateCachePath, "root_ca.pfx");
        var rootCertPassword = "password";

        if (File.Exists(rootCertPath))        {
            try
            {
                var loadedCert = X509CertificateLoader.LoadPkcs12FromFile(rootCertPath, rootCertPassword, X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet);
                if (loadedCert.NotAfter > DateTime.Now)
                {
                    _logger.LogInformation("Root CA loaded from file.");
                    return loadedCert;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load root CA from file. A new one will be generated.");
            }
        }

        var newRootCert = GenerateRootCertificate();
        try
        {
            File.WriteAllBytes(rootCertPath, newRootCert.Export(X509ContentType.Pfx, rootCertPassword));
            _logger.LogInformation("Root CA generated and saved to file.");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to save new root CA to file.");
        }
        return newRootCert;
    }

    public X509Certificate2 GetOrCreateCertificate(string hostname)
    {
        if (_memoryCertificateCache.TryGetValue(hostname, out var cachedCert) && VerifyCertificateChain(cachedCert, "Memory Cache Check"))
        {
            return cachedCert;
        }

        var certPath = Path.Combine(_certificateCachePath, $"{hostname}.pfx");        if (File.Exists(certPath))
        {
            try
            {
                // Load certificate with password (consistent with saving)
                var fileCert = X509CertificateLoader.LoadPkcs12FromFile(certPath, "cache", X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet);
                if (VerifyCertificateChain(fileCert, "File Cache Check"))
                {
                    _logger.LogInformation("Loaded certificate for {Hostname} from file cache.", hostname);
                    _memoryCertificateCache[hostname] = fileCert;
                    return fileCert;
                }
                else
                {
                    _logger.LogInformation("Certificate for {Hostname} in file cache failed validation (expired or no private key). Deleting and regenerating.", hostname);
                    fileCert.Dispose();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load certificate for {Hostname} from file cache. Will generate a new one.", hostname);
            }
            
            // Delete invalid cache file
            try
            {
                File.Delete(certPath);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete invalid certificate cache file for {Hostname}.", hostname);
            }
        }

        var certLock = _certificateGenerationLocks.GetOrAdd(hostname, _ => new SemaphoreSlim(1, 1));
        certLock.Wait();
        try
        {
            if (_memoryCertificateCache.TryGetValue(hostname, out cachedCert) && VerifyCertificateChain(cachedCert, "Post-Lock Cache Check"))
            {
                return cachedCert;
            }

            var cert = GenerateCertificateForHost(hostname);
            
            if (!VerifyCertificateChain(cert, "Post-Generation Check"))
            {
                 _logger.LogError("CRITICAL: Generated certificate for {Hostname} failed chain validation immediately after creation.", hostname);
                 throw new CryptographicException("Generated certificate is invalid.");
            }            _logger.LogInformation("🔑 Certificate generated for {Hostname}", hostname);
            
            try
            {
                // Save certificate with private key using a password (consistent with generation)
                var pfxPassword = "cache"; // Simple password for file cache
                var pfxBytes = cert.Export(X509ContentType.Pfx, pfxPassword);
                File.WriteAllBytes(certPath, pfxBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to save certificate for {Hostname} to file cache.", hostname);
            }

            _memoryCertificateCache[hostname] = cert;
            CertificateGenerated?.Invoke(this, new CertificateGeneratedEventArgs(hostname, cert.Subject, cert.Thumbprint, cert.NotBefore, cert.NotAfter));
            return cert;
        }
        finally
        {
            certLock.Release();
        }
    }    private bool VerifyCertificateChain(X509Certificate2 certificate, string context)
    {
        if (_rootCertificate == null)
        {
            _logger.LogWarning("[{Context}] Cannot verify certificate chain, Root CA is not loaded.", context);
            return false;
        }
        
        // Check if certificate has expired
        if (certificate.NotAfter < DateTime.Now)
        {
            _logger.LogWarning("[{Context}] Certificate for {Subject} has expired.", context, certificate.Subject);
            return false;
        }
        
        // Check if certificate has a private key (essential for SSL server operation)
        if (!certificate.HasPrivateKey)
        {
            _logger.LogWarning("[{Context}] Certificate for {Subject} does not have a private key.", context, certificate.Subject);
            return false;
        }

        try
        {
            using var chain = new X509Chain();
            chain.ChainPolicy.ExtraStore.Add(_rootCertificate);
            chain.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck;
            chain.ChainPolicy.VerificationFlags = X509VerificationFlags.NoFlag;

            bool isChainValid = chain.Build(certificate);

            if (!isChainValid)
            {
                CertificateDiagnostics.LogChainValidationFailure(_logger, context, certificate.Subject, chain);
            }
            
            return isChainValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[{Context}] Exception during certificate chain verification for {Subject}", context, certificate.Subject);
            return false;
        }
    }

    private X509Certificate2 GenerateCertificateForHost(string hostname)
    {
        if (_rootCertificate == null)
            throw new InvalidOperationException("Root certificate is not available for signing.");

        using var rsa = RSA.Create(2048);
        var certRequest = new CertificateRequest(
            $"CN={hostname}, O={_config.CertificateOrganization}, C={_config.CertificateCountry}",
            rsa,
            HashAlgorithmName.SHA256,
            RSASignaturePadding.Pkcs1);

        certRequest.CertificateExtensions.Add(new X509BasicConstraintsExtension(false, false, 0, true));
        certRequest.CertificateExtensions.Add(new X509KeyUsageExtension(X509KeyUsageFlags.DigitalSignature | X509KeyUsageFlags.KeyEncipherment, true));
        certRequest.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(certRequest.PublicKey, false));
        
        var sanBuilder = new SubjectAlternativeNameBuilder();
        sanBuilder.AddDnsName(hostname);
        certRequest.CertificateExtensions.Add(sanBuilder.Build());

        var notBefore = DateTimeOffset.UtcNow.AddDays(-1);
        var notAfter = DateTimeOffset.UtcNow.AddMonths(6);        var serialNumber = RandomNumberGenerator.GetBytes(16);
        using var certificate = certRequest.Create(_rootCertificate, notBefore, notAfter, serialNumber);
          // Create certificate with private key using a more robust approach
        using var certWithKey = certificate.CopyWithPrivateKey(rsa);
        
        // Export to PFX with a password to ensure private key persistence
        var pfxPassword = Guid.NewGuid().ToString("N")[..16]; // Random password
        var pfxBytes = certWithKey.Export(X509ContentType.Pfx, pfxPassword);
        
        // Reload from PFX to ensure proper private key association
        var finalCert = X509CertificateLoader.LoadPkcs12(pfxBytes, pfxPassword, X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet);
        
        CertificateDiagnostics.LogCertificateDetails(_logger, $"Generated for {hostname}", finalCert);
          // Verify the certificate has a private key
        if (!finalCert.HasPrivateKey)
        {
            _logger.LogError("CRITICAL: Generated certificate for {Hostname} does not have a private key after all processing steps", hostname);
            finalCert.Dispose();
            throw new CryptographicException("Certificate generation failed - no private key associated");
        }
        
        return finalCert;
    }

    private X509Certificate2 GenerateRootCertificate()
    {
        using var rsa = RSA.Create(4096);
        var request = new CertificateRequest(
            $"CN={_config.RootCertificateName}, O={_config.CertificateOrganization}, C={_config.CertificateCountry}",
            rsa,
            HashAlgorithmName.SHA256,
            RSASignaturePadding.Pkcs1);

        request.CertificateExtensions.Add(new X509BasicConstraintsExtension(true, true, 1, true));
        request.CertificateExtensions.Add(new X509KeyUsageExtension(X509KeyUsageFlags.KeyCertSign | X509KeyUsageFlags.CrlSign, true));
        request.CertificateExtensions.Add(new X509SubjectKeyIdentifierExtension(request.PublicKey, false));

        var serialNumber = RandomNumberGenerator.GetBytes(16);        var notBefore = DateTimeOffset.UtcNow.AddDays(-1);
        var notAfter = DateTimeOffset.UtcNow.AddYears(10);
        var rootCert = request.CreateSelfSigned(notBefore, notAfter);
        
        return X509CertificateLoader.LoadPkcs12(rootCert.Export(X509ContentType.Pfx, "password"), "password", X509KeyStorageFlags.Exportable | X509KeyStorageFlags.PersistKeySet);
    }    /// <summary>
    /// Installs the root certificate into the Windows certificate store
    /// </summary>
    public bool InstallRootCertificate()
    {
        if (_rootCertificate == null)
        {
            _logger.LogError("Cannot install root certificate - root certificate is not loaded");
            return false;
        }

        try
        {
            using var store = new X509Store(StoreName.Root, StoreLocation.CurrentUser);
            store.Open(OpenFlags.ReadWrite);
            
            // Check if already installed
            if (store.Certificates.Any(c => c.Thumbprint == _rootCertificate.Thumbprint))
            {
                _logger.LogInformation("Root certificate is already installed");
                return true;
            }
            
            store.Add(_rootCertificate);
            _logger.LogInformation("Root certificate installed successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to install root certificate");
            return false;
        }
    }

    private void LogRootCATrust(X509Certificate2 rootCert)
    {
        var trusted = false;
        try
        {
            using (var store = new X509Store(StoreName.Root, StoreLocation.CurrentUser))
            {
                store.Open(OpenFlags.ReadOnly);
                trusted = store.Certificates.Any(c => c.Thumbprint == rootCert.Thumbprint);
            }
            if (!trusted)
            {
                using (var store = new X509Store(StoreName.Root, StoreLocation.LocalMachine))
                {
                    store.Open(OpenFlags.ReadOnly);
                    trusted = store.Certificates.Any(c => c.Thumbprint == rootCert.Thumbprint);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking root CA trust status.");
        }
        
        if (trusted)
            _logger.LogInformation("Root CA is trusted: {Subject}", rootCert.Subject);
        else
            _logger.LogWarning("Root CA is NOT trusted: {Subject}", rootCert.Subject);
    }

    public OcspStatus ValidateServerCertificate(X509Certificate2 certificate)
    {
        if (!_config.EnableOcspValidation)
        {
            return OcspStatus.Good;
        }

        try
        {
            using var chain = new X509Chain();
            chain.ChainPolicy.RevocationMode = X509RevocationMode.Online;
            chain.ChainPolicy.RevocationFlag = X509RevocationFlag.ExcludeRoot;
            chain.ChainPolicy.UrlRetrievalTimeout = TimeSpan.FromSeconds(10);

            if (chain.Build(certificate))
            {
                return OcspStatus.Good;
            }

            if (chain.ChainStatus.Any(s => s.Status.HasFlag(X509ChainStatusFlags.Revoked)))
            {
                return OcspStatus.Revoked;
            }

            return OcspStatus.Unknown;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception during OCSP validation for {Subject}.", certificate.Subject);
            return OcspStatus.Unknown;
        }
    }

    /// <summary>
    /// Clears the certificate cache, forcing regeneration of all certificates.
    /// This is useful when upgrading certificate generation logic.
    /// </summary>
    public void ClearCertificateCache()
    {
        try
        {
            _logger.LogInformation("🧹 Clearing certificate cache...");
            
            // Clear memory cache
            foreach (var cert in _memoryCertificateCache.Values)
            {
                cert.Dispose();
            }
            _memoryCertificateCache.Clear();
            
            // Clear file cache (but preserve root CA)
            var cacheFiles = Directory.GetFiles(_certificateCachePath, "*.pfx")
                .Where(f => !Path.GetFileName(f).Equals("root_ca.pfx", StringComparison.OrdinalIgnoreCase));
            
            foreach (var file in cacheFiles)
            {
                try
                {
                    File.Delete(file);
                    _logger.LogDebug("Deleted cache file: {File}", Path.GetFileName(file));
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete cache file: {File}", file);
                }
            }

            _logger.LogInformation("Certificate cache cleared successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear certificate cache");
        }
    }

    /// <summary>
    /// Checks if there are any cached certificates without private keys
    /// </summary>
    public bool HasInvalidCachedCertificates()
    {
        try
        {
            var cacheFiles = Directory.GetFiles(_certificateCachePath, "*.pfx")
                .Where(f => !Path.GetFileName(f).Equals("root_ca.pfx", StringComparison.OrdinalIgnoreCase));
            
            foreach (var file in cacheFiles)
            {
                try
                {
                    var cert = X509CertificateLoader.LoadPkcs12FromFile(file, "cache", X509KeyStorageFlags.Exportable);
                    if (!cert.HasPrivateKey)
                    {
                        cert.Dispose();
                        return true;
                    }
                    cert.Dispose();
                }
                catch
                {
                    // If we can't load with new password, it's likely an old format
                    return true;
                }
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                foreach (var cert in _memoryCertificateCache.Values)
                {
                    cert.Dispose();
                }
                _rootCertificate?.Dispose();
                foreach (var semaphore in _certificateGenerationLocks.Values)
                {
                    semaphore.Dispose();
                }
            }
            _disposed = true;
        }
    }
}
