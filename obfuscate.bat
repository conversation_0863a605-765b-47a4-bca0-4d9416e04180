@echo off
setlocal enabledelayedexpansion

:: Fixed base path for all project operations
set "base_dir=C:\Users\<USER>\Desktop\Projects\Dolo"

:: Constants
set "exe_path=%base_dir%\Eazfix\EazTrialRemover.exe"
set "eazfuscator_path=C:\Program Files (x86)\Gap<PERSON>chenko\Eazfuscator.NET\Eazfuscator.NET.exe"

:: Defaults
set "project_subdir="
set "dll_name="
set "build_config=Release"
set "target_framework=net8.0"
set "target_rid=win-x64"
set "os_type=Windows"  :: Default OS type

:: Parse named command-line arguments
:parse_args
if "%~1"=="" goto args_parsed
if /i "%~1"=="-projectsubdir" set "project_subdir=%~2" & shift & shift & goto parse_args
if /i "%~1"=="-dllname" set "dll_name=%~2" & shift & shift & goto parse_args
if /i "%~1"=="-buildconfig" set "build_config=%~2" & shift & shift & goto parse_args
if /i "%~1"=="-framework" set "target_framework=%~2" & shift & shift & goto parse_args
if /i "%~1"=="-rid" (
    set "target_rid=%~2"
    echo !target_rid! | findstr /i "osx" >nul && set "os_type=MacOS"
    shift & shift & goto parse_args
)
shift
goto parse_args

:args_parsed
if "!project_subdir!"=="" (
    echo Missing required argument: -projectsubdir
    exit /b 1
)
if "!dll_name!"=="" (
    echo Missing required argument: -dllname
    exit /b 1
)

:: Adjust message based on OS type
if "!os_type!"=="MacOS" (
    echo "Obfuscating !dll_name! for MacOS with the following settings:"
) else (
    echo "Obfuscating !dll_name! for Windows with the following settings:"
)

echo - Project Subdirectory: !project_subdir!
echo - Build Config: !build_config!
echo - Framework: !target_framework!
echo - RID: !target_rid!

:: Process both bin and obj directories for completeness
for %%d in (bin obj) do (
    echo.
    echo Processing in '%%d' directory...
    set "folder_path=!base_dir!\!project_subdir!\%%d\!build_config!\!target_framework!\!target_rid!"
    set "dll_path=!folder_path!\!dll_name!.dll"
    
    if exist "!dll_path!" (
        echo Removing trial from "!dll_path!"...
        "!exe_path!" "!dll_path!"
        if errorlevel 1 (
            echo Error removing trial from "!dll_path!"
            goto error
        )
        
        echo Deleting original DLL...
        del "!dll_path!"
        if errorlevel 1 (
            echo Error deleting "!dll_path!"
            goto error
        )
        
        pushd "!folder_path!"
        echo Renaming !dll_name!_p.dll to !dll_name!.dll...
        if exist "!dll_name!_p.dll" (
            rename "!dll_name!_p.dll" "!dll_name!.dll"
            if errorlevel 1 (
                echo Error renaming file.
                popd
                goto error
            )
        ) else (
            echo File not found to rename: "!folder_path!\!dll_name!_p.dll"
        )
        popd
    ) else (
        echo File not found, skipping: "!dll_path!"
    )
)

goto end

:error
echo An error occurred during processing.
exit /b 1

:end
echo.
echo Processing completed successfully.
endlocal