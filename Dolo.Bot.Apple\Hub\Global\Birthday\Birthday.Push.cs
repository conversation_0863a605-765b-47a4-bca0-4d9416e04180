﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Birthday;

public partial class Birthday
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("push")]
    [Description("push a new birthday")]
    public async Task PushAsync(SlashCommandContext ctx, [Description("the user which has birthday")] DiscordUser user)
    {
        await ctx.Interaction.DeferAsync(ctx.Channel == HubChannel.Birthday);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == ctx.User.Id);
        if (member is null)
            return;

        // print the message in the chat
        await ctx.TryEditResponseAsync($"{HubEmoji.Cake} **HAPPY BIRTHDAY** <@{user.Id}> {HubEmoji.Tada}{HubEmoji.WhiteHeart}{HubEmoji.Party}");
    }
}