﻿using System.Globalization;

namespace Dolo.Core.Extension;

public static class DateTimeExtension
{
    public static long ToTimestamp(this TimeSpan timeSpan) => (long)timeSpan.TotalSeconds;
    public static long ToTimestamp(this DateTimeOffset dateTime) => (long)(dateTime - new DateTimeOffset(1970, 1, 1, 0, 0, 0, 0, TimeSpan.Zero)).TotalSeconds;
    public static long ToTimestamp(this DateTime dateTime) => (long)(dateTime - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds;
    public static long ToTimestamp(this string time, string format) => (long)(DateTime.ParseExact(time, format, null)             - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds;
    public static long ToTimestampMilliseconds(this DateTime dateTime) => (long)(dateTime                                         - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalMilliseconds;
    public static long ToTimestampMilliseconds(this string time, string format) => (long)(DateTime.ParseExact(time, format, CultureInfo.InvariantCulture) - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalMilliseconds;
    public static string? GetTimeString(this DateTime time)
    {
        var date = time > DateTime.Now ? time.Subtract(DateTime.Now) : DateTime.Now.Subtract(time);

        if (date.Days > 0)
            return $"{date.Days:N0} {"Day".Pluralize(date.Days)}";
        if (date is { Days: 0, Hours: > 0 })
            return $"{date.Hours:N0} {"Hour".Pluralize(date.Hours)}";
        if (date.Days == 0 && date is { Hours: 0, Minutes: > 0 })
            return $"{date.Minutes:N0} {"Minute".Pluralize(date.Minutes)}";
        if (date is { Days: 0, Hours: 0 } and { Minutes: > 0, Seconds: > 0 })
            return $"{date.Seconds:N0} {"Second".Pluralize(date.Seconds)}";
        if (date.Days == 0 && date is { Hours: 0, Minutes: 0 } and { Seconds: 0, Milliseconds: > 0 })
            return $"{date.Milliseconds:N0} {"Millisecond".Pluralize(date.Milliseconds)}";

        return default;
    }

    public static string? GetTimeString(this DateTimeOffset time)
    {
        var date = time.DateTime > DateTime.Now ? time.Subtract(DateTime.Now) : DateTime.Now.Subtract(time.DateTime);

        if (date.Days > 0)
            return $"{date.Days:N0} {"Day".Pluralize(date.Days)}";
        if (date is { Days: 0, Hours: > 0 })
            return $"{date.Hours:N0} {"Hour".Pluralize(date.Hours)}";
        if (date.Days == 0 && date is { Hours: 0, Minutes: > 0 })
            return $"{date.Minutes:N0} {"Minute".Pluralize(date.Minutes)}";
        if (date is { Days: 0, Hours: 0 } and { Minutes: > 0, Seconds: > 0 })
            return $"{date.Seconds:N0} {"Second".Pluralize(date.Seconds)}";
        if (date.Days == 0 && date is { Hours: 0, Minutes: 0 } and { Seconds: 0, Milliseconds: > 0 })
            return $"{date.Milliseconds:N0} {"Millisecond".Pluralize(date.Milliseconds)}";

        return default;
    }
}