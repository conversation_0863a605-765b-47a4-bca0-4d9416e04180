﻿using Dolo.Database;
using System.Diagnostics;
namespace Dolo.Bot.Apple.Hub.Pluto.Histogram;

[Command("histogram")]
[Description("commands for the histogram machine")]
public partial class Histogram
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("get")]
    [Description("Get a histogram")]
    public async Task GetHistogramAsync(SlashCommandContext ctx)
    {
        var cancel = new CancellationTokenSource();

        await ctx.Interaction.DeferAsync();
        await ctx.TryEditResponseAsync($"{HubEmoji.MspLoading} Generating new histogram...");
        await Task.Run(async () => {
            await Task.Delay(3000, cancel.Token);
            if (!cancel.IsCancellationRequested)
                await ctx.TryEditResponseAsync($"{HubEmoji.MspLoading} Loading Actor Sprite ..");

            await Task.Delay(4500, cancel.Token);
            if (!cancel.IsCancellationRequested)
                await ctx.TryEditResponseAsync($"{HubEmoji.MspLoading} Processing matching color hist ..");
        }, cancel.Token);

        const string app = "Dolo.Planet.Histogram.exe";
        await Process.Start(new ProcessStartInfo(app)
            {
                FileName = app,
                Arguments = "-single",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                CreateNoWindow = false
            })?.WaitForExitAsync(cancel.Token)
            .WaitAsync(TimeSpan.FromSeconds(25), cancel.Token)!;

        await cancel.CancelAsync();

        var histo = await Mongo.Histogram.GetAsync();
        await ctx.TryEditResponseAsync($"Generated histogram: `{histo.Last().Histo}`");
    }
}