﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Api.Event;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     Base interface containing common methods and attributs for all core objects.
/// </summary>
internal interface ICoreObject : IAttributeStore, IEventDispatcher, Event.IEventHandler, IEventListener
{}