﻿using Dolo.Core.Discord;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Log.Hub.Handler;

public static class MessageCreate
{
    public static async Task InvokeAsync(this MessageCreatedEventArgs e)
    {
        // cache the message
        HubCache.AddMessage(e.Message.Id, e.Message);

        // check if the message is a bot or user
        if (HubChannel.Log is null || e.Message.Author?.Id == Hub.Discord?.CurrentUser.Id || (e.Message.Author?.IsBot ?? false))
            return;

        // log the message to the channel
        await HubChannel.Log.TrySendMessageAsync(HubEmbed.MessageCreated(e.Message));

        // log and clone attachments if they exist
        if (e.Message.Attachments.Any())
        {
            await AttachmentLogger.LogAttachmentsCreatedAsync(e.Message);

            // Queue attachments for cloning to preserve them
            if (HubChannel.Attachments != null)
            {
                var cachedAttachments = HubCache.GetMessageAttachments(e.Message.Id);
                foreach (var attachment in cachedAttachments)
                {
                    Services.AttachmentCloneService.QueueAttachmentClone(e.Message.Id, attachment, HubChannel.Attachments);
                }
            }
        }
    }
}
