﻿using System.Net.Security;
using Titanium.Web.Proxy;
using Titanium.Web.Proxy.EventArguments;
using Titanium.Web.Proxy.Http;
using Titanium.Web.Proxy.Models;
namespace Dolo.Core.Http;

public class HttpIntercept
{
    private readonly ProxyServer _proxyServer;
    public HttpIntercept()
    {
        _proxyServer = new();
        _proxyServer.ServerCertificateValidationCallback += (sender, e) => {
            if (e.SslPolicyErrors == SslPolicyErrors.None)
                e.IsValid = true;

            OnCertificateValidated?.Invoke(sender, e);
            return Task.CompletedTask;
        };

        _proxyServer.ClientCertificateSelectionCallback += (sender, e) => Task.FromResult(OnCertificateSelected?.Invoke(sender, e));
        _proxyServer.BeforeResponse += (sender, e) => Task.FromResult(OnResponse?.Invoke(sender, e));
        _proxyServer.BeforeRequest += (sender, e) => Task.FromResult(OnRequest?.Invoke(sender, e));
        _proxyServer.AfterResponse += (sender, e) => Task.FromResult(OnAfterResponse?.Invoke(sender, e));
        _proxyServer.BeforeUpStreamConnectRequest += (sender, e) => Task.FromResult(OnConnectionCreated?.Invoke(sender, e));
    }

    /// <summary>
    ///     Fired when a response is received from the server
    /// </summary>
    public AsyncEventHandler<SessionEventArgs>? OnResponse { get; set; }
    /// <summary>
    ///     Fired when a request is sent to the server
    /// </summary>
    public AsyncEventHandler<SessionEventArgs>? OnRequest { get; set; }
    /// <summary>
    ///     Fired after we got the response from the server
    /// </summary>
    public AsyncEventHandler<SessionEventArgs>? OnAfterResponse { get; set; }
    /// <summary>
    ///     Fired when a new connection is created
    /// </summary>
    public AsyncEventHandler<ConnectRequest>? OnConnectionCreated { get; set; }
    /// <summary>
    ///     Fired when a certificate is selected
    /// </summary>
    public AsyncEventHandler<CertificateSelectionEventArgs>? OnCertificateSelected { get; set; }
    /// <summary>
    ///     Fired when a certificate is validated
    /// </summary>
    public AsyncEventHandler<CertificateValidationEventArgs>? OnCertificateValidated { get; set; }

    /// <summary>
    ///     Checks whenever the proxy is intercepting
    /// </summary>
    public bool IsIntercepting { get; set; }
    public void Start()
    {
        var explicitEndPoint = new ExplicitProxyEndPoint(IPAddress.Any, RandomNumberGenerator.GetInt32(1000, 9000));
        var transparentEndPoint = new TransparentProxyEndPoint(IPAddress.Any, RandomNumberGenerator.GetInt32(1000, 9000))
        {
            GenericCertificateName = "google.com"
        };

        _proxyServer.AddEndPoint(explicitEndPoint);
        _proxyServer.Start();

        _proxyServer.AddEndPoint(transparentEndPoint);
        _proxyServer.SetAsSystemHttpProxy(_proxyServer.ProxyEndPoints.Where(a => a is ExplicitProxyEndPoint).Cast<ExplicitProxyEndPoint>().First());
        _proxyServer.SetAsSystemHttpsProxy(_proxyServer.ProxyEndPoints.Where(a => a is ExplicitProxyEndPoint).Cast<ExplicitProxyEndPoint>().First());

        IsIntercepting = true;
    }
    public Task EnsureCertificateAsync() => _ = Task.Run(() => {
        _proxyServer.CertificateManager.EnsureRootCertificate();
        _proxyServer.CertificateManager.TrustRootCertificate(true);
    });
    public void Stop()
    {
        _proxyServer.Stop();
        _proxyServer.RestoreOriginalProxySettings();

        IsIntercepting = false;
    }
    public void Dispose() => _proxyServer.Dispose();
}