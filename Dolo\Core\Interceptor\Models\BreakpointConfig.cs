using System.Text.RegularExpressions;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Breakpoint processing modes for handling concurrent breakpoints
/// </summary>
public enum BreakpointProcessingMode {
    /// <summary>
    /// Process breakpoints sequentially (reliable input handling, no conflicts) - DEFAULT
    /// </summary>
    Sequential,

    /// <summary>
    /// Process breakpoints concurrently (faster, but potential input conflicts)
    /// </summary>
    Concurrent
}

/// <summary>
/// Configuration for breakpoint rules and settings
/// </summary>
public sealed class BreakpointConfig
{
    public bool EnableRequestBreakpoints { get; set; } = false;
    public bool EnableResponseBreakpoints { get; set; } = false;
    public List<BreakpointRule> Rules { get; set; } = [];
    public TimeSpan BreakpointTimeout { get; set; } = TimeSpan.FromMinutes(5);
    public int MaxConcurrentBreakpoints { get; set; } = 3; // FIXED: Allow multiple breakpoints per connection
    public BreakpointProcessingMode ProcessingMode { get; set; } = BreakpointProcessingMode.Sequential; // DEFAULT: Sequential

    /// <summary>
    /// Adds a breakpoint rule for URL patterns
    /// </summary>
    public BreakpointConfig AddRule(string urlPattern, BreakpointType type = BreakpointType.Both)
    {
        Rules.Add(new BreakpointRule
        {
            UrlPattern = urlPattern,
            Type = type,
            IsEnabled = true
        });
        return this;
    }

    /// <summary>
    /// Enables request breakpoints
    /// </summary>
    public BreakpointConfig WithRequestBreakpoints()
    {
        EnableRequestBreakpoints = true;
        return this;
    }

    /// <summary>
    /// Enables response breakpoints
    /// </summary>
    public BreakpointConfig WithResponseBreakpoints()
    {
        EnableResponseBreakpoints = true;
        return this;
    }

    /// <summary>
    /// Enables both request and response breakpoints
    /// </summary>
    public BreakpointConfig WithAllBreakpoints()
    {
        EnableRequestBreakpoints = true;
        EnableResponseBreakpoints = true;
        return this;
    }

    /// <summary>
    /// Sets the breakpoint timeout
    /// </summary>
    public BreakpointConfig WithTimeout(TimeSpan timeout)
    {
        BreakpointTimeout = timeout;
        return this;
    }

    /// <summary>
    /// Sets breakpoint processing to sequential mode (default) - reliable input handling
    /// </summary>
    public BreakpointConfig WithSequentialProcessing()
    {
        ProcessingMode = BreakpointProcessingMode.Sequential;
        return this;
    }

    /// <summary>
    /// Sets breakpoint processing to concurrent mode - faster but potential input conflicts
    /// </summary>
    public BreakpointConfig WithConcurrentProcessing()
    {
        ProcessingMode = BreakpointProcessingMode.Concurrent;
        return this;
    }

    /// <summary>
    /// Checks if a URL should trigger a breakpoint
    /// </summary>
    public bool ShouldBreakpoint(string url, BreakpointType type)
    {
        if (!IsBreakpointTypeEnabled(type))
            return false;

        if (Rules.Count == 0)
            return true; // If no rules, break on everything when enabled

        return Rules.Any(rule => rule.IsEnabled && 
                                rule.Type.HasFlag(type) && 
                                rule.Matches(url));
    }

    private bool IsBreakpointTypeEnabled(BreakpointType type)
    {
        return type switch
        {
            BreakpointType.Request => EnableRequestBreakpoints,
            BreakpointType.Response => EnableResponseBreakpoints,
            _ => false
        };
    }
}

/// <summary>
/// Represents a breakpoint rule
/// </summary>
public sealed class BreakpointRule
{
    public string UrlPattern { get; set; } = string.Empty;
    public BreakpointType Type { get; set; }
    public bool IsEnabled { get; set; } = true;
    private Regex? _compiledPattern;

    /// <summary>
    /// Checks if the URL matches this rule
    /// </summary>
    public bool Matches(string url)
    {
        if (string.IsNullOrEmpty(UrlPattern))
            return true;

        try
        {
            _compiledPattern ??= new Regex(UrlPattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);
            return _compiledPattern.IsMatch(url);
        }
        catch
        {
            // If regex compilation fails, fall back to simple contains check
            return url.Contains(UrlPattern, StringComparison.OrdinalIgnoreCase);
        }
    }
}


