﻿namespace Dolo.Bot.Apple.Hub.Global.Avatar;

public partial class Avatar
{
    [Command("nitro")]
    public async Task NitroAsync(TextCommandContext ctx, DiscordMember? member = null)
    {
        var user = member ?? ctx.Member;
        await ctx.Channel.TrySendMessageAsync(user?.GuildAvatarHash is null
                                                  ? user!.GetAvatarUrl(MediaFormat.Auto)
                                                  : user.GetGuildAvatarUrl(MediaFormat.Auto));
    }
}