﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("reset")]
    [Description("reset the level points")]
    public async Task ResetAsync(SlashCommandContext ctx, [Description("the user which points should be reset")] DiscordUser user)
    {
        await ctx.Interaction.DeferAsync(true);

        // try to get the member
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);
        if (member is null)
        {
            await ctx.TryEditResponseAsync($"{user.Mention} is not a member of this server.");
            return;
        }

        // reset level state
        member.Level.Reset();

        // update the database user
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, member.Member.DiscordId),
        Builders<ServerMember>.Update.Set(a => a.Level, member.Level));

        // revoke all the roles
        if (Hub.Guild!.TryGetMember(user.Id, out var mem))
        {
            // try to revoke the low role
            if (mem.Roles.Contains(HubRoles.Low))
                await mem.TryRevokeAsync(HubRoles.Low);

            // try to revoke the high role
            if (mem.Roles.Contains(HubRoles.High))
                await mem.TryRevokeAsync(HubRoles.High);

            // try to revoke the epic role
            if (mem.Roles.Contains(HubRoles.Epic))
                await mem.TryRevokeAsync(HubRoles.Epic);
        }

        // print response
        await ctx.TryEditResponseAsync($"{user.Username} level has been reset.");
    }
}