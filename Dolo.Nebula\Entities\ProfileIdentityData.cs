using Dolo.Nebula.Enum;
using Newtonsoft.Json;

namespace Dolo.Nebula.Entities;

public class ProfileIdentityData {
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("login")]
    public string? Login { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("culture")]
    public string? Culture { get; set; }

    [JsonProperty("created")]
    public DateTime Created { get; set; }

    [JsonProperty("latestStateChange")]
    public DateTime LatestStateChange { get; set; }

    [JsonProperty("state")]
    public string? State { get; set; }

    [JsonProperty("roles")]
    public List<string>? Roles { get; set; }

    [JsonProperty("latestLogins")]
    public List<LatestLoginData>? LatestLogins { get; set; }
    
    public DateTime GetLastLogin(GameType game) => LatestLogins?.FirstOrDefault(a => a.Game == game.GetNebulaGame())?.Timestamp ?? default;
}

public class LatestLoginData
{
    [JsonProperty("game")]
    public string? Game { get; set; }

    [JsonProperty("timestamp")]
    public DateTime Timestamp { get; set; }
}
