<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>preview</LangVersion>
        <SelfContained>true</SelfContained>
        <Description>- pluton - cydolo - cbkdz -</Description>
        <WindowsAppSdkDeploymentManagerInitialize>false</WindowsAppSdkDeploymentManagerInitialize>
        <TargetFramework>net10.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="Hub\Voice\Music\Music.Join.cs" />
    </ItemGroup>

    <ItemGroup>
        <None Include="Hub\Voice\Music\Music.Join.cs" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Dolo.Bot.Shard\Dolo.Bot.Shard.csproj" />
        <ProjectReference Include="..\Dolo.Database\Dolo.Database.csproj" />
        <ProjectReference Include="..\Dolo.Planet\Dolo.Planet.csproj" />
        <ProjectReference Include="..\Dolo.Pluto.Shard\Dolo.Pluto.Shard.csproj" />
        <ProjectReference Include="..\Dolo\Dolo.csproj" />
    </ItemGroup>
</Project>
