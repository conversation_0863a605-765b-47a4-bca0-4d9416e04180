using System.Reflection;

namespace Dolo.Core.Cryption;

public static class Decrypt
{
    /// <summary>
    ///     Method to decrypt RC2 with a custom made password
    /// </summary>
    /// <param name="input"></param>
    /// <param name="password"></param>
    /// <returns></returns>
    [Obfuscation(Feature = "renaming", Exclude = true)]
    [Obfuscation(Feature = "virtualization", Exclude = true)]
    [Obfuscation(Feature = "constants", Exclude = true)]
    public static string? Rc2Decrypt(string? input, string? password)
        => TryIt.This(() =>
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(password)) return default;

            var inputArray = System.Convert.FromBase64String(input.Replace("$1", "+").Replace("$3", "/").Replace("$5", "="));
            var keyArray = Encoding.UTF8.GetBytes(password);

            using var tripleDes = TripleDES.Create();

            tripleDes.Key = MD5.HashData(keyArray);
            tripleDes.Mode = CipherMode.ECB;
            tripleDes.Padding = PaddingMode.PKCS7;

            using var cTransform = tripleDes.CreateDecryptor();
            return Encoding.UTF8.GetString(cTransform.TransformFinalBlock(inputArray, 0, inputArray.Length));
        });
}