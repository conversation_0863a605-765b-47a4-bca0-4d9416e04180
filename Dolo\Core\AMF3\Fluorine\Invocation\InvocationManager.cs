﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Invocation;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class InvocationManager : IInvocationManager
{

    public InvocationManager()
    {
        Context = new();
        Properties = new();
    }

    #region IInvocationManager Members

    public Stack<object> Context
    {
        get;
    }

    public Dictionary<object, object> Properties
    {
        get;
    }

    public object Result
    {
        get;
        set;
    }

    #endregion
}