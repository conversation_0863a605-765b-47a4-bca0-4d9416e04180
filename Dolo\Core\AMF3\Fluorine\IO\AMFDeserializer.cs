﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
[Obfuscation(Feature = "apply to member * when method: renaming", Exclude = true)]
[Obfuscation(Feature = "internalization", Exclude = true)]
internal class AMFDeserializer : AMFReader
{
    private readonly List<AMFBody> _failedAMFBodies = new(1);

    /// <summary>
    ///     Initializes a new instance of the AMFDeserializer class.
    /// </summary>
    /// <param name="stream"></param>
    public AMFDeserializer(Stream stream) : base(stream) => FaultTolerancy = true;

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>

    public AMFBody[] FailedAMFBodies => _failedAMFBodies.ToArray();

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <returns></returns>
    public AMFMessage ReadAMFMessage()
    {
        try
        {
            // Version stored in the first two bytes.
            var version = base.ReadUInt16();
            var message = new AMFMessage(version);
            // Read header count.
            int headerCount = base.ReadUInt16();
            for (var i = 0; i < headerCount; i++) message.AddHeader(ReadHeader());
            // Read header count.
            int bodyCount = base.ReadUInt16();
            for (var i = 0; i < bodyCount; i++)
            {
                var amfBody = ReadBody();
                if (amfBody != null)//not failed
                    message.AddBody(amfBody);
            }

            return message;        }
        catch (Exception e)
        {
            Console.WriteLine($"🔍 AMF Deserializer Error: {e.Message}");
            if (e.StackTrace != null)
            {
                Console.WriteLine($"🔍 Stack Trace: {e.StackTrace}");
            }
            return new();
        }
    }

    private AMFHeader ReadHeader()
    {
        Reset();
        // Read name.
        var name = base.ReadString();
        // Read must understand flag.
        var mustUnderstand = base.ReadBoolean();
        // Read the length of the header.
        var length = base.ReadInt32();
        // Read content.
        var content = ReadData();
        return new(name, mustUnderstand, content);
    }

    private AMFBody ReadBody()
    {
        Reset();
        var target = base.ReadString();
        // Response that the client understands.
        var response = base.ReadString();
        var length = base.ReadInt32();
        if (base.BaseStream.CanSeek)
        {
            var position = base.BaseStream.Position;
            // Read content.
            try
            {
                var content = ReadData();
                var amfBody = new AMFBody(target, response, content);
                var exception = LastError;
                if (exception != null)
                {
                    var errorResponseBody = GetErrorBody(amfBody, exception);
                    _failedAMFBodies.Add(errorResponseBody);
                    return null;
                }

                return amfBody;
            }
            catch (Exception exception)
            {
                base.BaseStream.Position = position + length;
                //Try to build a valid response from partialy deserialized amf body
                var amfBody = new AMFBody(target, response, null);
                var errorResponseBody = GetErrorBody(amfBody, exception);
                _failedAMFBodies.Add(errorResponseBody);
                return amfBody;
            }
        }

        try
        {
            var content = ReadData();
            var amfBody = new AMFBody(target, response, content);
            var exception = LastError;
            if (exception != null)
            {
                var errorResponseBody = GetErrorBody(amfBody, exception);
                _failedAMFBodies.Add(errorResponseBody);
                return amfBody;
            }

            return amfBody;
        }
        catch (Exception exception)
        {
            //Try to build a valid response from partialy deserialized amf body
            var amfBody = new AMFBody(target, response, null);
            var errorResponseBody = GetErrorBody(amfBody, exception);
            _failedAMFBodies.Add(errorResponseBody);
            throw;
        }
    }

    private ErrorResponseBody GetErrorBody(AMFBody amfBody, Exception exception)
    {
        ErrorResponseBody errorResponseBody = null;
        try
        {
            var content = amfBody.Content;
            if (content is IList)
                #pragma warning disable CS8602// Dereference of a possibly null reference.
                content = (content as IList)[0];
            #pragma warning restore CS8602// Dereference of a possibly null reference.
            if (content is IMessage)
                errorResponseBody = new(amfBody, content as IMessage, exception);
            else
                errorResponseBody = new(amfBody, exception);
        }
        catch
        {
            errorResponseBody = new(amfBody, exception);
        }

        return errorResponseBody;
    }
}
