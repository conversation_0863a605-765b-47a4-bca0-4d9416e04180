namespace Dolo.Core.AMF3;

internal static class AMFTicketGenerator
{
    private static readonly object TmpObject = new();

    /// <summary>
    ///     Generates a Hash for the Ticket
    /// </summary>
    /// <returns></returns>
    internal static AMFTicket GetTicket()
    {
        lock (TmpObject)
        {
            var header = new AMFTicket();
            var number = RandomNumberGenerator.GetInt32(100, 1000).ToString();
            return header.SetTicket(BitConverter.ToString(MD5.HashData(Encoding.UTF8.GetBytes(number))).Replace("-", "").ToLower() + Secret(number));
        }
    }

    /// <summary>
    ///     Generates a Secret Number for the end of the Ticket
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    private static string Secret(string data)
    {
        var rs = string.Empty;
        return Encoding.UTF8.GetBytes(data)
            .Aggregate(rs, (current, t)
                => current + System.Convert.ToString(t, 16));
    }
}
