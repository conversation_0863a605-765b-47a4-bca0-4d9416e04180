﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Messaging;

/// <summary>
///     Message component handles out-of-band control messages.
/// </summary>
internal interface IMessageComponent
{
    /// <summary>
    ///     Handles out-of-band control message.
    /// </summary>
    /// <param name="source">Message component source.</param>
    /// <param name="pipe">Connection pipe.</param>
    /// <param name="oobCtrlMsg">Out-of-band control message</param>
    void OnOOBControlMessage(IMessageComponent source, IPipe pipe, OOBControlMessage oobCtrlMsg);
}