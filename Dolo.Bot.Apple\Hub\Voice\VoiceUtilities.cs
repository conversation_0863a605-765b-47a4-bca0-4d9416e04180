using System.Collections.Concurrent;
using Dolo.Pluto.Shard.Bot.Apple;

namespace Dolo.Bot.Apple.Hub.Voice;

public static class VoiceUtilities
{
    // Rate limiting for channel creation (user ID -> last creation time)
    private static readonly ConcurrentDictionary<ulong, DateTime> _channelCreationCooldowns = new();
    
    // Rate limiting for moderation actions (user ID -> last action time)
    private static readonly ConcurrentDictionary<ulong, DateTime> _moderationActionCooldowns = new();
    
    // Cooldown periods
    private static readonly TimeSpan ChannelCreationCooldown = TimeSpan.FromMinutes(2);
    private static readonly TimeSpan ModerationActionCooldown = TimeSpan.FromSeconds(5);

    /// <summary>
    /// Check if a user can create a voice channel (rate limiting)
    /// </summary>
    public static bool CanCreateChannel(ulong userId)
    {
        if (!_channelCreationCooldowns.TryGetValue(userId, out var lastCreation))
            return true;

        return DateTime.UtcNow - lastCreation >= ChannelCreationCooldown;
    }

    /// <summary>
    /// Record that a user has created a channel
    /// </summary>
    public static void RecordChannelCreation(ulong userId)
    {
        _channelCreationCooldowns[userId] = DateTime.UtcNow;
    }

    /// <summary>
    /// Get remaining cooldown time for channel creation
    /// </summary>
    public static TimeSpan GetChannelCreationCooldown(ulong userId)
    {
        if (!_channelCreationCooldowns.TryGetValue(userId, out var lastCreation))
            return TimeSpan.Zero;

        var elapsed = DateTime.UtcNow - lastCreation;
        return elapsed >= ChannelCreationCooldown ? TimeSpan.Zero : ChannelCreationCooldown - elapsed;
    }

    /// <summary>
    /// Check if a user can perform a moderation action (rate limiting)
    /// </summary>
    public static bool CanPerformModerationAction(ulong userId)
    {
        if (!_moderationActionCooldowns.TryGetValue(userId, out var lastAction))
            return true;

        return DateTime.UtcNow - lastAction >= ModerationActionCooldown;
    }

    /// <summary>
    /// Record that a user has performed a moderation action
    /// </summary>
    public static void RecordModerationAction(ulong userId)
    {
        _moderationActionCooldowns[userId] = DateTime.UtcNow;
    }

    /// <summary>
    /// Validate channel name for inappropriate content
    /// </summary>
    public static bool IsValidChannelName(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return false;

        // Remove the prefix for validation
        var cleanName = name.StartsWith("🌸 » ") ? name[5..] : name;
        
        // Check length
        if (cleanName.Length > 90) // Leave room for prefix
            return false;

        // Check for inappropriate content (basic filter)
        var lowerName = cleanName.ToLowerInvariant();
        var bannedWords = new[] { "discord", "everyone", "here", "@", "#", "http", "www", ".com", ".net", ".org" };
        
        return !bannedWords.Any(word => lowerName.Contains(word));
    }

    /// <summary>
    /// Log moderation action for audit purposes
    /// </summary>
    public static void LogModerationAction(string action, ulong moderatorId, ulong targetId, ulong channelId, string? reason = null)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC");
        var logMessage = $"[Voice Moderation] {timestamp} - {action} by {moderatorId} on {targetId} in channel {channelId}";
        
        if (!string.IsNullOrEmpty(reason))
            logMessage += $" - Reason: {reason}";
            
        Console.WriteLine(logMessage);
    }

    /// <summary>
    /// Check if a user has permission to moderate a voice channel
    /// </summary>
    public static bool CanModerateChannel(VoiceUser voiceChannel, ulong userId, DiscordMember? member = null)
    {
        // Owner can always moderate
        if (voiceChannel.Owner == userId)
            return true;

        // Moderators can moderate
        if (voiceChannel.Moderator.Contains(userId))
            return true;

        // Server admins can moderate
        if (member?.Roles.Contains(HubRoles.Admin) == true)
            return true;

        return false;
    }

    /// <summary>
    /// Check if a target user can be moderated
    /// </summary>
    public static bool CanModerateUser(DiscordMember target, DiscordMember moderator, VoiceUser voiceChannel)
    {
        // Cannot moderate server admins or energy role
        if (target.Roles.ContainsMany(HubRoles.Admin, HubRoles.Energy))
            return false;

        // Cannot moderate the channel owner (unless you're an admin)
        if (voiceChannel.Owner == target.Id && !moderator.Roles.Contains(HubRoles.Admin))
            return false;

        // Cannot moderate yourself
        if (target.Id == moderator.Id)
            return false;

        return true;
    }

    /// <summary>
    /// Clean up old cooldown entries to prevent memory leaks
    /// </summary>
    public static void CleanupOldCooldowns()
    {
        var cutoffTime = DateTime.UtcNow - TimeSpan.FromHours(1);
        
        var expiredChannelCreations = _channelCreationCooldowns
            .Where(kvp => kvp.Value < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();
            
        var expiredModerationActions = _moderationActionCooldowns
            .Where(kvp => kvp.Value < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var userId in expiredChannelCreations)
            _channelCreationCooldowns.TryRemove(userId, out _);
            
        foreach (var userId in expiredModerationActions)
            _moderationActionCooldowns.TryRemove(userId, out _);
    }
}
