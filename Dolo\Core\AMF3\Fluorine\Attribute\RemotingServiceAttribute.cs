﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Attribute;

/// <summary>
///     Indicates whether a type is a remoting service. This class cannot be inherited.
/// </summary>
[AttributeUsage(AttributeTargets.Class)]
internal sealed class RemotingServiceAttribute : System.Attribute
{
    private string _serviceName;

    /// <summary>
    ///     Initializes a new instance of the RemotingServiceAttribute class.
    /// </summary>
    public RemotingServiceAttribute()
    {}
    /// <summary>
    ///     Initializes a new instance of the RemotingServiceAttribute class.
    /// </summary>
    /// <param name="serviceName">Specifies a description for the remoting service.</param>
    public RemotingServiceAttribute(string serviceName) => _serviceName = serviceName;
}