﻿#pragma warning disable CS8604

namespace Dolo.Core.Discord;
public static class DiscordMemberExtension
{
    /// <summary>
    ///     Try to send a direct message to a member.
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="content">The content of the message.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task<bool> TryDmAsync(this DiscordMember member, string? content, Action<Exception>? onError = null)
    {
        var dm = await member.CreateDmChannelAsync();
        if (dm is null) return false;

        var message = await dm.SendMessageAsync(content).TryAsync(onError);
        return message is { };
    }

    /// <summary>
    ///     Try to kick a member from the guild.
    /// </summary>
    /// <param name="member">The Discord member to kick.</param>
    /// <param name="reason">The reason for kicking.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryKickAsync(this DiscordMember member, string? reason = null, Action<Exception>? onError = null)
    {
        await member.RemoveAsync(reason).TryAsync(onError);
    }

    /// <summary>
    ///     Try to ban a member from the guild.
    /// </summary>
    /// <param name="member">The Discord member to ban.</param>
    /// <param name="duration">The duration of the ban.</param>
    /// <param name="reason">The reason for banning.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryBanAsync(this DiscordMember member, TimeSpan duration, string? reason = null, Action<Exception>? onError = null)
    {
        await member.BanAsync(duration, reason).TryAsync(onError);
    }

    /// <summary>
    ///     Try to send a direct message to a member.
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="content">The content of the message.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordMember member, string content, Action<Exception>? onError = null)
        => await member.SendMessageAsync(content).TryAsync(onError);

    /// <summary>
    ///     Try to send a direct message to a member.
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="embed">The embed to send.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordMember member, DiscordEmbed embed, Action<Exception>? onError = null)
        => await member.SendMessageAsync(embed).TryAsync(onError);

    /// <summary>
    ///     Try to send a direct message to a member.
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="content">The content of the message.</param>
    /// <param name="embed">The embed to send.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordMember member, string content, DiscordEmbed embed, Action<Exception>? onError = null)
        => await member.SendMessageAsync(content, embed).TryAsync(onError);

    /// <summary>
    ///     Try to send a direct message to a member.
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="builder">The message builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordMember member, DiscordMessageBuilder builder, Action<Exception>? onError = null)
        => await member.SendMessageAsync(builder).TryAsync(onError);

    /// <summary>
    ///     Try to modify a member
    /// </summary>
    /// <param name="member">The Discord member to modify.</param>
    /// <param name="model">The action to configure member edit model.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryModifyAsync(this DiscordMember member, Action<MemberEditModel> model, Action<Exception>? onError = null)
    {
        await member.ModifyAsync(model).TryAsync(onError);
    }

    /// <summary>
    ///     Try to timeout a member using seconds
    /// </summary>
    /// <param name="member">The Discord member to timeout.</param>
    /// <param name="seconds">The timeout duration in seconds.</param>
    /// <param name="reason">The reason for the timeout.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryTimeoutAsync(this DiscordMember member, int seconds, string? reason = default, Action<Exception>? onError = null)
    {
        await member.TimeoutAsync(DateTimeOffset.Now.AddSeconds(seconds), reason).TryAsync(onError);
    }

    /// <summary>
    ///     Try to timeout a member using seconds
    /// </summary>
    /// <param name="member">The Discord member to timeout.</param>
    /// <param name="span">The timeout duration as TimeSpan.</param>
    /// <param name="reason">The reason for the timeout.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryTimeoutAsync(this DiscordMember member, TimeSpan span, string? reason = default, Action<Exception>? onError = null)
    {
        await member.TimeoutAsync(DateTimeOffset.Now.Add(span), reason).TryAsync(onError);
    }

    /// <summary>
    ///     Try to timeout a member using offset
    /// </summary>
    /// <param name="member">The Discord member to timeout.</param>
    /// <param name="offset">The timeout end time.</param>
    /// <param name="reason">The reason for the timeout.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryTimeoutAsync(this DiscordMember member, DateTimeOffset offset, string? reason = default, Action<Exception>? onError = null)
    {
        await member.TimeoutAsync(offset, reason).TryAsync(onError);
    }

    /// <summary>
    ///     Try to remove timeout from a member
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryRemoveTimeoutAsync(this DiscordMember member, Action<Exception>? onError = null)
    {
        await member.TimeoutAsync(null).TryAsync(onError);
    }

    /// <summary>
    ///     Try to revoke a role from a member
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="role">The role to revoke.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryRevokeAsync(this DiscordMember member, DiscordRole? role, Action<Exception>? onError = null)
    {
        await member.RevokeRoleAsync(role).TryAsync(onError);
    }

    /// <summary>
    ///     Try to grant a role to a member
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="role">The role to grant.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryGrantRoleAsync(this DiscordMember member, DiscordRole? role, Action<Exception>? onError = null)
    {
        await member.GrantRoleAsync(role).TryAsync(onError);
    }

    /// <summary>
    ///     Try to revoke all roles from a member
    /// </summary>
    /// <param name="guild">The Discord guild.</param>
    /// <param name="user">The Discord member.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryRevokeAllAsync(this DiscordGuild guild, DiscordMember user, Action<Exception>? onError = null)
    {
        if (!guild.TryGetMember(user.Id, out var member))
            return;

        // try to revoke all the roles
        foreach (var role in member.Roles.ToList())
            await member.TryRevokeAsync(role, onError);
    }

    /// <summary>
    ///     Try to revoke all roles from a member
    /// </summary>
    /// <param name="guild">The Discord guild.</param>
    /// <param name="user">The Discord user.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryRevokeAllAsync(this DiscordGuild guild, DiscordUser user, Action<Exception>? onError = null)
    {
        if (!guild.TryGetMember(user.Id, out var member))
            return;

        // try to revoke all the roles
        foreach (var role in member.Roles.ToList())
            await member.TryRevokeAsync(role, onError);
    }

    /// <summary>
    ///     Try to delete many messages of multiple channels from a member
    /// </summary>
    /// <param name="member">The Discord member.</param>
    /// <param name="channels">The channels to delete messages from.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryDeleteManyMessageAsync(this DiscordMember member, Action<Exception>? onError = null, params DiscordChannel?[] channels)
    {
        foreach (var channel in channels)
        {
            // continue when channel is null
            if (channel is null)
                continue;

            // fetch messages
            var msgs = await channel.TryGetMessagesAsync(200);

            // filter message by timestamp and id of member
            var filtered = msgs?.Where(a => a.Author!.Id == member.Id && a.Timestamp > DateTimeOffset.Now.AddDays(-14)).ToList();

            // delete if match
            if (filtered != null && filtered.Count != 0)
                await channel.TryDeleteMessageAsync(filtered);
        }
    }
}