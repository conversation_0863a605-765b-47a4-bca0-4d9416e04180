using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Apple;
using System.Text;
namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("info")]
    [Description("get detailed information about the current voice channel")]
    public async Task VoiceInfoAsync(SlashCommandContext ctx)
    {
        if (Hub.Guild is null)
            return;

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        // get the channel database entry
        var voiceChannel = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (voiceChannel is null)
        {
            await ctx.TryCreateResponseAsync("Voice channel data not found.", true);
            return;
        }

        // get the voice channel
        var channel = Hub.Guild.TryGetChannel(voiceChannel.Channel);
        if (channel is null)
        {
            await ctx.TryCreateResponseAsync("Voice channel not found.", true);
            return;
        }

        // get owner information
        var owner = Hub.Guild.TryGetMember(voiceChannel.Owner, out var ownerMember) 
            ? $"{ownerMember.Username} ({ownerMember.Mention})" 
            : $"Unknown User ({voiceChannel.Owner})";

        // get moderators
        var moderators = new List<string>();
        foreach (var modId in voiceChannel.Moderator)
        {
            if (Hub.Guild.TryGetMember(modId, out var mod))
                moderators.Add($"{mod.Username} ({mod.Mention})");
            else
                moderators.Add($"Unknown User ({modId})");
        }

        // get banned users
        var bannedUsers = new List<string>();
        foreach (var bannedId in voiceChannel.Banned)
        {
            if (Hub.Guild.TryGetMember(bannedId, out var banned))
                bannedUsers.Add($"{banned.Username} ({banned.Mention})");
            else
                bannedUsers.Add($"Unknown User ({bannedId})");
        }

        // get muted users
        var mutedUsers = new List<string>();
        foreach (var mutedId in voiceChannel.Muted)
        {
            if (Hub.Guild.TryGetMember(mutedId, out var muted))
                mutedUsers.Add($"{muted.Username} ({muted.Mention})");
            else
                mutedUsers.Add($"Unknown User ({mutedId})");
        }

        // build embed
        var embed = new DiscordEmbedBuilder()
            .WithTitle($"Voice Channel Information {HubEmoji.Astro}")
            .WithDescription(new StringBuilder()
                .AppendLine($"{HubEmoji.Apple} **» [Channel](https://a) » {channel.Name}**")
                .AppendLine($"{HubEmoji.Apple} **» [Users Online](https://a) » {channel.Users.Count()}/{(channel.UserLimit == 0 ? "∞" : channel.UserLimit.ToString())}**")
                .AppendLine($"{HubEmoji.Apple} **» [Owner](https://a) » {owner}**")
                .AppendLine($"{HubEmoji.Apple} **» [Status](https://a) » {(channel.PermissionOverwrites.Any(p => p.Id == Hub.Guild.EveryoneRole.Id && p.Denied.HasPermission(DiscordPermission.Connect)) ? "🔒 Locked" : "🔓 Unlocked")}**")
                .AppendLine()
                .AppendLine("**Channel Details**")
                .AppendLine($"**Created:** <t:{((DateTimeOffset)channel.CreationTimestamp).ToUnixTimeSeconds()}:R>")
                .AppendLine($"**Channel ID:** `{channel.Id}`")
                .AppendLine($"**Text Channel ID:** `{ctx.Channel.Id}`")
                .ToString())
            .WithColor(new DiscordColor("D6EDFB"))
            .WithThumbnail("https://cdn.cbkdz.eu/img/iMusic.jpeg");

        if (moderators.Any())
            embed.AddField($"{HubEmoji.WhiteCrown} Moderators ({moderators.Count})", string.Join("\n", moderators.Take(10)) + (moderators.Count > 10 ? $"\n... and {moderators.Count - 10} more" : ""), false);

        if (bannedUsers.Any())
            embed.AddField($"{HubEmoji.No} Banned Users ({bannedUsers.Count})", string.Join("\n", bannedUsers.Take(5)) + (bannedUsers.Count > 5 ? $"\n... and {bannedUsers.Count - 5} more" : ""), false);

        if (mutedUsers.Any())
            embed.AddField($"{HubEmoji.Shrug} Muted Users ({mutedUsers.Count})", string.Join("\n", mutedUsers.Take(5)) + (mutedUsers.Count > 5 ? $"\n... and {mutedUsers.Count - 5} more" : ""), false);

        embed.WithFooter($"Use /voice help for available commands {HubEmoji.WhiteHeart}");

        await ctx.TryCreateResponseAsync(embed.Build());
    }

    [Command("status")]
    [Description("get a quick status overview of your voice channel permissions")]
    public async Task VoiceStatusAsync(SlashCommandContext ctx)
    {
        if (Hub.Guild is null)
            return;

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        // get the channel database entry
        var voiceChannel = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (voiceChannel is null)
        {
            await ctx.TryCreateResponseAsync("Voice channel data not found.", true);
            return;
        }

        var member = Hub.Guild.TryGetMember(ctx.User.Id, out var guildMember) ? guildMember : null;
        var isOwner = voiceChannel.Owner == ctx.User.Id;
        var isModerator = voiceChannel.Moderator.Contains(ctx.User.Id);
        var isBanned = voiceChannel.Banned.Contains(ctx.User.Id);
        var isMuted = voiceChannel.Muted.Contains(ctx.User.Id);
        var isAdmin = member?.Roles.Contains(HubRoles.Admin) == true;

        var status = new List<string>();
        
        if (isOwner)
            status.Add("👑 **Channel Owner** - Full control over the channel");
        else if (isModerator)
            status.Add("🛡️ **Channel Moderator** - Can manage users and settings");
        else if (isAdmin)
            status.Add("⚡ **Server Admin** - Can override channel restrictions");
        else
            status.Add("👤 **Regular User** - Basic channel access");

        if (isBanned)
            status.Add("🚫 **Banned** - Cannot join the voice channel");
        
        if (isMuted)
            status.Add("🔇 **Muted** - Cannot send messages in text channel");

        // Available actions
        var actions = new List<string>();
        
        if (isOwner || isModerator || isAdmin)
        {
            actions.Add("• Kick users from voice channel");
            actions.Add("• Ban/unban users from voice channel");
            actions.Add("• Mute/unmute users in text channel");
            actions.Add("• Lock/unlock voice channel");
            actions.Add("• Set user limit");
            actions.Add("• Change channel name");
        }
        
        if (isOwner)
        {
            actions.Add("• Add/remove moderators");
            actions.Add("• Transfer channel ownership");
        }

        var embed = new DiscordEmbedBuilder()
            .WithTitle($"Your Voice Channel Status {HubEmoji.Astro}")
            .WithDescription(new StringBuilder()
                .AppendLine(string.Join("\n", status))
                .AppendLine()
                .ToString())
            .WithColor(new DiscordColor("D6EDFB"))
            .WithThumbnail("https://cdn.cbkdz.eu/img/iMusic.jpeg");

        if (actions.Any())
            embed.AddField($"{HubEmoji.Apple} Available Actions", string.Join("\n", actions), false);

        embed.WithFooter($"Use /voice help for command details {HubEmoji.WhiteHeart}");

        await ctx.TryCreateResponseAsync(embed.Build());
    }

    [Command("stats")]
    [Description("get detailed statistics for the current voice channel")]
    public async Task VoiceStatsAsync(SlashCommandContext ctx)
    {
        if (Hub.Guild is null)
            return;

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        // get the channel database entry
        var voiceChannel = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (voiceChannel is null)
        {
            await ctx.TryCreateResponseAsync("Voice channel data not found.", true);
            return;
        }

        // get channel statistics
        var stats = VoiceActivityMonitor.GetChannelStats(voiceChannel.Channel);
        var isInactive = VoiceActivityMonitor.IsChannelInactive(voiceChannel.Channel);

        var embed = new DiscordEmbedBuilder()
            .WithTitle($"Voice Channel Statistics {HubEmoji.Astro}")
            .WithColor(new DiscordColor("D6EDFB"))
            .WithThumbnail("https://cdn.cbkdz.eu/img/iMusic.jpeg");

        if (stats != null)
        {
            var uptime = DateTime.UtcNow - stats.CreatedAt;
            var lastActivity = DateTime.UtcNow - stats.LastActivity;

            embed.WithDescription(new StringBuilder()
                .AppendLine($"{HubEmoji.Apple} **» [Channel Age](https://a) » {uptime.Days}d {uptime.Hours}h {uptime.Minutes}m**")
                .AppendLine($"{HubEmoji.Apple} **» [Last Activity](https://a) » {(lastActivity.TotalMinutes < 1 ? "Just now" : $"{lastActivity.TotalMinutes:F0}m ago")}**")
                .AppendLine($"{HubEmoji.Apple} **» [Total Joins](https://a) » {stats.TotalJoins:N0}**")
                .AppendLine($"{HubEmoji.Apple} **» [Messages Sent](https://a) » {stats.TotalMessages:N0}**")
                .AppendLine($"{HubEmoji.Apple} **» [Peak Users](https://a) » {stats.MaxConcurrentUsers}**")
                .AppendLine($"{HubEmoji.Apple} **» [Unique Users](https://a) » {stats.UniqueUsers.Count}**")
                .AppendLine()
                .ToString());

            if (isInactive)
                embed.AddField($"{HubEmoji.No} Channel Status", "⚠️ Inactive - May be cleaned up automatically", false);
        }
        else
        {
            embed.WithDescription($"{HubEmoji.Shrug} No statistics available for this channel yet.");
        }

        // Get overall system stats
        var (activeChannels, totalChannels, totalUsers) = VoiceActivityMonitor.GetOverallStats();
        embed.AddField($"{HubEmoji.Astro} System Statistics", $"**Active Channels:** {activeChannels}\n**Total Channels:** {totalChannels}\n**Total Users:** {totalUsers:N0}", false);

        await ctx.TryCreateResponseAsync(embed.Build());
    }
}
