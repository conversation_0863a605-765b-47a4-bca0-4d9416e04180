﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Attribute;
using Dolo.Core.AMF3.Fluorine.IO.Writers;
using Dolo.Core.AMF3.Fluorine.IO.Writers.AMF0;
using Dolo.Core.AMF3.Fluorine.IO.Writers.AMF3;
using System.Xml;
using System.Xml.Linq;
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
public class AMFWriter
{
    private Dictionary<object, int> _amf0ObjectReferences;
    private Dictionary<object, int> _objectReferences;
    private Dictionary<object, int> _stringReferences;
    private Dictionary<ClassDefinition, int> _classDefinitionReferences;
    private static readonly Dictionary<string, ClassDefinition> classDefinitions;
    private static readonly Dictionary<Type, IAMFWriter>[] AmfWriterTable;
    public BinaryWriter BinaryWriter;

    static AMFWriter()
    {
        var amf0Writers = new Dictionary<Type, IAMFWriter>();
        var amf0NumberWriter = new AMF0NumberWriter();
        amf0Writers.Add(typeof(sbyte), amf0NumberWriter);
        amf0Writers.Add(typeof(byte), amf0NumberWriter);
        amf0Writers.Add(typeof(short), amf0NumberWriter);
        amf0Writers.Add(typeof(ushort), amf0NumberWriter);
        amf0Writers.Add(typeof(int), amf0NumberWriter);
        amf0Writers.Add(typeof(uint), amf0NumberWriter);
        amf0Writers.Add(typeof(long), amf0NumberWriter);
        amf0Writers.Add(typeof(ulong), amf0NumberWriter);
        amf0Writers.Add(typeof(float), amf0NumberWriter);
        amf0Writers.Add(typeof(double), amf0NumberWriter);
        amf0Writers.Add(typeof(decimal), amf0NumberWriter);
        amf0Writers.Add(typeof(DBNull), new AMF0NullWriter());
        amf0Writers.Add(typeof(CacheableObject), new AMF0CacheableObjectWriter());
        amf0Writers.Add(typeof(XmlDocument), new AMF0XmlDocumentWriter());
        amf0Writers.Add(typeof(RawBinary), new RawBinaryWriter());
        amf0Writers.Add(typeof(NameObjectCollectionBase), new AMF0NameObjectCollectionWriter());
        amf0Writers.Add(typeof(XDocument), new AMF0XDocumentWriter());
        amf0Writers.Add(typeof(XElement), new AMF0XElementWriter());
        amf0Writers.Add(typeof(Guid), new AMF0GuidWriter());
        amf0Writers.Add(typeof(string), new AMF0StringWriter());
        amf0Writers.Add(typeof(bool), new AMF0BooleanWriter());
        amf0Writers.Add(typeof(Enum), new AMF0EnumWriter());
        amf0Writers.Add(typeof(char), new AMF0CharWriter());
        amf0Writers.Add(typeof(DateTime), new AMF0DateTimeWriter());
        amf0Writers.Add(typeof(Array), new AMF0ArrayWriter());
        amf0Writers.Add(typeof(ASObject), new AMF0ASObjectWriter());

        var amf3Writers = new Dictionary<Type, IAMFWriter>();
        var amf3IntWriter = new AMF3IntWriter();
        var amf3DoubleWriter = new AMF3DoubleWriter();
        amf3Writers.Add(typeof(sbyte), amf3IntWriter);
        amf3Writers.Add(typeof(byte), amf3IntWriter);
        amf3Writers.Add(typeof(short), amf3IntWriter);
        amf3Writers.Add(typeof(ushort), amf3IntWriter);
        amf3Writers.Add(typeof(int), amf3IntWriter);
        amf3Writers.Add(typeof(uint), amf3IntWriter);
        amf3Writers.Add(typeof(long), amf3DoubleWriter);
        amf3Writers.Add(typeof(ulong), amf3DoubleWriter);
        amf3Writers.Add(typeof(float), amf3DoubleWriter);
        amf3Writers.Add(typeof(double), amf3DoubleWriter);
        amf3Writers.Add(typeof(decimal), amf3DoubleWriter);
        amf3Writers.Add(typeof(DBNull), new AMF3DBNullWriter());
        amf3Writers.Add(typeof(CacheableObject), new AMF3CacheableObjectWriter());
        amf3Writers.Add(typeof(XmlDocument), new AMF3XmlDocumentWriter());
        amf3Writers.Add(typeof(RawBinary), new RawBinaryWriter());
        amf3Writers.Add(typeof(NameObjectCollectionBase), new AMF3NameObjectCollectionWriter());
        amf3Writers.Add(typeof(XDocument), new AMF3XDocumentWriter());
        amf3Writers.Add(typeof(XElement), new AMF3XElementWriter());
        amf3Writers.Add(typeof(Guid), new AMF3GuidWriter());
        amf3Writers.Add(typeof(string), new AMF3StringWriter());
        amf3Writers.Add(typeof(bool), new AMF3BooleanWriter());
        amf3Writers.Add(typeof(Enum), new AMF3EnumWriter());
        amf3Writers.Add(typeof(char), new AMF3CharWriter());
        amf3Writers.Add(typeof(DateTime), new AMF3DateTimeWriter());
        amf3Writers.Add(typeof(Array), new AMF3ArrayWriter());
        amf3Writers.Add(typeof(ASObject), new AMF3ASObjectWriter());
        amf3Writers.Add(typeof(ByteArray), new AMF3ByteArrayWriter());
        amf3Writers.Add(typeof(byte[]), new AMF3ByteArrayWriter());
        AmfWriterTable = [amf0Writers, null, null, amf3Writers];
        classDefinitions = new();
    }

    /// <summary>
    ///     Initializes a new instance of the AMFReader class based on the supplied stream and using UTF8Encoding.
    /// </summary>
    /// <param name="stream"></param>
    public AMFWriter()
    {
        Reset();
    }

    public void SetBinary(Stream stream) => BinaryWriter = new(stream);

    internal AMFWriter(AMFWriter writer, Stream stream)
    {
        BinaryWriter = new(stream);
        _amf0ObjectReferences = writer._amf0ObjectReferences;
        _objectReferences = writer._objectReferences;
        _stringReferences = writer._stringReferences;
        _classDefinitionReferences = writer._classDefinitionReferences;
        UseLegacyCollection = writer.UseLegacyCollection;
    }

    /// <summary>
    ///     Resets object references.
    /// </summary>
    public void Reset()
    {
        _amf0ObjectReferences = new(5);
        _objectReferences = new(5);
        _stringReferences = new(5);
        _classDefinitionReferences = new();
    }

    /// <summary>
    ///     Gets or sets whether legacy collection serialization is used for AMF3.
    /// </summary>
    public bool UseLegacyCollection { get; set; } = true;

    /// <summary>
    ///     Writes a byte to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A byte to write to the stream.</param>
    public void WriteByte(byte value)
    {
        BinaryWriter.BaseStream.WriteByte(value);
    }

    /// <summary>
    ///     Writes a stream of bytes to the current position in the AMF stream.
    /// </summary>
    /// <param name="buffer">The memory buffer containing the bytes to write to the AMF stream</param>
    public void WriteBytes(byte[] buffer)
    {
        for (var i = 0; buffer != null && i < buffer.Length; i++)
            BinaryWriter.BaseStream.WriteByte(buffer[i]);
    }

    /// <summary>
    ///     Writes a 16-bit unsigned integer to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    public void WriteShort(int value)
    {
        var bytes = BitConverter.GetBytes((ushort)value);
        WriteBigEndian(bytes);
    }

    /// <summary>
    ///     Writes an UTF-8 string to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">The UTF-8 string.</param>
    /// <remarks>Standard or long string header is written depending on the string length.</remarks>
    public void WriteString(string value)
    {
        var utf8Encoding = new UTF8Encoding(true, true);
        var byteCount = utf8Encoding.GetByteCount(value);
        if (byteCount < 65536)
        {
            WriteByte(AMF0TypeCode.String);
            WriteUTF(value);
        }
        else
        {
            WriteByte(AMF0TypeCode.LongString);
            WriteLongUTF(value);
        }
    }

    /// <summary>
    ///     Writes a UTF-8 string to the current position in the AMF stream.
    ///     The length of the UTF-8 string in bytes is written first, as a 16-bit integer, followed by the bytes representing
    ///     the characters of the string.
    /// </summary>
    /// <param name="value">The UTF-8 string.</param>
    /// <remarks>Standard or long string header is not written.</remarks>
    public void WriteUTF(string value)
    {
        //null string is not accepted
        //in case of custom serialization leads to TypeError: Error #2007: Parameter value must be non-null.  at flash.utils::ObjectOutput/writeUTF()

        //Length - max 65536.
        var utf8Encoding = new UTF8Encoding();
        var byteCount = utf8Encoding.GetByteCount(value);
        var buffer = utf8Encoding.GetBytes(value);
        WriteShort(byteCount);
        if (buffer.Length > 0)
            BinaryWriter.BaseStream.Write(buffer);
    }

    /// <summary>
    ///     Writes a UTF-8 string to the current position in the AMF stream.
    ///     Similar to WriteUTF, but does not prefix the string with a 16-bit length word.
    /// </summary>
    /// <param name="value">The UTF-8 string.</param>
    /// <remarks>Standard or long string header is not written.</remarks>
    public void WriteUTFBytes(string value)
    {
        //Length - max 65536.
        var utf8Encoding = new UTF8Encoding();
        var buffer = utf8Encoding.GetBytes(value);
        if (buffer.Length > 0)
            BinaryWriter.BaseStream.Write(buffer);
    }

    private void WriteLongUTF(string value)
    {
        var utf8Encoding = new UTF8Encoding(true, true);
        var byteCount = (uint)utf8Encoding.GetByteCount(value);
        var buffer = new byte[byteCount + 4];
        //unsigned long (always 32 bit, big endian byte order)
        buffer[0] = (byte)(byteCount >> 0x18 & 0xff);
        buffer[1] = (byte)(byteCount >> 0x10 & 0xff);
        buffer[2] = (byte)(byteCount >> 8    & 0xff);
        buffer[3] = (byte)(byteCount         & 0xff);
        var bytesEncodedCount = utf8Encoding.GetBytes(value, 0, value.Length, buffer, 4);
        if (buffer.Length > 0)
            BinaryWriter.BaseStream.Write(buffer, 0, buffer.Length);
    }

    /// <summary>
    ///     Serializes object graphs in Action Message Format (AMF).
    /// </summary>
    /// <param name="objectEncoding">AMF version to use.</param>
    /// <param name="data">The Object to serialize in the AMF stream.</param>
    public void WriteData(ObjectEncoding objectEncoding, object data)
    {
        //If we have ObjectEncoding.AMF3 anything that serializes to String, Number, Boolean, Date will use AMF0 encoding
        //For other types we have to switch the encoding to AMF3
        if (data == null)
        {
            WriteNull();
            return;
        }

        var type = data.GetType();
        if (AMFConfiguration.Instance.AcceptNullValueTypes && AMFConfiguration.Instance.NullableValues != null)
            if (AMFConfiguration.Instance.NullableValues.ContainsKey(type) &&
                data.Equals(AMFConfiguration.Instance.NullableValues[type]))
            {
                WriteNull();
                return;
            }

        if (_amf0ObjectReferences.ContainsKey(data))
        {
            WriteReference(data);
            return;
        }

        IAMFWriter amfWriter = null;
        if (AmfWriterTable[0].ContainsKey(type))
            amfWriter = AmfWriterTable[0][type];
        //Second try with basetype (enums and arrays for example)
        if (amfWriter == null && AmfWriterTable[0].ContainsKey(type.BaseType))
            amfWriter = AmfWriterTable[0][type.BaseType];

        if (amfWriter == null)
            lock (AmfWriterTable)
                if (!AmfWriterTable[0].ContainsKey(type))
                {
                    amfWriter = new AMF0ObjectWriter();
                    AmfWriterTable[0].Add(type, amfWriter);
                }
                else
                    amfWriter = AmfWriterTable[0][type];

        if (amfWriter != null)
        {
            if (objectEncoding == ObjectEncoding.AMF0)
                amfWriter.WriteData(this, data);
            else
            {
                if (amfWriter.IsPrimitive)
                    amfWriter.WriteData(this, data);
                else
                {
                    WriteByte(AMF0TypeCode.AMF3Tag);
                    WriteAMF3Data(data);
                }
            }
        }
        else
        {
            var msg = __Res.GetString(__Res.TypeSerializer_NotFound, type.FullName);
            throw new AMFException(msg);
        }
    }

    internal void AddReference(object value)
    {
        _amf0ObjectReferences.Add(value, _amf0ObjectReferences.Count);
    }

    internal void WriteReference(object value)
    {
        //Circular references
        WriteByte(AMF0TypeCode.Reference);
        WriteShort(_amf0ObjectReferences[value]);
    }

    /// <summary>
    ///     Writes a null type marker to the current position in the AMF stream.
    /// </summary>
    public void WriteNull()
    {
        WriteByte(AMF0TypeCode.Null);
    }

    /// <summary>
    ///     Writes a double-precision floating point number to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteDouble(double value)
    {
        var bytes = BitConverter.GetBytes(value);
        WriteBigEndian(bytes);
    }

    /// <summary>
    ///     Writes a single-precision floating point number to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteFloat(float value)
    {
        var bytes = BitConverter.GetBytes(value);
        WriteBigEndian(bytes);
    }

    /// <summary>
    ///     Writes a 32-bit signed integer to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteInt32(int value)
    {
        var bytes = BitConverter.GetBytes(value);
        WriteBigEndian(bytes);
    }

    /// <summary>
    ///     Writes a 32-bit signed integer to the current position in the AMF stream using variable length unsigned 29-bit
    ///     integer encoding.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteUInt24(int value)
    {
        var bytes = new byte[3];
        bytes[0] = (byte)(0xFF & value >> 16);
        bytes[1] = (byte)(0xFF & value >> 8);
        bytes[2] = (byte)(0xFF & value >> 0);
        BinaryWriter.BaseStream.Write(bytes, 0, bytes.Length);
    }

    /// <summary>
    ///     Writes a Boolean value to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A Boolean value.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteBoolean(bool value)
    {
        BinaryWriter.BaseStream.WriteByte(value ? (byte)1 : (byte)0);
    }

    /// <summary>
    ///     Writes a 64-bit signed integer to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteLong(long value)
    {
        var bytes = BitConverter.GetBytes(value);
        WriteBigEndian(bytes);
    }

    private void WriteBigEndian(byte[] bytes)
    {
        if (bytes == null)
            return;
        for (var i = bytes.Length - 1; i >= 0; i--) BinaryWriter.BaseStream.WriteByte(bytes[i]);
    }

    /// <summary>
    ///     Writes a DateTime value to the current position in the AMF stream.
    ///     An ActionScript Date is serialized as the number of milliseconds elapsed since the epoch of midnight on 1st Jan
    ///     1970 in the UTC time zone.
    /// </summary>
    /// <param name="value">A DateTime value.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteDateTime(DateTime value)
    {
        value = value.ToUniversalTime();

        // Write date (milliseconds from 1970).
        var timeStart = new DateTime(1970, 1, 1);
        var span = value.Subtract(timeStart);
        var milliSeconds = (long)span.TotalMilliseconds;
        WriteDouble(milliSeconds);
        span = TimeZoneInfo.Local.GetUtcOffset(value);
        //whatever we write back, it is ignored
        //this.WriteLong(span.TotalMinutes);
        //this.WriteShort((int)span.TotalHours);
        //this.WriteShort(65236);
        if (AMFConfiguration.Instance.TimezoneCompensation == TimezoneCompensation.None)
            WriteShort(0);
        else
            WriteShort((int)(span.TotalMilliseconds / 60000));
    }

    /// <summary>
    ///     Writes an XmlDocument object to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">An XmlDocument object.</param>
    /// <remarks>Xml type marker is written in the AMF stream.</remarks>
    public void WriteXmlDocument(XmlDocument value)
    {
        if (value != null)
        {
            AddReference(value);
            BinaryWriter.BaseStream.WriteByte(AMF0TypeCode.Xml);
            var xml = value.DocumentElement.OuterXml;
            WriteLongUTF(xml);
        }
        else
            WriteNull();
    }

    public void WriteXDocument(XDocument xDocument)
    {
        if (xDocument != null)
        {
            AddReference(xDocument);
            BinaryWriter.BaseStream.WriteByte(15);//xml code (0x0F)
            var xml = xDocument.ToString();
            WriteLongUTF(xml);
        }
        else
            WriteNull();
    }

    public void WriteXElement(XElement xElement)
    {
        if (xElement != null)
        {
            AddReference(xElement);
            BinaryWriter.BaseStream.WriteByte(15);//xml code (0x0F)
            var xml = xElement.ToString();
            WriteLongUTF(xml);
        }
        else
            WriteNull();
    }

    /// <summary>
    ///     Writes an Array value to the current position in the AMF stream.
    /// </summary>
    /// <param name="objectEcoding">Object encoding used.</param>
    /// <param name="value">An Array object.</param>
    public void WriteArray(ObjectEncoding objectEcoding, Array value)
    {
        if (value == null)
            WriteNull();
        else
        {
            AddReference(value);
            WriteByte(AMF0TypeCode.Array);
            WriteInt32(value.Length);
            for (var i = 0; i < value.Length; i++) WriteData(objectEcoding, value.GetValue(i));
        }
    }

    /// <summary>
    ///     Writes an associative array to the current position in the AMF stream.
    /// </summary>
    /// <param name="objectEncoding">Object encoding used.</param>
    /// <param name="value">An Dictionary object.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteAssociativeArray(ObjectEncoding objectEncoding, IDictionary value)
    {
        if (value == null)
            WriteNull();
        else
        {
            AddReference(value);
            WriteByte(AMF0TypeCode.AssociativeArray);
            WriteInt32(value.Count);
            foreach (DictionaryEntry entry in value)
            {
                WriteUTF(entry.Key.ToString());
                WriteData(objectEncoding, entry.Value);
            }

            WriteEndMarkup();
        }
    }

    /// <summary>
    ///     Writes an object to the current position in the AMF stream.
    /// </summary>
    /// <param name="objectEncoding">Object encoding used.</param>
    /// <param name="obj">The object to serialize.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteObject(ObjectEncoding objectEncoding, object obj)
    {
        if (obj == null)
        {
            WriteNull();
            return;
        }

        AddReference(obj);

        var type = obj.GetType();

        WriteByte(16);
        var customClass = type.FullName;
        customClass = AMFConfiguration.Instance.GetCustomClass(customClass);

        WriteUTF(customClass);

        var propertyInfos = obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var properties = new List<PropertyInfo>(propertyInfos);

        for (var i = properties.Count - 1; i >= 0; i--)
        {
            var propertyInfo = properties[i];

            if (propertyInfo.GetCustomAttributes(typeof(NonSerializedAttribute), true).Length > 0)
                properties.RemoveAt(i);

            if (propertyInfo.GetCustomAttributes(typeof(TransientAttribute), true).Length > 0)
                properties.RemoveAt(i);
        }

        foreach (var propertyInfo in properties)
        {
            WriteUTF(propertyInfo.Name);
            var value = propertyInfo.GetValue(obj, null);
            WriteData(objectEncoding, value);
        }

        var fieldInfos = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance);

        var fields = new List<FieldInfo>(fieldInfos);

        for (var i = fields.Count - 1; i >= 0; i--)
        {
            var fieldInfo = fields[i];

            if (fieldInfo.GetCustomAttributes(typeof(NonSerializedAttribute), true).Length > 0)
                fields.RemoveAt(i);
        }

        for (var i = 0; i < fields.Count; i++)
        {
            var fieldInfo = fields[i];
            WriteUTF(fieldInfo.Name);
            WriteData(objectEncoding, fieldInfo.GetValue(obj));
        }

        WriteEndMarkup();
    }

    internal void WriteEndMarkup()
    {
        //Write the end object flag 0x00, 0x00, 0x09
        BinaryWriter.BaseStream.WriteByte(0);
        BinaryWriter.BaseStream.WriteByte(0);
        BinaryWriter.BaseStream.WriteByte(AMF0TypeCode.EndOfObject);
    }

    /// <summary>
    ///     Writes an anonymous ActionScript object to the current position in the AMF stream.
    /// </summary>
    /// <param name="objectEncoding">Object encoding to use.</param>
    /// <param name="asObject">The ActionScript object.</param>
    public void WriteASO(ObjectEncoding objectEncoding, ASObject asObject)
    {
        if (asObject != null)
        {
            AddReference(asObject);
            if (asObject.TypeName == null)
                // Object "Object"
                BinaryWriter.BaseStream.WriteByte(3);
            else
            {
                BinaryWriter.BaseStream.WriteByte(16);
                WriteUTF(asObject.TypeName);
            }

            foreach (var entry in asObject)
            {
                WriteUTF(entry.Key);
                WriteData(objectEncoding, entry.Value);
            }

            WriteEndMarkup();
        }
        else
            WriteNull();
    }

    #region AMF3

    /// <summary>
    ///     Serializes object graphs in Action Message Format (AMF).
    /// </summary>
    /// <param name="data">The Object to serialize in the AMF stream.</param>
    public void WriteAMF3Data(object data)
    {
        if (data == null)
        {
            WriteAMF3Null();
            return;
        }

        if (data is DBNull)
        {
            WriteAMF3Null();
            return;
        }

        var type = data.GetType();

        IAMFWriter amfWriter = null;
        if (AmfWriterTable[3].ContainsKey(type))
            amfWriter = AmfWriterTable[3][type];
        //Second try with basetype (Enums for example)
        if (amfWriter == null && type.BaseType != null && AmfWriterTable[3].ContainsKey(type.BaseType))
            amfWriter = AmfWriterTable[3][type.BaseType];

        if (amfWriter == null)
            lock (AmfWriterTable)
                if (!AmfWriterTable[3].ContainsKey(type))
                {
                    amfWriter = new AMF3ObjectWriter();
                    AmfWriterTable[3].Add(type, amfWriter);
                }
                else
                    amfWriter = AmfWriterTable[3][type];

        if (amfWriter != null)
            amfWriter.WriteData(this, data);
        else
        {
            var msg = string.Format("Could not find serializer for type {0}", type.FullName);
        }
        //WriteByte(AMF3TypeCode.Object);
        //WriteAMF3Object(data);
    }

    /// <summary>
    ///     Writes a null type marker to the current position in the AMF stream.
    /// </summary>
    public void WriteAMF3Null()
    {
        //Write the null code (0x1) to the output stream.
        WriteByte(AMF3TypeCode.Null);
    }

    /// <summary>
    ///     Writes a Boolean value to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A Boolean value.</param>
    public void WriteAMF3Bool(bool value)
    {
        WriteByte(value ? AMF3TypeCode.BooleanTrue : AMF3TypeCode.BooleanFalse);
    }

    /// <summary>
    ///     Writes an Array value to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">An Array object.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteAMF3Array(Array value)
    {
        if (_amf0ObjectReferences.ContainsKey(value))
        {
            WriteReference(value);
            return;
        }

        if (!_objectReferences.ContainsKey(value))
        {
            _objectReferences.Add(value, _objectReferences.Count);
            var handle = value.Length;
            handle = handle << 1;
            handle = handle | 1;
            WriteAMF3IntegerData(handle);
            WriteAMF3UTF(string.Empty);//hash name
            for (var i = 0; i < value.Length; i++) WriteAMF3Data(value.GetValue(i));
        }
        else
        {
            var handle = _objectReferences[value];
            handle = handle << 1;
            WriteAMF3IntegerData(handle);
        }
    }

    /// <summary>
    ///     Writes an Array value to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">An Array object.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteAMF3Array(IList value)
    {
        if (!_objectReferences.ContainsKey(value))
        {
            _objectReferences.Add(value, _objectReferences.Count);
            var handle = value.Count;
            handle = handle << 1;
            handle = handle | 1;
            WriteAMF3IntegerData(handle);
            WriteAMF3UTF(string.Empty);//hash name
            for (var i = 0; i < value.Count; i++) WriteAMF3Data(value[i]);
        }
        else
        {
            var handle = _objectReferences[value];
            handle = handle << 1;
            WriteAMF3IntegerData(handle);
        }
    }

    /// <summary>
    ///     Writes an associative array to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">An Dictionary object.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteAMF3AssociativeArray(IDictionary value)
    {
        if (!_objectReferences.ContainsKey(value))
        {
            _objectReferences.Add(value, _objectReferences.Count);
            WriteAMF3IntegerData(1);
            foreach (DictionaryEntry entry in value)
            {
                WriteAMF3UTF(entry.Key.ToString());
                WriteAMF3Data(entry.Value);
            }

            WriteAMF3UTF(string.Empty);
        }
        else
        {
            var handle = _objectReferences[value];
            handle = handle << 1;
            WriteAMF3IntegerData(handle);
        }
    }

    internal void WriteByteArray(ByteArray byteArray)
    {
        _objectReferences.Add(byteArray, _objectReferences.Count);
        WriteByte(AMF3TypeCode.ByteArray);
        var handle = (int)byteArray.Length;
        handle = handle << 1;
        handle = handle | 1;
        WriteAMF3IntegerData(handle);
        WriteBytes(byteArray.MemoryStream.ToArray());
    }

    /// <summary>
    ///     Writes a UTF-8 string to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">The UTF-8 string.</param>
    /// <remarks>Standard or long string header is not written.</remarks>
    public void WriteAMF3UTF(string value)
    {
        if (value == string.Empty)
            WriteAMF3IntegerData(1);
        else
        {
            if (!_stringReferences.ContainsKey(value))
            {
                _stringReferences.Add(value, _stringReferences.Count);
                var utf8Encoding = new UTF8Encoding();
                var byteCount = utf8Encoding.GetByteCount(value);
                var handle = byteCount;
                handle = handle << 1;
                handle = handle | 1;
                WriteAMF3IntegerData(handle);
                var buffer = utf8Encoding.GetBytes(value);
                if (buffer.Length > 0)
                    BinaryWriter.BaseStream.Write(buffer);
            }
            else
            {
                var handle = _stringReferences[value];
                handle = handle << 1;
                WriteAMF3IntegerData(handle);
            }
        }
    }

    /// <summary>
    ///     Writes an UTF-8 string to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">The UTF-8 string.</param>
    /// <remarks>Standard or long string header is written depending on the string length.</remarks>
    public void WriteAMF3String(string value)
    {
        WriteByte(AMF3TypeCode.String);
        WriteAMF3UTF(value);
    }

    /// <summary>
    ///     Writes a DateTime value to the current position in the AMF stream.
    ///     An ActionScript Date is serialized as the number of milliseconds elapsed since the epoch of midnight on 1st Jan
    ///     1970 in the UTC time zone.
    ///     Local time zone information is not sent.
    /// </summary>
    /// <param name="value">A DateTime value.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteAMF3DateTime(DateTime value)
    {
        if (!_objectReferences.ContainsKey(value))
        {
            _objectReferences.Add(value, _objectReferences.Count);
            var handle = 1;
            WriteAMF3IntegerData(handle);

            // Write date (milliseconds from 1970).
            var timeStart = new DateTime(1970, 1, 1, 0, 0, 0);
            value = value.ToUniversalTime();

            var span = value.Subtract(timeStart);
            var milliSeconds = (long)span.TotalMilliseconds;
            WriteDouble(milliSeconds);
        }
        else
        {
            var handle = _objectReferences[value];
            handle = handle << 1;
            WriteAMF3IntegerData(handle);
        }
    }

    private void WriteAMF3IntegerData(int value)
    {
        //Sign contraction - the high order bit of the resulting value must match every bit removed from the number
        //Clear 3 bits 
        value &= 0x1fffffff;
        if (value < 0x80)
            WriteByte((byte)value);
        else if (value < 0x4000)
        {
            WriteByte((byte)(value >> 7 & 0x7f | 0x80));
            WriteByte((byte)(value & 0x7f));
        }
        else if (value < 0x200000)
        {
            WriteByte((byte)(value >> 14 & 0x7f | 0x80));
            WriteByte((byte)(value >> 7  & 0x7f | 0x80));
            WriteByte((byte)(value & 0x7f));
        }
        else
        {
            WriteByte((byte)(value >> 22 & 0x7f | 0x80));
            WriteByte((byte)(value >> 15 & 0x7f | 0x80));
            WriteByte((byte)(value >> 8  & 0x7f | 0x80));
            WriteByte((byte)(value & 0xff));
        }
    }

    /// <summary>
    ///     Writes a 32-bit signed integer to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <remarks>Type marker is written in the AMF stream.</remarks>
    public void WriteAMF3Int(int value)
    {
        if (value is >= -268435456 and <= 268435455)//check valid range for 29bits
        {
            WriteByte(AMF3TypeCode.Integer);
            WriteAMF3IntegerData(value);
        }
        else
            //overflow condition would occur upon int conversion
            WriteAMF3Double(value);
    }

    /// <summary>
    ///     Writes a double-precision floating point number to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <remarks>Type marker is written in the AMF stream.</remarks>
    public void WriteAMF3Double(double value)
    {
        WriteByte(AMF3TypeCode.Number);
        //long tmp = BitConverter.DoubleToInt64Bits( double.Parse(value.ToString()) );
        WriteDouble(value);
    }

    /// <summary>
    ///     Writes an XmlDocument object to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">An XmlDocument object.</param>
    /// <remarks>Xml type marker is written in the AMF stream.</remarks>
    public void WriteAMF3XmlDocument(XmlDocument value)
    {
        WriteByte(AMF3TypeCode.Xml);
        var xml = string.Empty;
        if (value.DocumentElement is { OuterXml: {} })
            xml = value.DocumentElement.OuterXml;
        if (xml == string.Empty)
            WriteAMF3IntegerData(1);
        else
        {
            if (!_objectReferences.ContainsKey(value))
            {
                _objectReferences.Add(value, _objectReferences.Count);
                var utf8Encoding = new UTF8Encoding();
                var byteCount = utf8Encoding.GetByteCount(xml);
                var handle = byteCount;
                handle = handle << 1;
                handle = handle | 1;
                WriteAMF3IntegerData(handle);
                var buffer = utf8Encoding.GetBytes(xml);
                if (buffer.Length > 0)
                    BinaryWriter.BaseStream.Write(buffer);
            }
            else
            {
                var handle = _objectReferences[value];
                handle = handle << 1;
                WriteAMF3IntegerData(handle);
            }
        }
    }

    public void WriteAMF3XDocument(XDocument xDocument)
    {
        WriteByte(AMF3TypeCode.Xml);
        var value = string.Empty;
        if (xDocument != null)
            value = xDocument.ToString();
        if (value == string.Empty)
            WriteAMF3IntegerData(1);
        else
        {
            if (!_objectReferences.ContainsKey(value))
            {
                _objectReferences.Add(value, _objectReferences.Count);
                var utf8Encoding = new UTF8Encoding();
                var byteCount = utf8Encoding.GetByteCount(value);
                var handle = byteCount;
                handle = handle << 1;
                handle = handle | 1;
                WriteAMF3IntegerData(handle);
                var buffer = utf8Encoding.GetBytes(value);
                if (buffer.Length > 0)
                    BinaryWriter.BaseStream.Write(buffer);
            }
            else
            {
                var handle = _objectReferences[value];
                handle = handle << 1;
                WriteAMF3IntegerData(handle);
            }
        }
    }

    public void WriteAMF3XElement(XElement xElement)
    {
        WriteByte(AMF3TypeCode.Xml);
        var value = string.Empty;
        if (xElement != null)
            value = xElement.ToString();
        if (value == string.Empty)
            WriteAMF3IntegerData(1);
        else
        {
            if (!_objectReferences.ContainsKey(value))
            {
                _objectReferences.Add(value, _objectReferences.Count);
                var utf8Encoding = new UTF8Encoding();
                var byteCount = utf8Encoding.GetByteCount(value);
                var handle = byteCount;
                handle = handle << 1;
                handle = handle | 1;
                WriteAMF3IntegerData(handle);
                var buffer = utf8Encoding.GetBytes(value);
                if (buffer.Length > 0)
                    BinaryWriter.BaseStream.Write(buffer);
            }
            else
            {
                var handle = _objectReferences[value];
                handle = handle << 1;
                WriteAMF3IntegerData(handle);
            }
        }
    }

    /// <summary>
    ///     Writes an object to the current position in the AMF stream.
    /// </summary>
    /// <param name="value">The object to serialize.</param>
    /// <remarks>No type marker is written in the AMF stream.</remarks>
    public void WriteAMF3Object(object value)
    {
        if (!_objectReferences.ContainsKey(value))
        {
            _objectReferences.Add(value, _objectReferences.Count);

            var classDefinition = GetClassDefinition(value);
            if (classDefinition != null && _classDefinitionReferences.ContainsKey(classDefinition))
            {
                //Existing class-def
                var handle = _classDefinitionReferences[classDefinition];//handle = classRef 0 1
                handle = handle << 2;
                handle = handle | 1;
                WriteAMF3IntegerData(handle);
            }
            else
            {
                //inline class-def

                classDefinition = CreateClassDefinition(value);
                _classDefinitionReferences.Add(classDefinition, _classDefinitionReferences.Count);
                //handle = memberCount dynamic externalizable 1 1
                var handle = classDefinition.MemberCount;
                handle = handle << 1;
                handle = handle | (classDefinition.IsDynamic ? 1 : 0);
                handle = handle << 1;
                handle = handle | (classDefinition.IsExternalizable ? 1 : 0);
                handle = handle << 2;
                handle = handle | 3;
                WriteAMF3IntegerData(handle);
                WriteAMF3UTF(classDefinition.ClassName);
                for (var i = 0; i < classDefinition.MemberCount; i++)
                {
                    var key = classDefinition.Members[i].Name;
                    WriteAMF3UTF(key);
                }
            }

            //write inline object
            if (classDefinition.IsExternalizable)
            {
                if (value is IExternalizable)
                {
                    var externalizable = value as IExternalizable;
                    var dataOutput = new DataOutput(this);
                    externalizable.WriteExternal(dataOutput);
                }
                else
                    throw new AMFException(__Res.GetString(__Res.Externalizable_CastFail, classDefinition.ClassName));
            }
            else
            {
                for (var i = 0; i < classDefinition.MemberCount; i++)
                {
                    var memberValue = GetMember(value, classDefinition.Members[i]);
                    WriteAMF3Data(memberValue);
                }

                if (classDefinition.IsDynamic)
                {
                    var dictionary = value as IDictionary;
                    foreach (DictionaryEntry entry in dictionary)
                    {
                        WriteAMF3UTF(entry.Key.ToString());
                        WriteAMF3Data(entry.Value);
                    }

                    WriteAMF3UTF(string.Empty);
                }
            }
        }
        else
        {
            //handle = objectRef 0
            var handle = _objectReferences[value];
            handle = handle << 1;
            WriteAMF3IntegerData(handle);
        }
    }

    private ClassDefinition GetClassDefinition(object obj)
    {
        if (obj is ASObject)
        {
            var asObject = obj as ASObject;
            if (asObject.IsTypedObject && classDefinitions.ContainsKey(asObject.TypeName))
                return classDefinitions[asObject.TypeName];
            return null;
        }

        if (classDefinitions.ContainsKey(obj.GetType().FullName))
            return classDefinitions[obj.GetType().FullName];
        return null;
    }

    #if WCF
        static bool IsDataContract(Type type)
        {
            object[] attributes =
 type.GetCustomAttributes(typeof(System.Runtime.Serialization.DataContractAttribute), false);
            return attributes.Length == 1;
        }
    #endif

    private ClassDefinition CreateClassDefinition(object obj)
    {
        ClassDefinition classDefinition = null;
        var type = obj.GetType();
        var externalizable = type.GetInterface(typeof(IExternalizable).FullName, true) != null;
        var dynamic = false;
        string customClassName = null;
        if (obj is IDictionary)//ASObject, ObjectProxy
        {
            if (obj is ASObject && (obj as ASObject).IsTypedObject)//ASObject
            {
                var asObject = obj as ASObject;
                var classMemberList = new ClassMember[asObject.Count];
                var i = 0;

                foreach (var entry in asObject)
                {
                    var classMember = new ClassMember(entry.Key, BindingFlags.Default, MemberTypes.Custom);
                    classMemberList[i] = classMember;
                    i++;
                }

                customClassName = asObject.TypeName;
                classDefinition = new(customClassName, classMemberList, externalizable, dynamic);
                classDefinitions[customClassName] = classDefinition;
            }
            else
            {
                dynamic = true;
                customClassName = string.Empty;
                classDefinition = new(customClassName, ClassDefinition.EmptyClassMembers,
                externalizable, dynamic);
            }
        }
        else if (obj is IExternalizable)
        {
            customClassName = type.FullName;
            customClassName = AMFConfiguration.Instance.GetCustomClass(customClassName);

            classDefinition = new(customClassName, ClassDefinition.EmptyClassMembers, true, false);
            classDefinitions[type.FullName] = classDefinition;
        }
        else
        {
            var memberNames = new List<string>();
            var classMemberList = new List<ClassMember>();

            var propertyInfos = obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            for (var i = 0; i < propertyInfos.Length; i++)
            {
                var propertyInfo = propertyInfos[i];
                var name = propertyInfo.Name;
                if (propertyInfo.GetCustomAttributes(typeof(TransientAttribute), true).Length > 0)
                    continue;
                if (propertyInfo.GetGetMethod() == null || propertyInfo.GetGetMethod().GetParameters().Length > 0)
                {
                    //The gateway will not be able to access this property
                    var msg = __Res.GetString(__Res.Reflection_PropertyIndexFail,
                    string.Format("{0}.{1}", type.FullName, propertyInfo.Name));
                    continue;
                }

                if (memberNames.Contains(name))
                    continue;
                memberNames.Add(name);
                var bf = BindingFlags.Public | BindingFlags.Static | BindingFlags.Instance;
                try
                {
                    var propertyInfoTmp = obj.GetType().GetProperty(name);
                }
                catch (AmbiguousMatchException)
                {
                    bf = BindingFlags.DeclaredOnly | BindingFlags.Static | BindingFlags.Public | BindingFlags.Instance;
                }

                var classMember = new ClassMember(name, bf, propertyInfo.MemberType);
                classMemberList.Add(classMember);
            }

            var fieldInfos = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance);
            for (var i = 0; i < fieldInfos.Length; i++)
            {
                var fieldInfo = fieldInfos[i];

                if (fieldInfo.GetCustomAttributes(typeof(NonSerializedAttribute), true).Length > 0)
                    continue;

                if (fieldInfo.GetCustomAttributes(typeof(TransientAttribute), true).Length > 0)
                    continue;
                var name = fieldInfo.Name;
                var classMember = new ClassMember(name,
                BindingFlags.Public | BindingFlags.Static | BindingFlags.Instance, fieldInfo.MemberType);
                classMemberList.Add(classMember);
            }

            var classMembers = classMemberList.ToArray();
            customClassName = type.FullName;
            customClassName = AMFConfiguration.Instance.GetCustomClass(customClassName);
            classDefinition = new(customClassName, classMembers, externalizable, dynamic);
            if (classDefinitions.ContainsKey(type.FullName))
                classDefinitions[type.FullName] = classDefinition;
        }

        return classDefinition;
    }

    #endregion AMF3

    internal object GetMember(object instance, ClassMember member)
    {
        if (instance is ASObject)
        {
            var aso = instance as ASObject;
            if (aso.ContainsKey(member.Name))
                return aso[member.Name];
        }

        var type = instance.GetType();
        if (member.MemberType == MemberTypes.Property)
        {
            var propertyInfo = type.GetProperty(member.Name, member.BindingFlags);
            return propertyInfo.GetValue(instance, null);
        }

        if (member.MemberType == MemberTypes.Field)
        {
            var fieldInfo = type.GetField(member.Name, member.BindingFlags);
            return fieldInfo.GetValue(instance);
        }

        var msg = __Res.GetString(__Res.Reflection_MemberNotFound,
        string.Format("{0}.{1}", type.FullName, member.Name));
        throw new AMFException(msg);
    }
}