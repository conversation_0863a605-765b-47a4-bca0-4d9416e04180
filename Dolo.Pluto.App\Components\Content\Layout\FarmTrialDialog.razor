@using Dolo.Pluto.App.Components.Content.Layout
@using Dolo.Pluto.App.Core
@using System.Diagnostics
@inject TrialService TrialService
@inject DialogService DialogService
@namespace Dolo.Pluto.App.Components.Content.Layout

@if (_isVisible)
{
    <div class="fixed inset-0 w-full h-full flex justify-center items-center bg-black/50 z-[99999]" style="z-index: 99999;">
        <div class="m-4 max-w-md rounded-xl bg-white shadow-xl border border-gray-100 overflow-hidden">
            <!-- Header -->
            <div class="flex items-center justify-between bg-gradient-to-r from-[#77AAF5] to-[#5B9BD5] px-4 py-3 text-white">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-4 w-4 text-white">
                            <path fill-rule="evenodd" d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.563.563 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold">Farm Access</h2>
                        <p class="text-white/80 text-xs">Choose your method</p>
                    </div>
                </div>
                <button @onclick="@(() => Hide())" class="w-7 h-7 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-all duration-200">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="currentColor" class="w-3.5 h-3.5 text-white/80">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Content -->
            <div class="p-4 space-y-4">
                <!-- Description -->
                <p class="text-gray-600 text-sm text-center">
                    To farm StarCoins, you need access permissions. Our bot grants permissions based on Discord activity.
                </p>

                <!-- Options -->
                <div class="space-y-3">
                    <!-- Premium Option -->
                    <button @onclick="@(() => OpenDiscordShop())"
                        class="w-full bg-gradient-to-r from-[#77AAF5]/10 to-[#5B9BD5]/10 hover:from-[#77AAF5]/20 hover:to-[#5B9BD5]/20 border border-[#77AAF5]/30 hover:border-[#77AAF5] rounded-lg p-3 text-left transition-all duration-200 group">
                        <div class="flex items-center space-x-3">
                            <div class="w-9 h-9 rounded-lg bg-gradient-to-br from-[#77AAF5] to-[#5B9BD5] flex items-center justify-center shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-white">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-sm font-bold text-gray-900 group-hover:text-[#77AAF5] transition-colors">Purchase PlutoPlus</h3>
                                <p class="text-xs text-gray-500">Instant unlimited access</p>
                            </div>
                            <div class="text-[#77AAF5] group-hover:translate-x-0.5 transition-transform">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                                </svg>
                            </div>
                        </div>
                    </button>

                    <!-- Community Option -->
                    <button @onclick="@(() => OpenDiscordCommunity())"
                        class="w-full bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-green-300 rounded-lg p-3 text-left transition-all duration-200 group">
                        <div class="flex items-center space-x-3">
                            <div class="w-9 h-9 rounded-lg bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-white">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-sm font-bold text-gray-900 group-hover:text-green-600 transition-colors">Discord Community</h3>
                                <p class="text-xs text-gray-500">Stay active for automatic access</p>
                            </div>
                            <div class="text-green-500 group-hover:translate-x-0.5 transition-transform">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
                                </svg>
                            </div>
                        </div>
                    </button>
                </div>

                <!-- Info Footer -->
                <div class="bg-blue-50 rounded-lg p-3 border border-blue-100">
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <img width="12" src="@(Dolo.Assets.MSPLoader)" alt="" class="animate-spin"/>
                        </div>
                        <div>
                            <p class="text-xs font-medium text-blue-900">Automatic tracking active</p>
                            <p class="text-xs text-blue-700">Bot monitors Discord activity continuously</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private bool _isVisible = false;

    protected override void OnInitialized()
    {
        Console.WriteLine("FarmTrialDialog initialized");
        _isVisible = false;
    }

    public Task ShowAsync()
    {
        Console.WriteLine($"FarmTrialDialog.ShowAsync called - current _isVisible: {_isVisible}");
        _isVisible = true;
        StateHasChanged();
        Console.WriteLine($"FarmTrialDialog.ShowAsync completed - _isVisible set to: {_isVisible}");
        return Task.CompletedTask;
    }

    public void Show()
    {
        Console.WriteLine("FarmTrialDialog.Show called - calling ShowAsync");
        _ = ShowAsync();
    }

    public void Hide()
    {
        Console.WriteLine($"FarmTrialDialog.Hide called - current _isVisible: {_isVisible}");
        _isVisible = false;
        StateHasChanged();
        Console.WriteLine($"FarmTrialDialog.Hide completed - _isVisible set to: {_isVisible}");
    }

    private async Task OpenDiscordShop()
    {
        try
        {
            Process.Start(new ProcessStartInfo
            {
                FileName = "https://discord.gg/mspshop",
                UseShellExecute = true
            });
        }
        catch
        {
            await DialogService.ShowToastAsync("Please visit: https://discord.gg/mspshop");
        }
    }

    private async Task OpenDiscordCommunity()
    {
        try
        {
            Process.Start(new ProcessStartInfo
            {
                FileName = "https://discord.gg/dolo",
                UseShellExecute = true
            });
        }
        catch
        {
            await DialogService.ShowToastAsync("Please visit: https://discord.gg/dolo");
        }
    }
}
