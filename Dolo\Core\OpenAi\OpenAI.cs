﻿namespace Dolo.Core.OpenAi;

public class OpenAI
{
    private readonly OpenAIApi _api;
    private readonly OpenAIConfig _config;
    public OpenAI(Action<OpenAIConfig> config)
    {
        _config = config.GetAction();
        _api = new(this);

        if (string.IsNullOrEmpty(_config.ApiKey?.Trim()))
            throw new ArgumentNullException(nameof(config));
    }

    /// <summary>
    ///     Get the config for the OpenAI API
    /// </summary>
    internal OpenAIConfig GetConfig() => _config;

    /// <summary>
    ///     Lists the currently available models, and provides basic information
    ///     about each one such as the owner and availability.
    /// </summary>
    [OpenAIRequest("https://api.openai.com/v1/models")]
    public async Task<string?> ListModelsAsync() => await _api.ListModelsAsync();

    /// <summary>
    ///     Retrieves a model instance, providing basic information
    ///     about the model such as the owner and permission.
    /// </summary>
    [OpenAIRequest("https://api.openai.com/v1/models/{model}")]
    public async Task<string?> GetModelAsync(string model) => await _api.GetModelAsync(model);


    /// <summary>
    ///     Creates a completion for the provided prompt and parameters.
    /// </summary>
    [OpenAIRequest("https://api.openai.com/v1/completions")]
    public async Task<string?> CreateCompletionAsync(string? prompt) => await _api.CreateCompletionAsync(prompt);
}