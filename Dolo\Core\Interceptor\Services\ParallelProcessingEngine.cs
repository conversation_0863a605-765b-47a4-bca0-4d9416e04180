using System.Collections.Concurrent;
using System.Threading.Channels;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class ParallelProcessingEngine : IDisposable
{
    private readonly Channel<WorkItem> _workChannel;
    private readonly ChannelWriter<WorkItem> _writer;
    private readonly ChannelReader<WorkItem> _reader;
    private readonly ConcurrentDictionary<string, TaskCompletionSource<object?>> _pendingWork = new();
    private readonly SemaphoreSlim _concurrencyLimiter;
    private readonly ILogger<ParallelProcessingEngine> _logger;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly Task[] _workerTasks;
    private bool _disposed;

    public ParallelProcessingEngine(ILogger<ParallelProcessingEngine> logger, int maxConcurrency = 0)
    {
        _logger = logger;

        // Use default value if not specified
        if (maxConcurrency <= 0)
            maxConcurrency = Environment.ProcessorCount * 2;

        _concurrencyLimiter = new SemaphoreSlim(maxConcurrency, maxConcurrency);

        var options = new BoundedChannelOptions(1000)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = false,
            SingleWriter = false
        };

        _workChannel = Channel.CreateBounded<WorkItem>(options);
        _writer = _workChannel.Writer;
        _reader = _workChannel.Reader;

        _workerTasks = new Task[maxConcurrency];
        for (var i = 0; i < maxConcurrency; i++)
        {
            _workerTasks[i] = Task.Run(ProcessWorkItems, _cancellationTokenSource.Token);
        }

        _logger.LogInformation("🚀 Parallel processing engine started with {MaxConcurrency} workers", maxConcurrency);
    }

    public async Task<T?> ExecuteAsync<T>(string workId, Func<Task<T>> work, TimeSpan? timeout = null)
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ParallelProcessingEngine));

        var tcs = new TaskCompletionSource<object?>();
        var workItem = new WorkItem(workId, async () =>
        {
            try
            {
                var result = await work().ConfigureAwait(false);
                tcs.SetResult(result);
            }
            catch (Exception ex)
            {
                tcs.SetException(ex);
            }
        });

        _pendingWork.TryAdd(workId, tcs);

        try
        {
            await _writer.WriteAsync(workItem, _cancellationTokenSource.Token).ConfigureAwait(false);

            var timeoutTask = timeout.HasValue 
                ? Task.Delay(timeout.Value, _cancellationTokenSource.Token)
                : Task.Delay(Timeout.Infinite, _cancellationTokenSource.Token);

            var completedTask = await Task.WhenAny(tcs.Task, timeoutTask).ConfigureAwait(false);

            if (completedTask == timeoutTask)
            {
                _pendingWork.TryRemove(workId, out _);
                throw new TimeoutException($"Work item {workId} timed out");
            }

            var result = await tcs.Task.ConfigureAwait(false);
            return (T?)result;
        }
        finally
        {
            _pendingWork.TryRemove(workId, out _);
        }
    }

    public async Task ExecuteAsync(string workId, Func<Task> work, TimeSpan? timeout = null)
    {
        await ExecuteAsync<object?>(workId, async () =>
        {
            await work().ConfigureAwait(false);
            return null;
        }, timeout).ConfigureAwait(false);
    }

    public void ExecuteFireAndForget(string workId, Func<Task> work)
    {
        if (_disposed) return;

        var workItem = new WorkItem(workId, async () =>
        {
            try
            {
                await work().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Fire-and-forget work item {WorkId} failed: {Error}", workId, ex.Message);
            }
        });

        _ = _writer.TryWrite(workItem);
    }

    public (int QueuedItems, int ActiveWorkers, int PendingWork) GetStatistics()
    {
        var queuedItems = _workChannel.Reader.CanCount ? _workChannel.Reader.Count : -1;
        var activeWorkers = _concurrencyLimiter.CurrentCount;
        var pendingWork = _pendingWork.Count;

        return (queuedItems, activeWorkers, pendingWork);
    }

    private async Task ProcessWorkItems()
    {
        try
        {
            await foreach (var workItem in _reader.ReadAllAsync(_cancellationTokenSource.Token))
            {
                await _concurrencyLimiter.WaitAsync(_cancellationTokenSource.Token).ConfigureAwait(false);

                try
                {
                    _logger.LogDebug("🔄 Processing work item: {WorkId}", workItem.Id);
                    await workItem.Work().ConfigureAwait(false);
                    _logger.LogDebug("✅ Completed work item: {WorkId}", workItem.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "❌ Work item {WorkId} failed: {Error}", workItem.Id, ex.Message);
                }
                finally
                {
                    _concurrencyLimiter.Release();
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when shutting down
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Worker task failed: {Error}", ex.Message);
        }
    }

    public async Task StopAsync()
    {
        if (_disposed) return;

        _logger.LogInformation("🛑 Stopping parallel processing engine...");

        _writer.Complete();
        _cancellationTokenSource.Cancel();

        try
        {
            await Task.WhenAll(_workerTasks).ConfigureAwait(false);
        }
        catch (OperationCanceledException)
        {
            // Expected
        }

        _logger.LogInformation("✅ Parallel processing engine stopped");
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        StopAsync().GetAwaiter().GetResult();

        _concurrencyLimiter?.Dispose();
        _cancellationTokenSource?.Dispose();
    }

    private readonly record struct WorkItem(string Id, Func<Task> Work);
}
