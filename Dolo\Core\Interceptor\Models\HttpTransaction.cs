using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Services;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Represents a complete HTTP transaction with paired request and response
/// </summary>
public sealed class HttpTransaction
{
    private readonly TaskCompletionSource<bool> _completionSource = new();
    
    public string TransactionId { get; init; } = string.Empty;
    public string Hostname { get; init; } = string.Empty;
    public int Port { get; init; } = 443;
    public string Protocol { get; init; } = "HTTPS";
    public DateTime StartTime { get; init; } = DateTime.UtcNow;
    public DateTime? EndTime { get; set; }
    public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;
    public int TotalBytes => (Request?.Content?.Length ?? 0) + (Response?.Content?.Length ?? 0);
      public IHttpInterceptedRequestMessage? Request { get; set; }
    public IHttpInterceptedResponseMessage? Response { get; set; }
    public ProcessedResponse? ProcessedResponse { get; set; }
    
    /// <summary>
    /// True if both request and response have been captured
    /// </summary>
    public bool IsComplete => Request != null && Response != null;
    
    /// <summary>
    /// True if this transaction has been waiting too long for completion (orphaned)
    /// </summary>
    public bool IsExpired(TimeSpan timeout) => DateTime.UtcNow - StartTime > timeout;
    
    /// <summary>
    /// Wait for the transaction to complete or timeout
    /// </summary>
    public async Task<bool> WaitForCompletionAsync(TimeSpan? timeout = null)
    {
        var timeoutTask = timeout.HasValue 
            ? Task.Delay(timeout.Value) 
            : Task.Delay(Timeout.Infinite);
            
        var completedTask = await Task.WhenAny(_completionSource.Task, timeoutTask).ConfigureAwait(false);
        return completedTask == _completionSource.Task && await _completionSource.Task.ConfigureAwait(false);
    }
    
    /// <summary>
    /// Complete the transaction with a response
    /// </summary>
    public void CompleteWithResponse(IHttpInterceptedResponseMessage response)
    {
        Response = response;
        EndTime = DateTime.UtcNow;
        _completionSource.TrySetResult(true);
    }
    
    /// <summary>
    /// Mark the transaction as timed out
    /// </summary>
    public void MarkAsTimedOut()
    {
        EndTime = DateTime.UtcNow;
        _completionSource.TrySetResult(false);
    }
}

/// <summary>
/// Event arguments for complete HTTP transactions (request + response pairs)
/// </summary>
public sealed class HttpTransactionCompletedEventArgs(HttpTransaction transaction) : EventArgs
{
    public HttpTransaction Transaction { get; } = transaction;
    
    // Convenience properties for backward compatibility
    public string Hostname => Transaction.Hostname;
    public int Port => Transaction.Port;
    public string Protocol => Transaction.Protocol;
    public string Url => $"{Protocol}://{Hostname}:{Port}{Transaction.Request?.Uri?.PathAndQuery}";
    public int BytesTransferred => Transaction.TotalBytes;
    public TimeSpan Duration => Transaction.Duration;
    public DateTime Timestamp => Transaction.StartTime;
    public IHttpInterceptedRequestMessage? Request => Transaction.Request;
    public IHttpInterceptedResponseMessage? Response => Transaction.Response;
}
