using Dolo.Core.Discord;
using DSharpPlus.EventArgs;

namespace Dolo.Bot.Log.Hub.Handler;

public static class InteractionCreated
{
    public static async Task InvokeAsync(this InteractionCreatedEventArgs e)
    {
        // Skip if no slash commands channel configured
        if (HubChannel.SlashCommands is null)
            return;

        // Only log slash commands (not buttons, modals, etc.)
        if (e.Interaction.Type != DiscordInteractionType.ApplicationCommand)
            return;

        // Skip bot interactions
        if (e.Interaction.User.IsBot)
            return;

        try
        {
            // Create and send the log embed
            var embed = HubEmbed.InteractionCreated(e.Interaction);
            await HubChannel.SlashCommands.TrySendMessageAsync(embed);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Log] Error logging interaction: {ex.Message}");
        }
    }
}
