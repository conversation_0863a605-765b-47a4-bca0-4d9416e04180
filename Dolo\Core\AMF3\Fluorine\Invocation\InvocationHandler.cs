﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Invocation;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class InvocationHandler
{
    private readonly MethodInfo _methodInfo;

    /// <summary>
    ///     Initializes a new instance of the InvocationHandler class.
    /// </summary>
    /// <param name="methodInfo"></param>
    public InvocationHandler(MethodInfo methodInfo) => _methodInfo = methodInfo;

    public async Task<object> Invoke(object obj, object[] arguments)
    {
        object result = null;
        if (_methodInfo.GetCustomAttribute(typeof(AsyncStateMachineAttribute)) == null)
            result = _methodInfo.Invoke(obj, arguments);
        else
        {
            var task = (Task)_methodInfo.Invoke(obj, arguments);
            await task;
            result = task.GetType().GetProperty("Result").GetValue(task);
        }

        var attributes = _methodInfo.GetCustomAttributes(false);
        if (attributes is { Length: > 0 })
        {
            var invocationManager = new InvocationManager();
            invocationManager.Result = result;
            for (var i = 0; i < attributes.Length; i++)
            {
                var attribute = attributes[i] as System.Attribute;
                if (attribute is IInvocationCallback)
                {
                    var invocationCallback = attribute as IInvocationCallback;
                    invocationCallback.OnInvoked(invocationManager, _methodInfo, obj, arguments, result);
                }
            }

            for (var i = 0; i < attributes.Length; i++)
            {
                var attribute = attributes[i] as System.Attribute;
                if (attribute is IInvocationResultHandler)
                {
                    var invocationResultHandler = attribute as IInvocationResultHandler;
                    invocationResultHandler.HandleResult(invocationManager, _methodInfo, obj, arguments, result);
                }
            }

            return invocationManager.Result;
        }

        return result;
    }
}