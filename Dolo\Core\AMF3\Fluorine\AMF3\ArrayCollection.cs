﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.ComponentModel;
using System.Globalization;
using Dolo.Core.AMF3.Fluorine;
using Dolo.Core.AMF3.Fluorine.AMF3;
namespace Dolo.Core.AMF3.Fluorine.AMF3;

internal class ArrayCollectionConverter : ArrayConverter
{
    /// <summary>
    ///     Overloaded. Returns whether this converter can convert the object to the specified type.
    /// </summary>
    /// <param name="context">An ITypeDescriptorContext that provides a format context.</param>
    /// <param name="destinationType">A Type that represents the type you want to convert to.</param>
    /// <returns>true if this converter can perform the conversion; otherwise, false.</returns>
    public override bool CanConvertTo(ITypeDescriptorContext context, Type destinationType)
    {
        if (destinationType == null)
            throw new ArgumentNullException("destinationType");

        if (destinationType == typeof(ArrayCollection))
            return true;
        if (destinationType.IsArray)
            return true;
        if (destinationType == typeof(ArrayList))
            return true;
        if (destinationType == typeof(IList))
            return true;
        var typeIList = destinationType.GetInterface("System.Collections.IList", false);
        if (typeIList != null)
            return true;
        //generic interface
        var typeGenericICollection = destinationType.GetInterface("System.Collections.Generic.ICollection`1", false);
        if (typeGenericICollection != null)
            return true;
        return base.CanConvertTo(context, destinationType);
    }

    /// <summary>
    ///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="context">An ITypeDescriptorContext that provides a format context.</param>
    /// <param name="culture">
    ///     A CultureInfo object. If a null reference (Nothing in Visual Basic) is passed, the current
    ///     culture is assumed.
    /// </param>
    /// <param name="value">The Object to convert.</param>
    /// <param name="destinationType">The Type to convert the value parameter to.</param>
    public override object ConvertTo(ITypeDescriptorContext context, CultureInfo culture, object value,
        Type destinationType)
    {
        var ac = value as ArrayCollection;
        ValidationUtils.ArgumentNotNull(ac, "value");
        if (destinationType == null)
            throw new ArgumentNullException("destinationType");

        if (destinationType == typeof(ArrayCollection))
            return value;
        if (destinationType.IsArray)
            return ac.ToArray();
        if (destinationType == typeof(ArrayList))
        {
            if (ac?.List is ArrayList)
                return ac.List;
            return ac != null ? ArrayList.Adapter(ac.List) : default;
        }

        if (destinationType == typeof(IList)) return ac?.List;
        //generic interface
        var typeGenericICollection = destinationType.GetInterface("System.Collections.Generic.ICollection`1", false);
        if (typeGenericICollection != null)
        {
            var obj = TypeHelper.CreateInstance(destinationType);
            var miAddCollection = destinationType.GetMethod("Add");
            if (miAddCollection != null)
            {
                var parameters = miAddCollection.GetParameters();
                if (parameters is { Length: 1 })
                {
                    var parameterType = parameters[0].ParameterType;
                    var list = (IList)value;
                    for (var i = 0; i < list.Count; i++)
                        miAddCollection.Invoke(obj, [
                            TypeHelper.ChangeType(list[i], parameterType)
                        ]);
                    return obj;
                }
            }
        }

        var typeIList = destinationType.GetInterface("System.Collections.IList", false);
        if (typeIList != null)
        {
            var obj = TypeHelper.CreateInstance(destinationType);
            var list = obj as IList;
            for (var i = 0; i < ac.List.Count; i++) list.Add(ac.List[i]);
            return obj;
        }

        return base.ConvertTo(context, culture, value, destinationType);
    }
}

/// <summary>
///     Flex ArrayCollection class. The ArrayCollection class is a wrapper class that exposes an Array as a collection.
/// </summary>
[TypeConverter(typeof(ArrayCollectionConverter))]
internal class ArrayCollection : IExternalizable, IList
{
    /// <summary>
    ///     Initializes a new instance of the ArrayCollection class.
    /// </summary>
    public ArrayCollection()
        => List = new List<object>();

    /// <summary>
    ///     Creates an ArrayCollection wrapper for a specific IList.
    /// </summary>
    /// <param name="list">The IList to wrap.</param>
    public ArrayCollection(IList list)
        => List = list;

    /// <summary>
    ///     Gets the underlying IList.
    /// </summary>
    public IList List { get; private set; }

    /// <summary>
    ///     Gets the number of elements contained in the ArrayCollection.
    /// </summary>
    public int Count
        => List?.Count ?? 0;

    #region IEnumerable Members

    /// <summary>
    ///     Returns an enumerator that iterates through an ArrayCollection.
    /// </summary>
    /// <returns>An IEnumerator object that can be used to iterate through the collection.</returns>
    public IEnumerator GetEnumerator()
        => List.GetEnumerator();

    #endregion

    /// <summary>
    ///     Copies the elements of the ArrayCollection to a new array.
    /// </summary>
    /// <returns></returns>
    public object[] ToArray()
    {
        if (List != null)
        {
            if (List is ArrayList)
                return ((ArrayList)List).ToArray();
            if (List is List<object>)
                return ((List<object>)List).ToArray();
            var objArray = new object[List.Count];
            for (var i = 0; i < List.Count; i++)
                objArray[i] = List[i];
            return objArray;
        }

        return null;
    }

    #region IExternalizable Members

    /// <summary>
    ///     Decode the ArrayCollection from a data stream.
    /// </summary>
    /// <param name="input">IDataInput interface.</param>
    public void ReadExternal(IDataInput input)
    {
        List = input.ReadObject() as IList;
    }

    /// <summary>
    ///     Encode the ArrayCollection for a data stream.
    /// </summary>
    /// <param name="output">IDataOutput interface.</param>
    public void WriteExternal(IDataOutput output)
    {
        output.WriteObject(ToArray());
    }

    #endregion

    #region IList Members

    /// <summary>
    ///     Gets a value indicating whether the ArrayCollection is read-only.
    /// </summary>
    public bool IsReadOnly
        => List.IsReadOnly;

    /// <summary>
    ///     Gets or sets the element at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the element to get or set.</param>
    /// <returns>The element at the specified index.</returns>
    public object this[int index]
    {
        get => List[index];
        set => List[index] = value;
    }

    /// <summary>
    ///     Removes the ArrayCollection item at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the item to remove.</param>
    public void RemoveAt(int index)
    {
        List.RemoveAt(index);
    }

    /// <summary>
    ///     Inserts an item to the ArrayCollection at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index at which value should be inserted.</param>
    /// <param name="value">The Object to insert into the ArrayCollection.</param>
    public void Insert(int index, object value)
    {
        List.Insert(index, value);
    }

    /// <summary>
    ///     Removes the first occurrence of a specific object from the ArrayCollection.
    /// </summary>
    /// <param name="value">The Object to remove from the ArrayCollection.</param>
    public void Remove(object value)
    {
        List.Remove(value);
    }

    /// <summary>
    ///     Determines whether the ArrayCollection contains a specific value.
    /// </summary>
    /// <param name="value">The Object to locate in the ArrayCollection.</param>
    /// <returns>true if the Object is found in the ArrayCollection; otherwise, false.</returns>
    public bool Contains(object value)
        => List.Contains(value);

    /// <summary>
    ///     Removes all items from the ArrayCollection.
    /// </summary>
    public void Clear()
    {
        List.Clear();
    }

    /// <summary>
    ///     Determines the index of a specific item in the ArrayCollection.
    /// </summary>
    /// <param name="value">The Object to locate in the ArrayCollection.</param>
    /// <returns>The index of value if found in the ArrayCollection; otherwise, -1.</returns>
    public int IndexOf(object value)
        => List.IndexOf(value);

    /// <summary>
    ///     Adds an item to the ArrayCollection.
    /// </summary>
    /// <param name="value">The Object to add to the ArrayCollection.</param>
    /// <returns>The position into which the new element was inserted.</returns>
    public int Add(object value)
        => List.Add(value);

    /// <summary>
    ///     Gets a value indicating whether the ArrayCollection has a fixed size.
    /// </summary>
    public bool IsFixedSize
        => List.IsFixedSize;

    #endregion

    #region ICollection Members

    /// <summary>
    ///     Gets a value indicating whether access to the ArrayCollection is synchronized (thread safe).
    /// </summary>
    public bool IsSynchronized
        => List.IsSynchronized;

    /// <summary>
    ///     Copies the elements of the ArrayCollection to an Array, starting at a particular Array index.
    /// </summary>
    /// <param name="array">
    ///     The one-dimensional Array that is the destination of the elements copied from ArrayCollection. The
    ///     Array must have zero-based indexing.
    /// </param>
    /// <param name="index">The zero-based index in array at which copying begins.</param>
    public void CopyTo(Array array, int index)
    {
        List.CopyTo(array, index);
    }

    /// <summary>
    ///     Gets an object that can be used to synchronize access to the ArrayCollection.
    /// </summary>
    public object SyncRoot
        => List.SyncRoot;

    #endregion
}
