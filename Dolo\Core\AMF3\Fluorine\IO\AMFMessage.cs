﻿namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMFMessage
{
    /// <summary>
    ///     AMF packet body values.
    /// </summary>
    protected List<AMFBody> _bodies;

    /// <summary>
    ///     AMF packet headers.
    /// </summary>
    protected List<AMFHeader> _headers;

    /// <summary>
    ///     AMF packet version.
    /// </summary>
    protected ushort _version;

    /// <summary>
    ///     Initializes a new instance of the AMFMessage class.
    /// </summary>
    public AMFMessage() : this(0)
    {}

    /// <summary>
    ///     Initializes a new instance of the AMFMessage class.
    /// </summary>
    /// <param name="version"></param>
    public AMFMessage(ushort version)
    {
        _version = version;
        _headers = new(1);
        _bodies = new(1);
    }

    /// <summary>
    ///     Gets the AMF packet version.
    /// </summary>
    public ushort Version => _version;

    /// <summary>
    ///     Gets the body count.
    /// </summary>
    public int BodyCount => _bodies.Count;

    /// <summary>
    ///     Gets a readonly collection of AMF bodies.
    /// </summary>
    public List<AMFBody> Bodies => _bodies;

    /// <summary>
    ///     Gets the header count.
    /// </summary>
    public int HeaderCount => _headers.Count;

    /// <summary>
    ///     Gets the AMF version/encoding used for this AMF packet.
    /// </summary>
    public ObjectEncoding ObjectEncoding
    {
        get
        {
            if (_version == 0 || _version == 1)
                return ObjectEncoding.AMF0;
            if (_version == 3)
                return ObjectEncoding.AMF3;
            throw new UnexpectedAMF();
        }
    }


    public void ClearHeaders()
    {
        _headers.Clear();
    }

    /// <summary>
    ///     Adds a body to the AMF packet.
    /// </summary>
    /// <param name="body">The body object to add.</param>
    public AMFMessage AddMany(AMFBody body, List<AMFHeader>? header)
    {
        if (header is null)
            return this;

        _bodies.Add(body);
        _headers.AddRange(header);

        return this;
    }

    /// <summary>
    ///     Adds a body to the AMF packet.
    /// </summary>
    /// <param name="body">The body object to add.</param>
    public void AddBody(AMFBody body)
    {
        _bodies.Add(body);
    }

    /// <summary>
    ///     Adds a header to the AMF packet.
    /// </summary>
    /// <param name="header">The header object to add.</param>
    public void AddHeader(AMFHeader header)
    {
        _headers.Add(header);
    }

    /// <summary>
    ///     Gets a single AMF body object by index.
    /// </summary>
    /// <param name="index">The numerical index of the body.</param>
    /// <returns>The body referenced by index.</returns>
    public AMFBody TryGetBodyAt(int index)
    {
        // check if the _bodies has the index
        if (index < 0 || index >= _bodies.Count)
            return new();

        return _bodies[index];
    }

    public AMFBody ReplaceBodyAt(int index, object body)
    {
        _bodies[index].Content = body;
        return _bodies[index];
    }

    /// <summary>
    ///     Gets a single AMF header object by index.
    /// </summary>
    /// <param name="index">The numerical index of the header.</param>
    /// <returns>The header referenced by index.</returns>
    public AMFHeader GetHeaderAt(int index) => _headers[index];
    public AMFHeader? TryGetHeaderAt(int index)
    {
        // check if the _headers has the index
        if (index < 0 || index >= _headers.Count)
            return null;

        return _headers[index];
    }

    /// <summary>
    ///     Gets the value of a single AMF header object by name.
    /// </summary>
    /// <param name="header">The name of the header.</param>
    /// <returns>The header referenced by name.</returns>
    public AMFHeader? GetHeader(string header) {
        for (var i = 0; _headers != null && i < _headers.Count; i++) {
            var amfHeader = _headers[i];
            if (amfHeader.Name == header)
                return amfHeader;
        }

        return null;
    }

    public List<AMFHeader> GetHeaders() => _headers;

    /// <summary>
    ///     Removes the named header from teh AMF packet.
    /// </summary>
    /// <param name="header">The name of the header.</param>
    public void RemoveHeader(string header)
    {
        for (var i = 0; _headers != null && i < _headers.Count; i++)
        {
            var amfHeader = _headers[i];
            if (amfHeader.Name == header) _headers.RemoveAt(i);
        }
    }
}
