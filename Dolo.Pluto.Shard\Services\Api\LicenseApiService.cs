using System.Text;
using Dolo.Core.Cryption;
using Dolo.Core.Http;
using Dolo.Pluto.Shard.Configuration;
using Dolo.Pluto.Shard.License;
using Dolo.Pluto.Shard.License.Feature;
using Dolo.Pluto.Shard.Services.License;

namespace Dolo.Pluto.Shard.Services.Api;

public class LicenseApiService(IAppConfiguration appConfig, ILicenseService licenseService) : ILicenseApiService
{
    private readonly string _licensePermissionsEndpoint = $"{appConfig.ApiDomain}api/toolbox/license/permissions";
    private readonly string _licenseFeaturesEndpoint = $"{appConfig.ApiDomain}api/toolbox/license/permissions/features";

    public async Task<LicensePermission[]?> GetLicensePermissionsAsync()
    {
        var encryptedToken = CustomEncryptor.DoEncrypt(licenseService.Token);
        if (string.IsNullOrEmpty(encryptedToken))
            return null;

        var response = await Http.TrySendAsync<LicensePermission[]>(cfg => {
            cfg.Content = new StringContent(encryptedToken, Encoding.UTF8, "text/plain");
            cfg.Method = HttpMethod.Post;
            cfg.Url = _licensePermissionsEndpoint;
            cfg.WithDecryption();
        });

        Console.WriteLine(response.StatusCode);

        return response.IsSuccess ? response.Body : null;
    }

    public async Task<IFeature[]?> GetLicenseFeaturesAsync()
    {
        var encryptedToken = CustomEncryptor.DoEncrypt(licenseService.Token);
        if (string.IsNullOrEmpty(encryptedToken))
            return null;

        var response = await Http.TrySendAsync<IFeature[]>(cfg => {
            cfg.Content = new StringContent(encryptedToken, Encoding.UTF8, "text/plain");
            cfg.Method = HttpMethod.Post;
            cfg.Url = _licenseFeaturesEndpoint;
            cfg.WithDecryption();
        });

        return response.IsSuccess ? response.Body : null;
    }
}
