﻿using System.Diagnostics;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
namespace Dolo.Core.Consola;

public static class Consola
{
    private static readonly object LockObject = new object();


    private static readonly Dictionary<LogLevel, ConsoleColor> LogLevelColors = new()
    {
        [LogLevel.Trace] = ConsoleColor.Gray,
        [LogLevel.Debug] = ConsoleColor.Green,
        [LogLevel.Information] = ConsoleColor.Cyan,
        [LogLevel.Warning] = ConsoleColor.Yellow,
        [LogLevel.Error] = ConsoleColor.Red,
        [LogLevel.Critical] = ConsoleColor.DarkRed
    };

    [DllImport("user32.dll")]
    private static extern bool ShowWindow(nint hWnd, int nCmdShow);

    private static nint GetConsoleWindow()
        => Process.GetCurrentProcess().MainWindowHandle;

    public static void Show() => ShowWindow(GetConsoleWindow(), 5);

    public static void Hide() => ShowWindow(GetConsoleWindow(), 0);

    public static void Space() => Console.WriteLine();

    private static void Write(string? message, LogLevel logLevel = LogLevel.Information, string? category = null, Exception? exception = null)
    {
        lock (LockObject)
        {
            var originalColor = Console.ForegroundColor;

            try
            {
                var logBuilder = new StringBuilder();

                // Timestamp
                Console.ForegroundColor = ConsoleColor.DarkGray;
                logBuilder.Append($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] ");

                // Log Level
                Console.ForegroundColor = LogLevelColors[logLevel];
                logBuilder.Append($"{logLevel,-11} ");

                // Category
                if (!string.IsNullOrEmpty(category))
                {
                    Console.ForegroundColor = ConsoleColor.DarkCyan;
                    logBuilder.Append($"[{category}] ");
                }

                // Message
                Console.ForegroundColor = originalColor;
                logBuilder.Append(message);

                Console.WriteLine(logBuilder.ToString());

                // Exception
                if (exception == null)
                    return;
                
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(exception.ToString());
            }
            finally
            {
                Console.ForegroundColor = originalColor;
            }
        }
    }

    public static void Trace(string? message, string? category = null) => Write(message, LogLevel.Trace, category);
    public static void Debug(string? message, string? category = null) => Write(message, LogLevel.Debug, category);
    public static void Information(string? message, string? category = null) => Write(message, LogLevel.Information, category);
    public static void Warning(string? message, string? category = null) => Write(message, LogLevel.Warning, category);
    public static void Error(string? message, string? category = null, Exception? exception = null) => Write(message, LogLevel.Error, category, exception);
    public static void Critical(string? message, string? category = null, Exception? exception = null) => Write(message, LogLevel.Critical, category, exception);
}