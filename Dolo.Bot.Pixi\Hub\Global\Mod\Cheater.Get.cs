﻿using Dolo.Core.Discord;
using Dolo.Database;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Cheater
    {
        [Command("get")]
        [Description("check if a cheater is in the database")]
        public async Task GetAsync(SlashCommandContext ctx, [Description("the id of the profile")] string profileId)
        {
            if (ctx.User.Id != 440584675740876810) return;

            if (string.IsNullOrEmpty(profileId))
            {
                await ctx.TryCreateResponseAsync("Please provide a profile id");
                return;
            }

            var settings = await Mongo.PixiSettings.GetFirstAsync();
            var cheater = settings!.Cheater.FirstOrDefault(a => a == profileId);

            await ctx.TryCreateResponseAsync($"Cheater: `{profileId}` > {cheater != null}");
        }
    }
}