﻿namespace Dolo.Bot.Apple.Hub.Handler;

public static class MessageReactionAdded
{
    public static async Task InvokeAsync(this MessageReactionAddedEventArgs e)
    {
        if (e.Channel == HubChannel.Roles && e.Guild.TryGetMember(e.User.Id, out var member))
        {
            // grant the female role when the user has not the male role or the famle role already
            if (e.Emoji == HubEmoji.Female && !member.Roles.ContainsMany(HubRoles.Female, HubRoles.Male))
                await member.TryGrantRoleAsync(HubRoles.Female);

            // grant the male role when the user has not the female role or the male role already
            if (e.Emoji == HubEmoji.Male && !member.Roles.ContainsMany(HubRoles.Female, HubRoles.Male))
                await member.TryGrantRoleAsync(HubRoles.Male);

            // grant the germany role
            if (e.Emoji == HubEmoji.Germany)
                await member.TryGrantRoleAsync(HubRoles.Germany);

            // grant the french role
            if (e.Emoji == HubEmoji.France)
                await member.TryGrantRoleAsync(HubRoles.France);

            // grant the turkish role
            if (e.Emoji == HubEmoji.Turkey)
                await member.TryGrantRoleAsync(HubRoles.Turkey);

            // grant the polish role
            if (e.Emoji == HubEmoji.Poland)
                await member.TryGrantRoleAsync(HubRoles.Poland);

            // grant the changelog notify role
            if (e.Emoji == HubEmoji.Changelog)
                await member.TryGrantRoleAsync(HubRoles.Changelog);

            // grant the news notify role
            if (e.Emoji == HubEmoji.News)
                await member.TryGrantRoleAsync(HubRoles.News);

            // grant the event notify role
            if (e.Emoji == HubEmoji.Tada)
                await member.TryGrantRoleAsync(HubRoles.Event);
        }
    }
}