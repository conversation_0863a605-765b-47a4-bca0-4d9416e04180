﻿using Dolo.Core.Extension;
using System.Text;
namespace Dolo.Bot.Log.Hub;

internal class HubEmbed
{
    public static DiscordEmbed Joined(DiscordMember member) => new DiscordEmbedBuilder()
        .WithTitle("Joined")
        .WithColor(new("63FE5F"))
        .WithThumbnail(member.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**User**](https://a) • {member.Mention}")
            .AppendLine($"[**Name**](https://a) • {member.Username}")
            .AppendLine($"[**Created**](https://a) • {member.CreationTimestamp}")
            .AppendLine($"[**Joined**](https://a) • {member.JoinedAt}")
            .ToString()).Build();

    public static DiscordEmbed Left(DiscordMember member) => new DiscordEmbedBuilder()
        .WithTitle("Left")
        .WithColor(new("F92651"))
        .WithThumbnail(member.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**User**](https://a) • {member.Mention}")
            .AppendLine($"[**Name**](https://a) • {member.Username}")
            .AppendLine($"[**Created**](https://a) • {member.CreationTimestamp}")
            .AppendLine($"[**Joined**](https://a) • {member.JoinedAt}")
            .ToString()).Build();

    public static DiscordEmbed MessageCreated(DiscordMessage message) => new DiscordEmbedBuilder()
        .WithTitle("Created")
        .WithColor(new("63FE5F"))
        .WithThumbnail(message.Author?.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**Name**](https://a) • {message.Author?.Username ?? "Unknown User"}")
            .AppendLine($"[**User**](https://a) • {message.Author?.Mention ?? "Unknown User"}")
            .AppendLine($"[**Channel**](https://a) • {message.Channel?.Mention ?? "Unknown Channel"}")
            .AppendLine()
            .AppendLine("**Message**")
            .Append($"```{message.Content.IsNullOrEmptyThen("Attachment/Picture/Embed")}```")
            .ToString()).Build();

    public static DiscordEmbed MessageDeleted(DiscordMessage? message) => new DiscordEmbedBuilder()
        .WithTitle("Deleted")
        .WithColor(new("F92651"))
        .WithThumbnail(message?.Author?.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**Name**](https://a) • {message?.Author?.Username ?? "Unknown User"}")
            .AppendLine($"[**User**](https://a) • {message?.Author?.Mention ?? "Unknown User"}")
            .AppendLine($"[**Channel**](https://a) • {message?.Channel?.Mention ?? "Unknown Channel"}")
            .AppendLine()
            .AppendLine("**Message**")
            .Append($"```{message?.Content?.IsNullOrEmptyThen("Attachment/Picture/Embed") ?? "Message not cached"}```")
            .ToString()).Build();


    public static DiscordEmbed MessageUpdated(DiscordMessage message, DiscordMessage messagebefore) => new DiscordEmbedBuilder()
        .WithTitle("Updated")
        .WithColor(new("FEC456"))
        .WithThumbnail(message.Author?.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**Name**](https://a) • {message.Author?.Username ?? "Unknown User"}")
            .AppendLine($"[**User**](https://a) • {message.Author?.Mention ?? "Unknown User"}")
            .AppendLine($"[**Channel**](https://a) • {message.Channel?.Mention ?? "Unknown Channel"}")
            .AppendLine()
            .AppendLine("**Message Before**")
            .Append($"```{messagebefore?.Content.IsNullOrEmptyThen("Attachment/Picture/Embed") ?? "Message not cached"}```")
            .AppendLine("**Message After**")
            .Append($"```{message.Content.IsNullOrEmptyThen("Attachment/Picture/Embed")}```")
            .ToString()).Build();

    public static DiscordEmbed InteractionCreated(DiscordInteraction interaction) => new DiscordEmbedBuilder()
        .WithTitle("Slash Command Executed")
        .WithColor(new("5865F2"))
        .WithThumbnail(interaction.User?.AvatarUrl)
        .WithDescription(new StringBuilder()
            .AppendLine($"[**User**](https://a) • {interaction.User?.Mention ?? "Unknown User"}")
            .AppendLine($"[**Name**](https://a) • {interaction.User?.Username ?? "Unknown User"}")
            .AppendLine($"[**Channel**](https://a) • {interaction.Channel?.Mention ?? "Unknown Channel"}")
            .AppendLine($"[**Command**](https://a) • `/{interaction.Data?.Name ?? "Unknown"}`")
            .AppendLine()
            .AppendLine("**Parameters**")
            .Append(FormatSlashCommandOptions(interaction.Data?.Options))
            .ToString())
        .WithTimestamp(DateTimeOffset.UtcNow)
        .Build();

    public static DiscordEmbed AttachmentsDeleted(DiscordMessage? message, List<HubCache.CachedAttachment> attachments)
    {
        var embed = new DiscordEmbedBuilder()
            .WithTitle("🗑️ Attachments Deleted")
            .WithColor(new("FF6B6B"))
            .WithThumbnail(message?.Author?.AvatarUrl)
            .WithDescription(new StringBuilder()
                .AppendLine($"👤 **User:** {message?.Author?.Mention ?? "Unknown User"}")
                .AppendLine($"📝 **Name:** {message?.Author?.Username ?? "Unknown User"}")
                .AppendLine($"📍 **Channel:** {message?.Channel?.Mention ?? "Unknown Channel"}")
                .AppendLine($"🆔 **Message ID:** `{message?.Id ?? 0}`")
                .AppendLine()
                .AppendLine($"**{attachments.Count} Attachment(s) Deleted:**")
                .AppendLine(FormatAttachments(attachments))
                .ToString())
            .WithTimestamp(DateTimeOffset.UtcNow);

        // Add the first cloned image as embed image if available
        var firstImage = attachments.FirstOrDefault(a => a.IsImage && a.IsCloned);
        if (firstImage != null)
        {
            embed.WithImageUrl(firstImage.ClonedUrl);
        }

        return embed.Build();
    }

    public static DiscordEmbed AttachmentsCreated(DiscordMessage message, List<HubCache.CachedAttachment> attachments)
    {
        var embed = new DiscordEmbedBuilder()
            .WithTitle("📎 Attachments Posted")
            .WithColor(new("4CAF50"))
            .WithThumbnail(message.Author?.AvatarUrl)
            .WithDescription(new StringBuilder()
                .AppendLine($"👤 **User:** {message.Author?.Mention ?? "Unknown User"}")
                .AppendLine($"📝 **Name:** {message.Author?.Username ?? "Unknown User"}")
                .AppendLine($"📍 **Channel:** {message.Channel?.Mention ?? "Unknown Channel"}")
                .AppendLine($"🆔 **Message ID:** `{message.Id}`")
                .AppendLine()
                .AppendLine($"**{attachments.Count} Attachment(s) Posted:**")
                .AppendLine(FormatAttachments(attachments))
                .ToString())
            .WithTimestamp(DateTimeOffset.UtcNow);

        // Add the first image as embed image if available
        var firstImage = attachments.FirstOrDefault(a => a.IsImage);
        if (firstImage != null)
        {
            // Use cloned URL if available, otherwise original
            var imageUrl = firstImage.ClonedUrl ?? firstImage.OriginalUrl;
            embed.WithImageUrl(imageUrl);
        }

        return embed.Build();
    }

    private static string FormatSlashCommandOptions(IEnumerable<DiscordInteractionDataOption>? options)
    {
        if (options == null || !options.Any())
            return "```None```";

        var sb = new StringBuilder();
        sb.AppendLine("```");

        foreach (var option in options)
        {
            sb.AppendLine($"{option.Name}: {option.Value ?? "null"}");
        }

        sb.Append("```");
        return sb.ToString();
    }

    private static string FormatAttachments(List<HubCache.CachedAttachment> attachments)
    {
        var sb = new StringBuilder();

        foreach (var attachment in attachments.Take(5)) // Limit to 5 attachments to avoid embed limits
        {
            var sizeKb = attachment.Size / 1024.0;
            var dimensions = attachment.Width.HasValue && attachment.Height.HasValue
                ? $" ({attachment.Width}x{attachment.Height})"
                : "";

            var type = attachment.IsImage ? "🖼️" : attachment.IsVideo ? "🎥" : "📎";
            var status = attachment.IsCloned ? "✅ Preserved" : "⏳ Cloning...";

            sb.AppendLine($"{type} **{attachment.FileName}**{dimensions}");
            sb.AppendLine($"📊 Size: {sizeKb:F1} KB | Status: {status}");

            // Show cloned URL if available, otherwise original
            var displayUrl = attachment.ClonedUrl ?? attachment.OriginalUrl;
            sb.AppendLine($"🔗 [View Attachment]({displayUrl})");
            sb.AppendLine();
        }

        if (attachments.Count > 5)
            sb.AppendLine($"*... and {attachments.Count - 5} more attachments*");

        return sb.ToString();
    }
}
