﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Collections.Generic;
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using IServiceProvider=Dolo.Core.AMF3.Fluorine.Messaging.Api.IServiceProvider;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     The scope object.
///     A statefull object shared between a group of clients connected to the same
///     context path. Scopes are arranged in a hierarchical way, so its possible for
///     a scope to have a parent. If a client is connect to a scope then they are
///     also connected to its parent scope. The scope object is used to access
///     resources, shared object, streams, etc.
///     The following are all names for scopes: application, room, place, lobby.
/// </summary>
internal class Scope : BasicScope, IScope
{
    private static readonly string ScopeType = "scope";
    public static string Separator = ":";

    /// <summary>
    ///     String, IBasicScope
    /// </summary>
    private readonly Dictionary<string, IBasicScope> _children = new();

    /// <summary>
    ///     IClient, Set(IConnection)
    /// </summary>
    private readonly Dictionary<IClient, CopyOnWriteArraySet<IConnection>> _clients = new();

    private IScopeContext _context;
    private IScopeHandler _handler;
    protected ServiceContainer _serviceContainer;

    protected Scope() : this(string.Empty)
    {}

    protected Scope(string name)
        : this(name, null)
    {}

    protected Scope(string name, IServiceProvider serviceProvider)
        : base(null, ScopeType, name, false)
        => _serviceContainer = new(serviceProvider);

    public bool IsEnabled { get; set; } = true;

    public bool IsRunning { get; private set; }

    public bool AutoStart { get; set; } = true;

    public bool HasContext
        => _context != null;

    /// <summary>
    ///     Adds the specified service to the scope.
    /// </summary>
    /// <param name="serviceType">The type of service to add.</param>
    /// <param name="service">An instance of the service type to add.</param>
    public void AddService(Type serviceType, object service)
    {
        _serviceContainer.AddService(serviceType, service);
    }

    /// <summary>
    ///     Adds the specified service to the scope.
    /// </summary>
    /// <param name="serviceType">The type of service to add.</param>
    /// <param name="service">An instance of the service type to add.</param>
    /// <param name="promote"></param>
    public void AddService(Type serviceType, object service, bool promote)
    {
        _serviceContainer.AddService(serviceType, service, promote);
    }

    /// <summary>
    ///     Removes the specified service type from the service container.
    /// </summary>
    /// <param name="serviceType">The type of service to remove.</param>
    public void RemoveService(Type serviceType)
    {
        _serviceContainer.RemoveService(serviceType);
    }

    /// <summary>
    ///     Removes the specified service type from the service container.
    /// </summary>
    /// <param name="serviceType">The type of service to remove.</param>
    /// <param name="promote"></param>
    public void RemoveService(Type serviceType, bool promote)
    {
        _serviceContainer.RemoveService(serviceType, promote);
    }

    /// <summary>
    ///     Gets the service object of the specified type.
    /// </summary>
    /// <param name="serviceType">An object that specifies the type of service object to get.</param>
    /// <returns>
    ///     A service object of type serviceType or a null reference (Nothing in Visual Basic) if there is no service
    ///     object of type serviceType.
    /// </returns>
    public virtual object GetService(Type serviceType)
        => _serviceContainer.GetService(serviceType);

    #region IEnumerable Members

    public override IEnumerator GetEnumerator()
        => _children.Values.GetEnumerator();

    #endregion

    public void Init()
    {
        if (HasParent)
            if (!Parent.HasChildScope(Name))
                if (!Parent.AddChildScope(this))
                    return;

        if (AutoStart) Start();
    }

    /// <summary>
    ///     Uninitialize scope and unregister from parent.
    /// </summary>
    public void Uninit()
    {
        foreach (var child in _children.Values)
            (child as Scope)?.Uninit();
        Stop();
        if (HasParent)
            if (Parent.HasChildScope(Name))
                Parent.RemoveChildScope(this);
    }


    /// <summary>
    ///     Starts scope.
    /// </summary>
    /// <returns><code>true</code> if scope has handler and it's start method returned true, <code>false</code> otherwise.</returns>
    public bool Start()
    {
        lock (SyncRoot)
        {
            var result = false;
            if (IsEnabled && !IsRunning)
            {
                if (HasHandler)
                    // Only start if scope handler allows it
                    try
                    {
                        // If we dont have a handler of our own dont try to start it
                        if (_handler != null)
                            result = _handler.Start(this);
                    }
                    catch (Exception)
                    {}
                else
                    result = true;

                IsRunning = result;
            }

            return result;
        }
    }

    /// <summary>
    ///     Stops scope.
    /// </summary>
    public void Stop()
    {
        lock (SyncRoot)
        {
            if (IsEnabled && IsRunning && HasHandler)
                try
                {
                    //If we dont have a handler of our own don't try to stop it
                    _handler?.Stop(this);
                }
                catch (Exception)
                {}

            _serviceContainer.Shutdown();
            IsRunning = false;
        }
    }

    protected override void Free()
    {
        if (HasParent) Parent.RemoveChildScope(this);
        if (HasHandler) Handler.Stop(this);
    }


    private sealed class PrefixFilteringStringEnumerator : IEnumerator
    {
        private readonly object[] _enumerable;
        private readonly string _prefix;
        private string _currentElement;
        private int _index;


        internal PrefixFilteringStringEnumerator(ICollection enumerable, string prefix)
        {
            _prefix = prefix;
            _index = -1;
            _enumerable = new object[enumerable.Count];
            enumerable.CopyTo(_enumerable, 0);
        }

        #region IEnumerator Members

        public void Reset()
        {
            _currentElement = null;
            _index = -1;
        }

        public string Current
        {
            get
            {
                if (_index == -1)
                    throw new InvalidOperationException("Enum not started.");
                if (_index >= _enumerable.Length)
                    throw new InvalidOperationException("Enumeration ended.");
                return _currentElement;
            }
        }

        object IEnumerator.Current
        {
            get
            {
                if (_index == -1)
                    throw new InvalidOperationException("Enum not started.");
                if (_index >= _enumerable.Length)
                    throw new InvalidOperationException("Enumeration ended.");
                return _currentElement;
            }
        }

        public bool MoveNext()
        {
            while (_index < _enumerable.Length - 1)
            {
                _index++;

                var element = _enumerable[_index] as string;
                if (element.StartsWith(_prefix))
                {
                    _currentElement = element;
                    return true;
                }
            }

            _index = _enumerable.Length;
            return false;
        }

        #endregion
    }

    private sealed class ConnectionIterator : IEnumerator
    {
        private readonly IDictionaryEnumerator _setIterator;
        private IEnumerator _connectionIterator;

        public ConnectionIterator(Scope scope)
            => _setIterator = scope._clients.GetEnumerator();

        #region IEnumerator Members

        public object Current
            => _connectionIterator.Current;

        public bool MoveNext()
        {
            if (_connectionIterator != null && _connectionIterator.MoveNext())
                // More connections for this client
                return true;
            if (!_setIterator.MoveNext())
                // No more clients
                return false;
            _connectionIterator = (_setIterator.Value as CopyOnWriteArraySet<IConnection>).GetEnumerator();
            while (_connectionIterator != null)
            {
                if (_connectionIterator.MoveNext())
                    // Found client with connections
                    return true;
                if (!_setIterator.MoveNext())
                    // No more clients
                    return false;
                // Advance to next client
                _connectionIterator = (_setIterator.Value as CopyOnWriteArraySet<IConnection>).GetEnumerator();
            }

            return false;
        }

        public void Reset()
        {
            _connectionIterator = null;
            _setIterator.Reset();
        }

        #endregion
    }

    #region IScope Members

    public bool Connect(IConnection connection)
        => Connect(connection, null);

    public bool Connect(IConnection connection, object[] parameters)
    {
        if (HasParent && !Parent.Connect(connection, parameters))
            return false;
        if (HasHandler && !Handler.Connect(connection, this, parameters))
            return false;
        var client = connection.Client;
        if (!connection.IsConnected)
            // Timeout while connecting client
            return false;
        //We would not get this far if there is no handler
        if (HasHandler && !Handler.Join(client, this)) return false;
        if (!connection.IsConnected)
            // Timeout while connecting client
            return false;

        CopyOnWriteArraySet<IConnection> connections = null;
        if (_clients.ContainsKey(client))
            connections = _clients[client];
        else
        {
            connections = new();
            _clients[client] = connections;
        }

        connections.Add(connection);
        AddEventListener(connection);
        return true;
    }

    public void Disconnect(IConnection connection)
    {
        // We call the disconnect handlers in reverse order they were called
        // during connection, i.e. roomDisconnect is called before
        // appDisconnect.
        var client = connection.Client;
        if (_clients.ContainsKey(client))
        {
            var connections = _clients[client];
            connections.Remove(connection);
            IScopeHandler handler = null;
            if (HasHandler)
            {
                handler = Handler;
                try
                {
                    handler.Disconnect(connection, this);
                }
                catch (Exception)
                {}
            }

            if (connections.Count == 0)
            {
                _clients.Remove(client);
                if (handler != null)
                    try
                    {
                        // there may be a timeout here 
                        handler.Leave(client, this);
                    }
                    catch (Exception)
                    {}
            }

            RemoveEventListener(connection);
        }

        if (HasParent)
            Parent.Disconnect(connection);
    }

    public IScopeContext Context
    {
        get
        {
            if (!HasContext && HasParent)
                return Parent.Context;
            return _context;
        }
        set => _context = value;
    }

    public bool HasChildScope(string name)
        => _children.ContainsKey(ScopeType + Separator + name);

    public bool HasChildScope(string type, string name)
        => _children.ContainsKey(type + Separator + name);

    public bool CreateChildScope(string name)
    {
        var scope = new Scope(name, _serviceContainer);
        scope.Parent = this;
        return AddChildScope(scope);
    }

    public bool AddChildScope(IBasicScope scope)
    {
        if (HasHandler && !Handler.AddChildScope(scope))
            return false;

        if (scope is IScope)
            // Start the scope
            if (HasHandler && !Handler.Start((IScope)scope))
                return false;
        _children[scope.Type + Separator + scope.Name] = scope;
        return true;
    }

    public void RemoveChildScope(IBasicScope scope)
    {
        if (scope is IScope)
            if (HasHandler)
                Handler.Stop((IScope)scope);
        var child = scope.Type + Separator + scope.Name;
        if (_children.ContainsKey(child))
            _children.Remove(child);
        if (HasHandler)
            Handler.RemoveChildScope(scope);
    }

    public ICollection GetScopeNames()
        => _children.Keys;

    public IEnumerator GetBasicScopeNames(string type)
    {
        if (type == null)
            return _children.Keys.GetEnumerator();
        return new PrefixFilteringStringEnumerator(_children.Keys, type + Separator);
    }

    public IBasicScope GetBasicScope(string type, string name)
    {
        var child = type + Separator + name;
        if (_children.ContainsKey(child))
            return _children[child];
        return null;
    }

    public IScope GetScope(string name)
    {
        var child = ScopeType + Separator + name;
        if (_children.ContainsKey(child))
            return _children[child] as IScope;
        return null;
    }

    public ICollection GetClients()
        => _clients.Keys;

    public bool HasHandler
        => _handler != null || HasParent && Parent.HasHandler;

    public IScopeHandler Handler
    {
        get
        {
            if (_handler != null)
                return _handler;
            if (HasParent)
                return Parent.Handler;
            return null;
        }
        set
        {
            _handler = value;
            (_handler as IScopeAware)?.SetScope(this);
        }
    }

    public virtual string ContextPath
    {
        get
        {
            if (HasContext)
                return string.Empty;
            if (HasParent)
                return Parent.ContextPath + "/" + Name;
            return null;
        }
    }

    public IEnumerator GetConnections()
        => new ConnectionIterator(this);

    /// <summary>
    ///     Returns scope context.
    /// </summary>
    /// <returns></returns>
    public IScopeContext GetContext()
    {
        if (!HasContext && HasParent)
            return _parent.Context;
        return _context;
    }

    public ICollection LookupConnections(IClient client)
    {
        if (_clients.ContainsKey(client))
            return _clients[client];
        return null;
    }

    #endregion

    #region IBasicScope Members

    #endregion
}