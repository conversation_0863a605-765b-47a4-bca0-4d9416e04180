﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Security;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Messages;

/// <summary>
///     The ErrorMessage class is used to report errors within the messaging system.
///     An error message only occurs in response to a message sent within the system.
/// </summary>
internal class ErrorMessage : AcknowledgeMessage
{
    /// <summary>
    ///     Client authentication fault code.
    /// </summary>
    public const string ClientAuthenticationError = "Client.Authentication";
    /// <summary>
    ///     Client authorization fault code.
    /// </summary>
    public const string ClientAuthorizationError = "Client.Authorization";

    /// <summary>
    ///     Initializes a new instance of the ErrorMessage class.
    /// </summary>
    public ErrorMessage()
    {}
    /// <summary>
    ///     The fault code for the error.
    ///     This value typically follows the convention of "[outer_context].[inner_context].[issue]".
    ///     For example: "Channel.Connect.Failed", "Server.Call.Failed"
    /// </summary>
    public string faultCode
    {
        get;
        set;
    }
    /// <summary>
    ///     A simple description of the error.
    /// </summary>
    public string faultString
    {
        get;
        set;
    }
    /// <summary>
    ///     Detailed description of what caused the error. This is typically a stack trace from the remote destination
    /// </summary>
    public string faultDetail
    {
        get;
        set;
    }
    /// <summary>
    ///     Root cause for the error.
    /// </summary>
    public object rootCause
    {
        get;
        set;
    }
    /// <summary>
    ///     Extended data that the remote destination can choose to associate with this error for custom error processing on
    ///     the client.
    /// </summary>
    public ASObject extendedData
    {
        get;
        set;
    }

    internal static ErrorMessage GetErrorMessage(IMessage message, Exception exception)
    {
        MessageException me = null;
        if (exception is MessageException)
            me = exception as MessageException;
        else
            me = new(exception);
        var errorMessage = me.GetErrorMessage();
        if (message.clientId != null)
            errorMessage.clientId = message.clientId;
        else
            errorMessage.clientId = Guid.NewGuid().ToString("D");
        errorMessage.correlationId = message.messageId;
        errorMessage.destination = message.destination;
        if (exception is SecurityException)
            errorMessage.faultCode = ClientAuthenticationError;
        if (exception is UnauthorizedAccessException)
            errorMessage.faultCode = ClientAuthorizationError;
        return errorMessage;
    }
}