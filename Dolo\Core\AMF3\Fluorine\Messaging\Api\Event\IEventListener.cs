﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Event;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal interface IEventListener
{
    /// <summary>
    ///     Event notification.
    /// </summary>
    /// <param name="evt">The event object.</param>
    void NotifyEvent(IEvent evt);
}