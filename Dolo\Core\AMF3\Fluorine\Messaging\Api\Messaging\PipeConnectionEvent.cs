﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Messaging;

/// <summary>
///     Event object corresponds to the connect/disconnect events among providers/consumers and pipes.
/// </summary>
internal class PipeConnectionEvent
{
    /// <summary>
    ///     A provider connects as pull mode.
    /// </summary>
    public const int PROVIDER_CONNECT_PULL = 0;
    /// <summary>
    ///     A provider connects as push mode.
    /// </summary>
    public const int PROVIDER_CONNECT_PUSH = 1;
    /// <summary>
    ///     A provider disconnects.
    /// </summary>
    public const int PROVIDER_DISCONNECT = 2;
    /// <summary>
    ///     A consumer connects as pull mode.
    /// </summary>
    public const int CONSUMER_CONNECT_PULL = 3;
    /// <summary>
    ///     A consumer connects as push mode.
    /// </summary>
    public const int CONSUMER_CONNECT_PUSH = 4;
    /// <summary>
    ///     A consumer disconnects.
    /// </summary>
    public const int CONSUMER_DISCONNECT = 5;
    /// <summary>
    ///     Consumer.
    /// </summary>
    private IConsumer _consumer;
    /// <summary>
    ///     Params map.
    /// </summary>
    private Dictionary<string, object> _parameterMap;

    /// <summary>
    ///     Provider.
    /// </summary>
    private IProvider _provider;

    /// <summary>
    ///     Event type.
    /// </summary>
    private int _type;
    /// <summary>
    ///     Construct an object with the specific pipe as the source.
    /// </summary>
    /// <param name="source">A pipe that triggers this event.</param>
    public PipeConnectionEvent(object source) => Source = source;
    /// <summary>
    ///     Gets or sets pipe connection provider.
    /// </summary>
    public IProvider Provider
    {
        get => _provider;
        set => _provider = value;
    }
    /// <summary>
    ///     Gets or sets pipe connection consumer.
    /// </summary>
    public IConsumer Consumer
    {
        get => _consumer;
        set => _consumer = value;
    }
    /// <summary>
    ///     Gets or sets event type.
    /// </summary>
    public int Type
    {
        get => _type;
        set => _type = value;
    }
    /// <summary>
    ///     Gets or sets event parameters.
    /// </summary>
    public Dictionary<string, object> ParameterMap
    {
        get => _parameterMap;
        set => _parameterMap = value;
    }
    /// <summary>
    ///     Gets or sets the vent source.
    /// </summary>
    public object Source
    {
        get;
        set;
    }
}