using System.Collections.Concurrent;
using System.Net.Sockets;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class ConnectionManager : IDisposable
{
    private readonly ConcurrentDictionary<string, ManagedConnection> _connections = new();
    private readonly Timer _healthCheckTimer;
    private readonly ILogger<ConnectionManager> _logger;
    private readonly TimeSpan _connectionTimeout;
    private bool _disposed;

    public ConnectionManager(ILogger<ConnectionManager> logger, TimeSpan? connectionTimeout = null)
    {
        _logger = logger;
        _connectionTimeout = connectionTimeout ?? TimeSpan.FromMinutes(5);
        _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    public event EventHandler<ConnectionClosedEventArgs>? ConnectionClosed;

    public string RegisterConnection(TcpClient client, string hostname, int port, string protocol = "HTTPS")
    {
        var connectionId = GenerateConnectionId(hostname, port);
        var connection = new ManagedConnection(connectionId, client, hostname, port, protocol);

        _connections.TryAdd(connectionId, connection);

        _logger.LogDebug("🔗 Registered connection {ConnectionId} for {Host}:{Port} from {RemoteEndpoint}",
            connectionId, hostname, port, client.Client.RemoteEndPoint);

        return connectionId;
    }

    public void RegisterConnectionWithId(TcpClient client, string connectionId, string hostname, int port, string protocol = "HTTPS")
    {
        var connection = new ManagedConnection(connectionId, client, hostname, port, protocol);

        _connections.TryAdd(connectionId, connection);

        _logger.LogDebug("🔗 Registered connection {ConnectionId} for {Host}:{Port} from {RemoteEndpoint} (using provided ID)",
            connectionId, hostname, port, client.Client.RemoteEndPoint);
    }

    public ManagedConnection? GetConnection(string connectionId)
    {
        _connections.TryGetValue(connectionId, out var connection);
        return connection;
    }

    public void CloseConnection(string connectionId, string reason = "Normal closure")
    {
        if (_connections.TryRemove(connectionId, out var connection))
        {
            _logger.LogDebug("🔌 Closing connection {ConnectionId}: {Reason}", connectionId, reason);
            
            connection.Close();
            ConnectionClosed?.Invoke(this, new ConnectionClosedEventArgs(connectionId, reason));
        }
    }

    public void UpdateLastActivity(string connectionId)
    {
        if (_connections.TryGetValue(connectionId, out var connection))
        {
            connection.UpdateLastActivity();
        }
    }

    public (int ActiveConnections, TimeSpan OldestConnectionAge) GetStatistics()
    {
        var connections = _connections.Values.ToList();
        var activeCount = connections.Count;
        var oldestAge = connections.Count > 0 
            ? connections.Max(c => DateTime.UtcNow - c.CreatedAt)
            : TimeSpan.Zero;

        return (activeCount, oldestAge);
    }

    private static string GenerateConnectionId(string hostname, int port)
    {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        var random = Random.Shared.Next(1000, 9999);
        return $"{hostname}:{port}:{timestamp}:{random}";
    }

    private void PerformHealthCheck(object? state)
    {
        if (_disposed) return;

        var expiredConnections = _connections.Values
            .Where(c => c.IsExpired(_connectionTimeout) || !c.IsConnected)
            .ToList();

        foreach (var connection in expiredConnections)
        {
            var reason = !connection.IsConnected ? "Connection lost" : "Connection timeout";
            CloseConnection(connection.ConnectionId, reason);
        }

        if (expiredConnections.Count > 0)
        {
            _logger.LogDebug("🧹 Cleaned up {Count} expired connections", expiredConnections.Count);
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _healthCheckTimer?.Dispose();

        foreach (var connection in _connections.Values)
        {
            connection.Close();
        }

        _connections.Clear();
    }
}

public sealed class ManagedConnection : IDisposable
{
    private readonly TcpClient _client;
    private DateTime _lastActivity;
    private readonly Lock _lock = new();
    private bool _disposed;

    public string ConnectionId { get; }
    public string Hostname { get; }
    public int Port { get; }
    public string Protocol { get; }
    public DateTime CreatedAt { get; } = DateTime.UtcNow;
    public DateTime LastActivity => _lastActivity;
    public bool IsConnected => _client.Connected;

    public ManagedConnection(string connectionId, TcpClient client, string hostname, int port, string protocol)
    {
        ConnectionId = connectionId;
        _client = client;
        Hostname = hostname;
        Port = port;
        Protocol = protocol;
        _lastActivity = DateTime.UtcNow;
    }

    public void UpdateLastActivity()
    {
        lock (_lock)
        {
            _lastActivity = DateTime.UtcNow;
        }
    }

    public bool IsExpired(TimeSpan timeout)
    {
        lock (_lock)
        {
            return DateTime.UtcNow - _lastActivity > timeout;
        }
    }

    public NetworkStream? GetStream()
    {
        if (_disposed || !_client.Connected) return null;
        
        try
        {
            return _client.GetStream();
        }
        catch
        {
            return null;
        }
    }

    public void Close()
    {
        if (_disposed) return;

        try
        {
            _client.Close();
        }
        catch
        {
            // Ignore close errors
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        Close();
        _client.Dispose();
    }
}

public sealed class ConnectionClosedEventArgs : EventArgs
{
    public string ConnectionId { get; }
    public string Reason { get; }
    public DateTime ClosedAt { get; } = DateTime.UtcNow;

    public ConnectionClosedEventArgs(string connectionId, string reason)
    {
        ConnectionId = connectionId;
        Reason = reason;
    }
}
