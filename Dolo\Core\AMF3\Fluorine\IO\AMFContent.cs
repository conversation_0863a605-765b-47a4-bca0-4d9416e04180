﻿using Dolo.Core.Extension;
using Dolo.Core.AMF3.Fluorine.IO;
namespace Dolo.Core.AMF3.Fluorine.IO;

public class AMFContent
{
    public bool HasValue
        => RawContent?.Length != 0;
    public bool HasException
        => Content is null;
    public object? Content { get; set; }
    public string? RawContent { get; set; }
    public List<AMFHeader>? Headers { get; set; } 
    public List<AMFBody>? Bodies { get; set; } 
    public AMFHeader? Header { get; set; }
    public string? TryReadRawContent() {
        if (!HasValue)
            return default;


        var unicode = new[]
        {
            "a", "b", "c", "d", "e", "f", "g", "h", "i",
            "j", "k", "l", "m", "n", "o", "p", "q", "r",
            "s", "t", "u", "v", "w", "x", "y", "z", "�=",
            "�q", "O", "1", "2", "3", "4", "5", "6", "7",
            "8", "9", "/", "\\", ";", "[", "]", "#", "", "", "", "",
            "", "",
            "", "", "�]", "�1", "�2", "�3", "�4", "�5", "�6", "�7", "�8", "�9", "1", "", "�", "	", "\n"
        };

        var match = RawContent!.Split(unicode, StringSplitOptions.RemoveEmptyEntries);

        return match.TryGetIndex(6);
    }
}
