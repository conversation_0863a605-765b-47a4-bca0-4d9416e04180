using System.Net.Sockets;
using System.Text;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class TunnelingService
{
    private readonly ILogger<TunnelingService> _logger;

    public TunnelingService(ILogger<TunnelingService> logger)
    {
        _logger = logger;
    }

    public async Task TunnelTrafficAsync(NetworkStream clientStream, string hostname, int port)
    {
        try
        {
            _logger.LogDebug("Starting tunnel to {Host}:{Port}", hostname, port);

            using var serverClient = new TcpClient();
            await serverClient.ConnectAsync(hostname, port).ConfigureAwait(false);
            using var serverStream = serverClient.GetStream();

            await clientStream.WriteAsync("HTTP/1.1 200 Connection established\r\n\r\n"u8.ToArray()).ConfigureAwait(false);

            var clientToServer = RelayDataAsync(clientStream, serverStream, $"Client->Server({hostname}:{port})");
            var serverToClient = RelayDataAsync(serverStream, clientStream, $"Server->Client({hostname}:{port})");

            await Task.WhenAny(clientToServer, serverToClient).ConfigureAwait(false);

            _logger.LogDebug("Tunnel completed for {Host}:{Port}", hostname, port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Tunnel failed for {Host}:{Port}: {Error}", hostname, port, ex.Message);
        }
    }

    public async Task TunnelHttpTrafficAsync(NetworkStream clientStream, string hostname, int port, string initialRequest)
    {
        try
        {
            _logger.LogDebug("Starting HTTP tunnel to {Host}:{Port}", hostname, port);

            using var serverClient = new TcpClient();
            await serverClient.ConnectAsync(hostname, port).ConfigureAwait(false);
            using var serverStream = serverClient.GetStream();

            var requestBytes = Encoding.UTF8.GetBytes(initialRequest);
            await serverStream.WriteAsync(requestBytes.AsMemory()).ConfigureAwait(false);

            var clientToServer = RelayDataAsync(clientStream, serverStream, $"HTTP-Client->Server({hostname}:{port})");
            var serverToClient = RelayDataAsync(serverStream, clientStream, $"HTTP-Server->Client({hostname}:{port})");

            await Task.WhenAny(clientToServer, serverToClient).ConfigureAwait(false);

            _logger.LogDebug("HTTP tunnel completed for {Host}:{Port}", hostname, port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "HTTP tunnel failed for {Host}:{Port}: {Error}", hostname, port, ex.Message);
        }
    }

    private async Task RelayDataAsync(Stream from, Stream to, string direction)
    {
        try
        {
            var buffer = new byte[8192];
            var totalBytes = 0L;

            while (true)
            {
                var received = await from.ReadAsync(buffer.AsMemory()).ConfigureAwait(false);
                if (received == 0) break;

                await to.WriteAsync(buffer.AsMemory(0, received)).ConfigureAwait(false);
                totalBytes += received;
            }

            _logger.LogDebug("{Direction}: {TotalBytes} bytes relayed", direction, totalBytes);
        }
        catch (Exception ex)
        {
            _logger.LogDebug("{Direction} relay ended: {Error}", direction, ex.Message);
        }
    }
}
