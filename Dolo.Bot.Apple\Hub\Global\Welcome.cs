﻿namespace Dolo.Bot.Apple.Hub.Global;

public class Welcome 
{
    [Command("welcome")]

[Description("welcome a new server-member")]
    public async Task WelcomeAsync(SlashCommandContext ctx, [Description("the member to welcome")] DiscordUser member)
    {
        await ctx.Interaction.DeferAsync();
        await ctx.TryEditResponseAsync($"**Hey** {member.Mention}, **Welcome to** `{ctx.Guild.Name}` {HubEmoji.WhiteHeart}");
    }
}