﻿using Dolo.Core.Discord;
using Dolo.Database;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Embed
    {
        [Command("get")]

[Description("get the embed color")]
        public async Task GetAsync(SlashCommandContext ctx)
        {
            if (ctx.User.Id != 440584675740876810) return;

            var color = await Mongo.PixiSettings.GetFirstAsync();
            await ctx.TryCreateResponseAsync($"Embed color: `{color?.EmbedColor}`");
        }
    }
}