﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Cheater
    {
        [Command("whitelist")]

[Description("add a whitelist user")]
        public async Task WhitelistAsync(SlashCommandContext ctx, [Description("the id of the profile")] string profileId)
        {
            if (ctx.User.Id != 440584675740876810) return;

            if (string.IsNullOrEmpty(profileId))
            {
                await ctx.TryCreateResponseAsync("Please provide a profile id");
                return;
            }

            var settings = await Mongo.PixiSettings.GetFirstAsync();
            if (settings!.<PERSON><PERSON>hitelist(profileId))
            {
                await ctx.TryCreateResponseAsync("This profile has already been added to the whitelist.");
                return;
            }

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty, Builders<PixiSettings>.Update.Push(x => x.Whitelist, profileId));
            await ctx.TryCreateResponseAsync($"User: `{profileId}` has been added to the whitelist list.");
        }
    }
}