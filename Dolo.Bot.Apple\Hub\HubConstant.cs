﻿namespace Dolo.Bot.Apple.Hub;

public static class HubConstant
{
    /// <summary>
    ///     cube dice numbers
    ///     used for apple dice command
    /// </summary>
    public static readonly List<DiscordEmoji> DiceNumbers = [DiscordEmoji.FromName(Hub.Discord!, ":one:"), DiscordEmoji.FromName(Hub.Discord!, ":two:"), DiscordEmoji.FromName(Hub.Discord!, ":three:"), DiscordEmoji.FromName(Hub.Discord!, ":four:"), DiscordEmoji.FromName(Hub.Discord!, ":five:"), DiscordEmoji.FromName(Hub.Discord!, ":six:")];

    /// <summary>
    ///     Random images for birthday
    /// </summary>
    public static readonly List<string> BirthdayImages = ["https://tenor.com/view/hbd-wishes-hb-hbd-happy-birthday-gif-25721730", "https://tenor.com/view/happy-birthday-the-office-dwight-dwight-schrute-birthday-gif-25725679", "https://tenor.com/view/happy-birthday-hbd-hbd-wishes-hbday-gif-26242421", "https://tenor.com/view/sweet-cute-happy-birthday-happy-birthday-gif-24108315"];

    public static TimeSpan TimeoutTime => TimeSpan.FromDays(7);

    /// <summary>
    ///     All channels for activity
    ///     this is used for /notify command
    /// </summary>
    public static IEnumerable<DiscordChannel?> ActivityChannels => new[]
    {
        HubChannel.Chat,
        HubChannel.French,
        HubChannel.Turkish,
        HubChannel.Polish,
        HubChannel.German,
        HubChannel.Art,
        HubChannel.Birthday,
        HubChannel.Food,
        HubChannel.Selfie,
        HubChannel.Outfit,
        HubChannel.Pets
    };
}