﻿using Dolo.Core.Windows;
using System.Diagnostics;
using System.Runtime.InteropServices;
namespace Dolo.Core;

public class Memory
{
    private readonly Process? _process;

    public Memory(int processId) => _process = Process.GetProcessById(processId);
    public Memory(Process? process) => _process = process;

    public Memory(string processName) => _process = Process.GetProcessesByName(processName)
                                             .FirstOrDefault();
    public bool IsOpen => Process.GetProcessesByName(_process?.ProcessName).FirstOrDefault() != null;

    /// <summary>
    ///     Get a string using the content to find the address
    /// </summary>
    public string? GetString(string? content)
    {
        if (_process is null || string.IsNullOrEmpty(content))
            return default;

        var address = FindAddress(content);
        return ReadString(address, content.Length);
    }

    /// <summary>
    ///     Method to read a value from the process memory
    ///     using the address and the length of the value.
    /// </summary>
    public string? ReadString(nint address, int length)
    {
        if (_process is null)
            return default;

        var buffer = new byte[length];
        return !Windows.Windows.ReadProcessMemory(_process.Handle, address, buffer, length, out _)
                   ? null
                   : Encoding.UTF8.GetString(buffer);
    }

    /// <summary>
    ///     Read a value from the process memory using the address
    /// </summary>
    public T Read<T>(nint address) where T : unmanaged
    {
        if (_process is null)
            return default;

        var buffer = new byte[Unsafe.SizeOf<T>()];
        return !Windows.Windows.ReadProcessMemory(_process.Handle, address, buffer, buffer.Length, out _)
                   ? default
                   : MemoryMarshal.Read<T>(buffer);
    }


    /// <summary>
    ///     Write into the memory of the process using the address and the value
    /// </summary>
    public bool Write(nint address, string? value)
    {
        if (_process is null || value is null)
            return false;

        var buffer = Encoding.UTF8.GetBytes(value);
        return Windows.Windows.WriteProcessMemory(_process.Handle, address, buffer, (uint)buffer.Length, out _);
    }

    /// <summary>
    ///     Method to find a address using the string
    /// </summary>
    public nint FindAddress(string value, int maxSearch = 3500)
    {
        if (_process is null)
            return default;

        var buffer = Encoding.UTF8.GetBytes(value);
        var memoryInfo = new MEMORY_BASIC_INFORMATION();
        var address = 0;
        var count = 0;

        while (Windows.Windows.VirtualQueryEx(_process.Handle, address, out memoryInfo, Marshal.SizeOf(memoryInfo)) != 0)
        {
            if (memoryInfo.State == MEMORY_STATE.MEM_COMMIT.GetValue() && memoryInfo.Protect == MEMORY_PROTECTION.PAGE_READWRITE.GetValue())
            {
                var buffer2 = new byte[memoryInfo.RegionSize];
                if (Windows.Windows.ReadProcessMemory(_process.Handle, memoryInfo.BaseAddress, buffer2, memoryInfo.RegionSize.ToInt32(), out _))
                {
                    var index = buffer2.AsSpan().IndexOf(buffer);
                    if (index != -1)
                        return memoryInfo.BaseAddress + index;

                    if (count++ >= maxSearch)
                        break;
                }
            }
            address += (int)memoryInfo.RegionSize;
        }
        return default;
    }
}