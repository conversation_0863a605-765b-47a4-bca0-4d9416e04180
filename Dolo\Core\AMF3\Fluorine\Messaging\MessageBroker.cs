﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Endpoints;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
using AMFContext=Dolo.Core.AMF3.Fluorine.Context.AMFContext;
using IService=Dolo.Core.AMF3.Fluorine.Messaging.Services.IService;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     <para>
///         All communication with the various services provided is mediated by the message broker.
///     </para>
///     <para>
///         It has a number of endpoints which send and receive messages over the network, and it has
///         a number of services that are message destinations. The broker routes messages to
///         endpoints based on the content type of those messages, and routes decoded messages
///         to services based on message type.
///     </para>
///     <para>
///         The broker also has a means of calling back into the endpoints in order to push messages
///         back through them.
///     </para>
/// </summary>
/// <example>
///     <para>Pushing a message to connected clients (Flex Messaging)</para>
///     <code lang="CS">
/// MessageBroker msgBroker = MessageBroker.GetMessageBroker(null);
/// AsyncMessage msg = new AsyncMessage();
/// msg.destination = "chat";
/// msg.headers.Add(AsyncMessage.SubtopicHeader, "status." + userId);
/// msg.clientId = Guid.NewGuid().ToString("D");
/// msg.messageId = Guid.NewGuid().ToString("D");
/// msg.timestamp = Environment.TickCount;
/// Hashtable body = new Hashtable();
/// body.Add("userId", userId);
/// body.Add("status", status);
/// msg.body = body;
/// msgBroker.RouteMessage(msg);
/// </code>
/// </example>
internal class MessageBroker
{
    /// <summary>
    ///     Default MessageBroker identity.
    /// </summary>
    public static string DefaultMessageBrokerId = "default";

    private static readonly Hashtable _messageBrokers = new(1);
    private static readonly object _syncLock = new();
    private readonly ClientManager _clientManager;
    private readonly Hashtable _destinations;
    private readonly Hashtable _destinationServiceMap;
    private readonly Hashtable _endpoints;
    private readonly Hashtable _factories;

    private readonly Hashtable _services;
    private GlobalScope _globalScope;

    /// <summary>
    ///     Initializes a new instance of the MessageBroker class.
    /// </summary>
    public MessageBroker(MessageServer messageServer)
    {
        MessageServer = messageServer;
        _services = new();
        _endpoints = new();
        _factories = new();
        _destinationServiceMap = new();
        _destinations = new();
        _clientManager = new(this);
    }

    /// <summary>
    ///     Gets the Id for the MessageBroker.
    /// </summary>
    public string Id { get; private set; }

    /// <summary>
    ///     Gets an object that can be used to synchronize access.
    /// </summary>
    public object SyncRoot => _syncLock;

    /// <summary>
    ///     Gets the Global scope.
    /// </summary>
    /// <remarks>
    ///     The global scope is the parent of all Web scopes. For Flex Messaging applications the Global scope is accessible
    ///     through the message broker.
    /// </remarks>
    public IGlobalScope GlobalScope => _globalScope;

    internal IClientRegistry ClientRegistry => _clientManager;

    internal FlexClientSettings FlexClientSettings => MessageServer.ServiceConfigSettings.FlexClientSettings;

    internal MessageServer MessageServer { get; }

    /// <summary>
    ///     Registers the message broker.
    /// </summary>
    protected void RegisterMessageBroker()
    {
        if (Id == null)
            Id = DefaultMessageBrokerId;
        lock (_syncLock)
        {
            if (_messageBrokers.ContainsKey(Id))
                throw new AMFException(__Res.GetString(__Res.MessageBroker_RegisterError, Id));
            _messageBrokers[Id] = this;
        }
    }

    /// <summary>
    ///     Unregisters the message broker.
    /// </summary>
    protected void UnregisterMessageBroker()
    {
        if (Id == null)
            Id = DefaultMessageBrokerId;
        lock (_syncLock) _messageBrokers.Remove(Id);
    }

    internal void RegisterDestination(Destination destination, IService service)
    {
        _destinationServiceMap[destination.Id] = service.id;
        _destinations[destination.Id] = destination;
    }

    /// <summary>
    ///     Gets the MessageBroker object for the current request.
    /// </summary>
    /// <param name="messageBrokerId">Ignored.</param>
    /// <returns>The MessageBroker instance if it is found; otherwise, null.</returns>
    public static MessageBroker GetMessageBroker(string messageBrokerId)
    {
        lock (_syncLock)
        {
            if (messageBrokerId == null)
                messageBrokerId = DefaultMessageBrokerId;
            return _messageBrokers[messageBrokerId] as MessageBroker;
        }
    }

    internal void AddService(IService service)
    {
        _services[service.id] = service;
    }

    internal IService GetService(string id) => _services[id] as IService;

    internal void AddEndpoint(IEndpoint endpoint)
    {
        _endpoints[endpoint.Id] = endpoint;
    }

    internal void AddFactory(string id, IFlexFactory factory)
    {
        _factories.Add(id, factory);
    }

    /// <summary>
    ///     Returns the IFlexFactory with the specified Id.
    /// </summary>
    /// <param name="id">FlexFactory identity.</param>
    /// <returns>The FlexFactory instance if it is found; otherwise, null.</returns>
    public IFlexFactory GetFactory(string id) => _factories[id] as IFlexFactory;

    /// <summary>
    ///     Start all of the broker's services.
    /// </summary>
    internal void StartServices()
    {
        foreach (DictionaryEntry entry in _services)
        {
            var service = entry.Value as IService;
            service.Start();
        }
    }

    /// <summary>
    ///     Stop all of the broker's services.
    /// </summary>
    internal void StopServices()
    {
        foreach (DictionaryEntry entry in _services)
        {
            var service = entry.Value as IService;
            service.Stop();
        }
    }

    /// <summary>
    ///     Start all of the broker's endpoints.
    /// </summary>
    internal void StartEndpoints()
    {
        foreach (DictionaryEntry entry in _endpoints)
        {
            var endpoint = entry.Value as IEndpoint;
            endpoint.Start();
        }
    }

    /// <summary>
    ///     Stop all of the broker's endpoints.
    /// </summary>
    internal void StopEndpoints()
    {
        foreach (DictionaryEntry entry in _endpoints)
        {
            var endpoint = entry.Value as IEndpoint;
            endpoint.Stop();
        }
    }

    /// <summary>
    ///     Start the message broker.
    /// </summary>
    public void Start()
    {
        RegisterMessageBroker();

        //Each Application has its own Scope hierarchy and the root scope is WebScope. 
        //There's a global scope that aims to provide common resource sharing across Applications namely GlobalScope.
        //The GlobalScope is the parent of all WebScopes. 
        //Other scopes in between are all instances of Scope. Each scope takes a name. 
        //The GlobalScope is named "default".
        //The WebScope is named per Application context name.
        //The Scope is named per path name.
        _globalScope = new();
        _globalScope.Name = "default";
        _globalScope.Register();

        StartServices();
        StartEndpoints();
    }

    /// <summary>
    ///     Stop the message broker.
    /// </summary>
    public void Stop()
    {
        StopServices();
        StopEndpoints();

        if (_globalScope != null)
        {
            _globalScope.Stop();
            _globalScope.Dispose();
            _globalScope = null;
        }

        UnregisterMessageBroker();
    }

    internal IEndpoint GetEndpoint(string endpointId)
    {
        foreach (DictionaryEntry entry in _endpoints)
        {
            var endpoint = entry.Value as IEndpoint;
            if (endpoint.Id == endpointId)
                return endpoint;
        }

        return null;
    }

    internal IEndpoint GetEndpoint(string path, string contextPath, bool secure)
    {
        foreach (DictionaryEntry entry in _endpoints)
        {
            var endpoint = entry.Value as IEndpoint;
            var channelSettings = endpoint.GetSettings();
            if (channelSettings != null && channelSettings.Bind(path, contextPath))
                return endpoint;
        }

        return null;
    }

    internal void TraceChannelSettings()
    {
        foreach (DictionaryEntry entry in _endpoints)
        {
            var endpoint = entry.Value as IEndpoint;
            var channelSettings = endpoint.GetSettings();
        }
    }

    /// <summary>
    ///     Call this method in order to send a message from your code into the message routing system.
    ///     The message is routed to a service that is defined to handle messages of this type.
    ///     Once the service is identified, the destination property of the message is used to find a destination configured
    ///     for that service.
    ///     The adapter defined for that destination is used to handle the message.
    /// </summary>
    /// <param name="message">The message to be routed to a service.</param>
    /// <returns>The result of the message routing.</returns>
    public Task<IMessage> RouteMessage(IMessage message) => RouteMessage(message, null);

    /// <summary>
    ///     Call this method in order to send a message from your code into the message routing system.
    ///     The message is routed to a service that is defined to handle messages of this type.
    ///     Once the service is identified, the destination property of the message is used to find a destination configured
    ///     for that service.
    ///     The adapter defined for that destination is used to handle the message.
    /// </summary>
    /// <param name="message">The message to be routed to a service.</param>
    /// <param name="endpoint">
    ///     This can identify the endpoint that is sending the message but it is currently not used so you
    ///     may pass in null.
    /// </param>
    /// <returns>The result of the message routing.</returns>
    internal async Task<IMessage> RouteMessage(IMessage message, IEndpoint endpoint)
    {
        IService service = null;
        object result = null;
        Task<IMessage> responseMessage = null;

        var commandMessage = message as CommandMessage;
        if (commandMessage != null && (commandMessage.operation == CommandMessage.RoyalePingOperation ||
                                       commandMessage.operation == CommandMessage.FlexPingOperation))
        {
            responseMessage = Task.FromResult<IMessage>(new AcknowledgeMessage());
            responseMessage.Result.body = true;
        }
        else
        {
            //log.Debug(string.Format("Locate service for message {0}", message.GetType().Name));
            service = GetService(message);
            if (service != null)
                try
                {
                    var task = service.ServiceMessage(message);
                    await task;
                    result = task.Result;
                }
                catch (Exception exception)
                {
                    result = ErrorMessage.GetErrorMessage(message, exception);
                }
            else
            {
                var msg = __Res.GetString(__Res.Destination_NotFound, message.destination);
                result = ErrorMessage.GetErrorMessage(message, new AMFException(msg));
            }

            if (!(result is IMessage))
            {
                responseMessage = Task.FromResult<IMessage>(new AcknowledgeMessage());
                responseMessage.Result.body = result;
            }
            else
                responseMessage = result as Task<IMessage>;
        }

        if (responseMessage.Result is AsyncMessage)
            ((AsyncMessage)responseMessage.Result).correlationId = message.messageId;
        responseMessage.Result.destination = message.destination;
        responseMessage.Result.clientId = message.clientId;
        //The only case when we do not have context should be when the server side initiates a push
        if (AMFContext.Current is { Client: {} })
            responseMessage.Result.SetHeader(MessageBase.FlexClientIdHeader, AMFContext.Current.Client.Id);
        return responseMessage.Result;
    }

    internal IService GetService(IMessage message)
    {
        var service = GetServiceByDestinationId(message.destination);
        if (service == null)
        {
            var commandMessage = message as CommandMessage;
            if (commandMessage is { messageRefType: {} })
                foreach (DictionaryEntry entry in _services)
                {
                    var serviceTmp = entry.Value as IService;
                    if (serviceTmp.IsSupportedMessageType(commandMessage.messageRefType))
                    {
                        service = serviceTmp;
                        break;
                    }
                }
        }

        return service;
    }

    internal IService GetServiceByDestinationId(string destinationId)
    {
        if (destinationId == null)
            return null;
        var serviceId = _destinationServiceMap[destinationId] as string;
        if (serviceId != null)
            return _services[serviceId] as IService;
        return null;
    }

    internal IService GetServiceByMessageType(string messageRef)
    {
        if (messageRef == null)
            return null;
        foreach (DictionaryEntry entry in _services)
        {
            var serviceTmp = entry.Value as IService;
            if (serviceTmp.IsSupportedMessageType(messageRef)) return serviceTmp;
        }

        return null;
    }

    /// <summary>
    ///     Gets the destination Id for the specified source.
    /// </summary>
    /// <param name="source">The destination's source property.</param>
    /// <returns>The Id if the destination is found; otherwise, null.</returns>
    public string GetDestinationBySource(string source)
    {
        foreach (DictionaryEntry entry in _destinations)
        {
            var destination = entry.Value as Destination;
            if (destination.Source == source)
                return destination.Id;
        }

        return null;
    }

    /// <summary>
    ///     Gets the specified destination.
    /// </summary>
    /// <param name="destinationId">The Id if the destination.</param>
    /// <returns>The destination if found; otherwise, null.</returns>
    public Destination GetDestination(string destinationId)
    {
        foreach (DictionaryEntry entry in _destinations)
        {
            var destination = entry.Value as Destination;
            if (destination.Id == destinationId)
                return destination;
        }

        return null;
    }

    /// <summary>
    ///     Gets the destination Id from the specified IMessage instance.
    /// </summary>
    /// <param name="message">The message that should be handled by the destination.</param>
    /// <returns>The Id if the destination is found; otherwise, null.</returns>
    public string GetDestinationId(IMessage message)
    {
        //If destination is specified then return
        if (message.destination != null)
            return message.destination;
        if (message is RemotingMessage)
        {
            //Search for a destination with the same source
            var remotingMessage = message as RemotingMessage;
            var destinationId = GetDestinationBySource(remotingMessage.source);
            if (destinationId != null)
                return destinationId;
            //Search for a RemotingService
            Destination defaultDestination = null;
            foreach (DictionaryEntry entry in _services)
            {
                var serviceTmp = entry.Value as IService;
                if (serviceTmp.IsSupportedMessage(message))
                {
                    var destinations = serviceTmp.GetDestinations();
                    foreach (var destination in destinations)
                    {
                        if (destination.Source == remotingMessage.source)
                            return destination.Id;
                        if (destination.Source == "*")
                            defaultDestination = destination;
                    }
                }
            }

            if (defaultDestination != null)
                return defaultDestination.Id;
        }

        return null;
    }

    /// <summary>
    ///     Gets the destination Id for the specified source.
    /// </summary>
    /// <param name="source">The source should be handled by the destination.</param>
    /// <returns>The Id if the destination is found; otherwise, null.</returns>
    public string GetDestinationId(string source)
    {
        ValidationUtils.ArgumentNotNullOrEmpty(source, "source");
        var destinationId = GetDestinationBySource(source);
        if (destinationId != null)
            return destinationId;
        //Search for a RemotingService
        Destination defaultDestination = null;
        foreach (DictionaryEntry entry in _services)
        {
            var serviceTmp = entry.Value as IService;
            if (serviceTmp.IsSupportedMessageType("flex.messaging.messages.RemotingMessage"))
            {
                var destinations = serviceTmp.GetDestinations();
                foreach (var destination in destinations)
                {
                    if (destination.Source == source)
                        return destination.Id;
                    if (destination.Source == "*")
                        defaultDestination = destination;
                }
            }
        }

        return defaultDestination?.Id;
    }

    internal IClient GetClient(string id) => _clientManager.LookupClient(id);
}