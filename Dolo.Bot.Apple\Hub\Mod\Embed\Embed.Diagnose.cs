using System.Text;

namespace Dolo.Bot.Apple.Hub.Mod.Embed;

public partial class Embed
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("diagnose")]
    [Description("diagnose embed update issues")]
    public async Task DiagnoseAsync(SlashCommandContext ctx, 
        [Description("the type of the embed")] EmbedType embedType)
    {
        await ctx.Interaction.DeferAsync(true);

        var diagnostics = new StringBuilder();
        diagnostics.AppendLine($"🔍 **Diagnosing {embedType} Embed**\n");

        try
        {
            // Check channel access
            var channelInfo = embedType switch
            {
                EmbedType.Welcome => ("Welcome", HubChannel.Welcome, 772233477688655912UL),
                EmbedType.Birthday => ("Birthday", HubChannel.Birthday, 813048025273466920UL),
                EmbedType.Verification => ("Activator", HubChannel.Activator, 961269735352332299UL),
                EmbedType.Reward => ("Reward", HubChannel.Reward, 1207108766118576200UL),
                _ => ("Unknown", null, 0UL)
            };

            var (channelName, channel, messageId) = channelInfo;

            // Check channel
            if (channel == null)
            {
                diagnostics.AppendLine($"❌ **Channel Issue**: {channelName} channel is null");
                diagnostics.AppendLine($"   - Check if channel exists in guild");
                diagnostics.AppendLine($"   - Verify channel ID in HubChannel.cs");
            }
            else
            {
                diagnostics.AppendLine($"✅ **Channel**: {channelName} channel found");
                diagnostics.AppendLine($"   - Name: {channel.Name}");
                diagnostics.AppendLine($"   - ID: {channel.Id}");
                
                // Check bot permissions
                var botMember = ctx.Guild.CurrentMember;
                var permissions = channel.PermissionsFor(botMember);
                
                if (permissions.HasPermission(DiscordPermission.ViewChannel))
                    diagnostics.AppendLine($"✅ **Permission**: Can view channel");
                else
                    diagnostics.AppendLine($"❌ **Permission**: Cannot view channel");
                
                if (permissions.HasPermission(DiscordPermission.SendMessages))
                    diagnostics.AppendLine($"✅ **Permission**: Can send messages");
                else
                    diagnostics.AppendLine($"❌ **Permission**: Cannot send messages");
                
                if (permissions.HasPermission(DiscordPermission.ManageMessages))
                    diagnostics.AppendLine($"✅ **Permission**: Can manage messages");
                else
                    diagnostics.AppendLine($"❌ **Permission**: Cannot manage messages");

                // Check message
                try
                {
                    var message = await channel.TryGetMessageAsync(messageId);
                    if (message == null)
                    {
                        diagnostics.AppendLine($"❌ **Message**: Message {messageId} not found");
                        diagnostics.AppendLine($"   - Message may have been deleted");
                        diagnostics.AppendLine($"   - Use `/embed add {embedType.ToString().ToLower()}` to create new message");
                    }
                    else
                    {
                        diagnostics.AppendLine($"✅ **Message**: Message {messageId} found");
                        diagnostics.AppendLine($"   - Author: {message.Author?.Username}");
                        diagnostics.AppendLine($"   - Created: <t:{message.CreationTimestamp.ToUnixTimeSeconds()}:R>");
                        
                        if (message.Author?.Id == ctx.Client.CurrentUser.Id)
                            diagnostics.AppendLine($"✅ **Ownership**: Bot owns this message");
                        else
                            diagnostics.AppendLine($"❌ **Ownership**: Bot doesn't own this message");
                    }
                }
                catch (Exception ex)
                {
                    diagnostics.AppendLine($"❌ **Message Error**: {ex.Message}");
                }
            }

            // Check embed generation
            try
            {
                var embedBuilder = GetEmbedFromType(embedType);
                diagnostics.AppendLine($"✅ **Embed Generation**: Embed created successfully");
                diagnostics.AppendLine($"   - Components: {embedBuilder.Components.Count}");
                diagnostics.AppendLine($"   - Embeds: {embedBuilder.Embeds.Count}");
            }
            catch (Exception ex)
            {
                diagnostics.AppendLine($"❌ **Embed Generation**: {ex.Message}");
            }

            // Check for null references in embed content
            if (embedType == EmbedType.Welcome)
            {
                diagnostics.AppendLine($"\n🔍 **Welcome Embed Channel References**:");
                diagnostics.AppendLine($"   - Changelog: {(HubChannel.Changelog != null ? $"✅ {HubChannel.Changelog.Id}" : "❌ null")}");
                diagnostics.AppendLine($"   - Rules: {(HubChannel.Rules != null ? $"✅ {HubChannel.Rules.Id}" : "❌ null")}");
                diagnostics.AppendLine($"   - News: {(HubChannel.News != null ? $"✅ {HubChannel.News.Id}" : "❌ null")}");
            }

        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"❌ **Critical Error**: {ex.Message}");
            diagnostics.AppendLine($"   - Stack: {ex.StackTrace?.Split('\n').FirstOrDefault()}");
        }

        var resultEmbed = new DiscordEmbedBuilder()
            .WithTitle($"🔧 Embed Diagnostics - {embedType}")
            .WithDescription(diagnostics.ToString())
            .WithColor(DiscordColor.Orange)
            .WithTimestamp(DateTimeOffset.UtcNow);

        await ctx.TryEditResponseAsync(resultEmbed.Build());
    }
}
