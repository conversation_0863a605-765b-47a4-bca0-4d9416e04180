﻿namespace Dolo.Bot.Apple.Hub.System;

public static class BadChannelFilter
{
    public static async Task<bool> IsOkAsync(MessageArgs e)
    {
        if (e.Channel.IsPrivate
            || e.Guild.Id  != Hub.Guild?.Id
            || e.Author.Id == 755020247756570705
            || e.Author.IsSystem.GetValueOrDefault()
            || e.Channel == HubChannel.PlutoChat)
            return true;

        // add reactions on the looks channel
        if (e.Message.Channel == HubChannel.Looks && e.Message.Attachments.Count > 0)
        {
            await e.Message.TryCreateReactionAsync(HubEmoji.CheckYes);
            await e.Message.TryCreateReactionAsync(HubEmoji.CheckNo);
        }

        // add reactions to art, outfit, selfie, food
        if (e.Message.Channel    == HubChannel.Art    && e.Message.Attachments.Count > 0
            || e.Message.Channel == HubChannel.Outfit && e.Message.Attachments.Count > 0
            || e.Message.Channel == HubChannel.Selfie && e.Message.Attachments.Count > 0
            || e.Message.Channel == HubChannel.Food   && e.Message.Attachments.Count > 0
            || e.Message.Channel == HubChannel.Pets   && e.Message.Attachments.Count > 0
            || e.Message.Channel == HubChannel.Travel && e.Message.Attachments.Count > 0)
        {
            await e.Message.TryCreateReactionAsync(HubEmoji.WhiteFlyingHeart);
            await e.Message.TryCreateReactionAsync(HubEmoji.GhostLove);
        }

        // return deletion check if the user is admin or friend
        if (e.Guild.Members.ContainsKey(e.Message.Author.Id) && e.Guild.Members[e.Message.Author.Id].Roles.ContainsMany(HubRoles.Friend, HubRoles.Admin))
            return true;

        // check not accepted content
        if (e.Message.Channel              == HubChannel.Art
            && e.Message.Attachments.Count < 1
            || e.Message.Channel           == HubChannel.Travel
            && e.Message.Attachments.Count < 1
            || e.Message.Channel           == HubChannel.Looks
            && e.Message.Attachments.Count < 1
            || e.Message.Channel           == HubChannel.Selfie
            && e.Message.Attachments.Count < 1
            || e.Message.Channel           == HubChannel.Outfit
            && e.Message.Attachments.Count < 1
            || e.Message.Channel           == HubChannel.Food
            && e.Message.Attachments.Count < 1
            || e.Message.Channel           == HubChannel.Pets
            && e.Message.Attachments.Count < 1
            || e.Message.Channel    == HubChannel.Birthday
            || e.Message.Channel.Id == 972191400252801054)
        {
            await e.Message.TryDeleteMessageAsync();
            return false;
        }

        return true;
    }
}