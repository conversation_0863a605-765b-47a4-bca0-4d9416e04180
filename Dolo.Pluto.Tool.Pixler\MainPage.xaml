﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:Dolo.Pluto.Tool.Pixler"
             xmlns:components="clr-namespace:Dolo.Pluto.Tool.Pixler.Components"
             x:Class="Dolo.Pluto.Tool.Pixler.MainPage">

    <BlazorWebView x:Name="blazorWebView" HostPage="wwwroot/index.html">
        <BlazorWebView.RootComponents>
            <RootComponent Selector="#app" ComponentType="{x:Type components:Routes}" />
        </BlazorWebView.RootComponents>
    </BlazorWebView>

</ContentPage>
