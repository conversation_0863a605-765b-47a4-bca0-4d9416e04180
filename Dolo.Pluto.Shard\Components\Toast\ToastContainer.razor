@using Dolo.Pluto.Shard.Components.Toast

<!-- Toast Container -->
<div class="fixed top-4 right-4 z-[60] space-y-2 pointer-events-none">
    @foreach (var toast in ToastService.Toasts)
    {
        <div class="pointer-events-auto">
            <Toast Message="@toast.Message"
                   Title="@toast.Title"
                   Type="@toast.Type"
                   IsVisible="@toast.IsVisible"
                   AutoCloseDelay="@toast.AutoCloseDelay"
                   OnClose="@(() => HandleToastClose(toast.Id))" />
        </div>
    }
</div>
