﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal abstract class AbstractFilter : IFilter
{

    /// <summary>
    ///     Initializes a new instance of the AbstractFilter class.
    /// </summary>
    public AbstractFilter()
    {}

    #region IFilter Members

    public virtual IFilter Next
    {
        get;
        set;
    }

    public virtual Task Invoke(AMFContext context)
        => Task.FromResult<object>(null);

    #endregion
}