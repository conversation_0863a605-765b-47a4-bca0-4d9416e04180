﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
using Dolo.Core.AMF3.Fluorine.Messaging.Services.Messaging;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     Represents a client-side MessageAgent instance.
///     A server-side MessageClient is only created if its client-side counterpart has subscribed to a destination for
///     pushed data (e.g. Consumer).
/// </summary>
/// <remarks>
///     <para>
///         Client-side Producers do not result in the creation of corresponding
///         server-side MessageClient instances.
///     </para>
///     <para></para>
///     <para>
///         Each MessageClient instance is bound to a client class (session) and when the
///         client is invalidated any associated MessageClient instances are invalidated as
///         well.
///     </para>
///     <para>
///         MessageClient instances may also be timed out on a per-destination basis and
///         based on subscription inactivity. If no messages are pushed to the MessageClient
///         within the destination's subscription timeout period the MessageClient will be
///         shutdown.
///     </para>
///     <para>
///         Per-destination subscription timeout should be used when inactive
///         subscriptions should be shut down opportunistically to preserve server
///         resources.
///     </para>
/// </remarks>
internal sealed class MessageClient : IMessageClient
{
    private static readonly object _syncLock = new();

    private static Hashtable _messageClientCreatedListeners;
    private readonly IClient _client;
    private byte[] _binaryId;

    private Hashtable _messageClientDestroyedListeners;

    private MessageClient()
    {}

    internal MessageClient(IClient client, string messageClientId, string endpoint)
    {
        _client = client;
        ClientId = messageClientId;
        Endpoint = endpoint;
        MessageConnection = AMFContext.Current.Connection as IMessageConnection;
        MessageConnection?.RegisterMessageClient(this);

        if (_messageClientCreatedListeners != null)
            foreach (IMessageClientListener listener in _messageClientCreatedListeners.Keys)
                listener.MessageClientCreated(this);
    }

    internal IMessageConnection MessageConnection { get; }

    /// <summary>
    ///     Gets the endpoint identity the MessageClient is subscribed to.
    /// </summary>
    public string Endpoint { get; }

    /// <summary>
    ///     Gets the MessageClient subtopic.
    /// </summary>
    /// <value>The MessageClient subtopic.</value>
    public Subtopic Subtopic { get; set; }

    /// <summary>
    ///     Gets an object that can be used to synchronize access.
    /// </summary>
    public object SyncRoot => _syncLock;

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <returns></returns>
    public byte[] GetBinaryId()
    {
        if (_binaryId == null)
        {
            var utf8Encoding = new UTF8Encoding();
            _binaryId = utf8Encoding.GetBytes(ClientId);
        }

        return _binaryId;
    }

    /// <summary>
    ///     Gets the client identity.
    /// </summary>
    /// <value>The client identity.</value>
    public string ClientId { get; }

    /// <summary>
    ///     Gets whether the connection is being disconnected.
    /// </summary>
    public bool IsDisconnecting { get; private set; }

    internal void SetIsDisconnecting(bool value)
    {
        IsDisconnecting = value;
    }

    /// <summary>
    ///     Adds a MessageClient created listener.
    /// </summary>
    /// <param name="listener">The listener to add.</param>
    public static void AddMessageClientCreatedListener(IMessageClientListener listener)
    {
        lock (typeof(MessageClient))
        {
            if (_messageClientCreatedListeners == null)
                _messageClientCreatedListeners = new(1);
            _messageClientCreatedListeners[listener] = null;
        }
    }

    /// <summary>
    ///     Removes a MessageClient created listener.
    /// </summary>
    /// <param name="listener">The listener to remove.</param>
    public static void RemoveMessageClientCreatedListener(IMessageClientListener listener)
    {
        lock (typeof(MessageClient))
            if (_messageClientCreatedListeners != null)
                if (_messageClientCreatedListeners.Contains(listener))
                    _messageClientCreatedListeners.Remove(listener);
    }

    /// <summary>
    ///     Adds a MessageClient destroy listener.
    /// </summary>
    /// <param name="listener">The listener to add.</param>
    public void AddMessageClientDestroyedListener(IMessageClientListener listener)
    {
        if (_messageClientDestroyedListeners == null)
            _messageClientDestroyedListeners = new(1);
        _messageClientDestroyedListeners[listener] = null;
    }

    /// <summary>
    ///     Removes a MessageClient destroyed listener.
    /// </summary>
    /// <param name="listener">The listener to remove.</param>
    public void RemoveMessageClientDestroyedListener(IMessageClientListener listener)
    {
        if (_messageClientDestroyedListeners != null)
            if (_messageClientDestroyedListeners.Contains(listener))
                _messageClientDestroyedListeners.Remove(listener);
    }

    //Rtmpconnection.Close -> Disconnect -> Unsubscribe
    internal void Disconnect()
    {
        SetIsDisconnecting(true);
        Unsubscribe(false);
    }

    /// <summary>
    ///     Timeout -> Unsubscribe
    ///     Client -> Unsubscribe
    /// </summary>
    internal void Unsubscribe()
    {
        if (_messageClientDestroyedListeners != null)
            foreach (IMessageClientListener listener in _messageClientDestroyedListeners.Keys)
                listener.MessageClientDestroyed(this);

        _client.UnregisterMessageClient(this);
    }

    internal void Timeout()
    {
        try
        {
            if (IsDisconnecting)
                return;

            //Timeout
            var commandMessage = new CommandMessage();
            commandMessage.destination = DestinationSettings.AMFDestination;
            commandMessage.clientId = ClientId;
            //Indicate that the client's session with a remote destination has timed out
            commandMessage.operation = CommandMessage.SessionInvalidateOperation;
            commandMessage.headers[MessageBase.FlexClientIdHeader] = _client.Id;

            object[] subscribers = [commandMessage.clientId];
            Unsubscribe(true);
        }
        catch (Exception)
        {}
    }

    private void Unsubscribe(bool timeout)
    {
        var commandMessageUnsubscribe = new CommandMessage();
        commandMessageUnsubscribe.destination = DestinationSettings.AMFDestination;
        commandMessageUnsubscribe.operation = CommandMessage.UnsubscribeOperation;
        commandMessageUnsubscribe.clientId = ClientId;
        if (timeout)
        {
            commandMessageUnsubscribe.headers[CommandMessage.SessionInvalidatedHeader] = true;
            commandMessageUnsubscribe.headers[CommandMessage.AMFMessageClientTimeoutHeader] = true;
            commandMessageUnsubscribe.headers[MessageBase.FlexClientIdHeader] = _client.Id;
        }
    }
}