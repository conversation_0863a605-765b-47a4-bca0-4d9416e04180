using System.Net;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Models;

public class InterceptorConfig
{
    public int ProxyPort { get; set; } = 8888;
    public IPAddress BindAddress { get; set; } = IPAddress.Any;
    public List<string> TargetDomains { get; set; } = new()
    {
        "mspapis.com",
        "mspcdns.com"
    };
    public ILoggerFactory? LoggerFactory { get; set; }
    public bool EnableOcspValidation { get; set; } = true;
    public string RootCaSubject { get; set; } = "CN=HTTPS Interceptor Root CA, O=Security Research, C=US";
    public string RootCertificateName { get; set; } = "HTTPS Interceptor Root CA";
    public string CertificateOrganization { get; set; } = "Microsoft Corporation";
    public string CertificateCountry { get; set; } = "US";
    public BreakpointConfig Breakpoints { get; set; } = new();

    public InterceptorConfig UseLogger(Action<InterceptorLoggingBuilder> configure)
    {
        LoggerFactory = Microsoft.Extensions.Logging.LoggerFactory.Create(builder =>
        {
            var interceptorBuilder = new InterceptorLoggingBuilder(builder);
            configure(interceptorBuilder);
        });
        return this;
    }

    /// <summary>
    /// Configures breakpoints using a fluent API
    /// </summary>
    public InterceptorConfig UseBreakpoints(Action<BreakpointConfig> configure)
    {
        configure(Breakpoints);
        return this;
    }

    /// <summary>
    /// Enables request breakpoints
    /// </summary>
    public InterceptorConfig EnableRequestBreakpoints()
    {
        Breakpoints.EnableRequestBreakpoints = true;
        return this;
    }

    /// <summary>
    /// Enables response breakpoints
    /// </summary>
    public InterceptorConfig EnableResponseBreakpoints()
    {
        Breakpoints.EnableResponseBreakpoints = true;
        return this;
    }

    /// <summary>
    /// Enables both request and response breakpoints
    /// </summary>
    public InterceptorConfig EnableAllBreakpoints()
    {
        Breakpoints.EnableRequestBreakpoints = true;
        Breakpoints.EnableResponseBreakpoints = true;
        return this;
    }

    /// <summary>
    /// Sets breakpoint processing to sequential mode (default) - reliable input handling
    /// </summary>
    public InterceptorConfig UseSequentialBreakpoints()
    {
        Breakpoints.ProcessingMode = BreakpointProcessingMode.Sequential;
        return this;
    }

    /// <summary>
    /// Sets breakpoint processing to concurrent mode - faster but potential input conflicts
    /// </summary>
    public InterceptorConfig UseConcurrentBreakpoints()
    {
        Breakpoints.ProcessingMode = BreakpointProcessingMode.Concurrent;
        return this;
    }

    /// <summary>
    /// Adds a breakpoint rule for URL patterns
    /// </summary>
    public InterceptorConfig AddBreakpointRule(string urlPattern, BreakpointType type = BreakpointType.Both)
    {
        Breakpoints.AddRule(urlPattern, type);
        return this;
    }

    public bool IsTargetDomain(string hostname)
    {
        if (string.IsNullOrEmpty(hostname))
            return false;

        return TargetDomains.Any(domain =>
            hostname.Equals(domain, StringComparison.OrdinalIgnoreCase) ||
            hostname.EndsWith($".{domain}", StringComparison.OrdinalIgnoreCase) ||
            domain.StartsWith("*.") && hostname.EndsWith(domain[2..], StringComparison.OrdinalIgnoreCase));
    }
}
