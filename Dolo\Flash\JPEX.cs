﻿using System.Diagnostics;
namespace Dolo.Flash;

public static class JPEX
{
    /// <summary>
    ///     Checks for jPEX installation
    /// </summary>
    /// <returns></returns>
    public static bool IsInstalled()
    {
        var directory = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86);
        return Directory.Exists(Path.Combine(directory, "FFDec"));
    }

    /// <summary>
    ///     Gets the path to jPEX
    /// </summary>
    /// <returns></returns>
    private static string GetJpexPath()
    {
        var directory = Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86);
        return Path.Combine(directory, "FFDec", "ffdec.bat");
    }

    /// <summary>
    ///     Exports SWF images to a folder
    /// </summary>
    /// <param name="path"></param>
    /// <param name="swf"></param>
    public static async Task ExportAsync(string path, string swf)
    {
        if (!IsInstalled()) return;

        var process = new Process
        {
            StartInfo = new()
            {
                FileName = GetJpexPath(),
                Arguments = $"-export frame \"{path}\" {swf}",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                CreateNoWindow = true
            }
        };
        process.Start();
        await process.WaitForExitAsync();
    }
}