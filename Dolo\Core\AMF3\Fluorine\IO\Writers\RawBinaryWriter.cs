﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class RawBinaryWriter : IAMFWriter
{
    #region IAMFWriter Members

    public bool IsPrimitive
        => true;

    public void WriteData(AMFWriter writer, object data)
    {
        writer.WriteBytes((data as RawBinary).Buffer);
    }

    #endregion
}