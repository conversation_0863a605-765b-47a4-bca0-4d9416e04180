﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Pluto.Histogram;

public partial class Histogram
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("clear")]
    [Description("clear the histogram entries")]
    public async Task ClearHistogramAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(true);
        await Mongo.Histogram.ClearAsync();

        await ctx.TryEditResponseAsync("MspHistogram database cleared");
    }
}