﻿namespace Dolo.Core.Flash;

public abstract class SharedObjectParser
{
    public static SharedObject Parse(string filename)
    {
        var so = new SharedObject();
        var file = new SoReader(filename);
        var stringTable = new List<string>();

        if (!File.Exists(filename))
            return so;

        var header = new SoHeader
        {
            Padding1 = file.Read16(),
            FileSize = file.Read32()
        };

        file.FileSize = (int)header.FileSize + 6;
        header.Padding2 = file.Read32();
        header.Padding3 = file.Read16();
        header.Padding4 = file.Read32();

        var soNameLength = file.Read16();
        file.ReadString(soNameLength);
        file.Read32();

        while (file.Pos < file.FileSize)
        {
            var soValue = new SoValue();

            var lengthInt = (uint)file.ReadCompressedInt();
            var nameInline = (lengthInt & 0x01) > 0;

            lengthInt >>= 1;

            soValue.Key = nameInline ? file.ReadString((int)lengthInt) : stringTable[(int)lengthInt];
            if (nameInline)
                stringTable.Add(soValue.Key);

            soValue.Type = file.Read8();
            switch (soValue.Type)
            {
                case SoTypes.TypeNull:
                    break;
                case SoTypes.TypeBoolFalse:
                    soValue.BoolVal = false;
                    break;
                case SoTypes.TypeBoolTrue:
                    soValue.BoolVal = true;
                    break;
                case SoTypes.TypeInt:
                    soValue.IntVal = file.ReadCompressedInt();
                    break;
                case SoTypes.TypeDouble:
                    soValue.DoubleVal = file.ReadDouble();
                    break;
                case SoTypes.TypeString:
                {
                    var valLength = file.ReadCompressedInt();
                    var valInline = (valLength & 0x00000001) > 0;
                    valLength >>= 1;
                    switch (valInline)
                    {
                        case false:
                        {
                            if (valLength < stringTable.Count)
                                soValue.StringVal = stringTable[valLength];
                            break;
                        }
                        default:
                            soValue.StringVal = file.ReadString(valLength);
                            stringTable.Add(soValue.StringVal);
                            break;
                    }
                    break;
                }
                default:
                {
                    while (file.Pos < file.FileSize)
                    {
                        var nextByte = file.Read8();
                        if (nextByte != 0)
                            continue;

                        --file.Pos;
                        break;
                    }
                    break;
                }
            }

            so.Values.Add(soValue);
            if (file.Pos < file.FileSize)
                file.Read8();//Padding
        }
        return so;
    }
}