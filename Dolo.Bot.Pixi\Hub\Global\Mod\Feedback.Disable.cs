﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Feedback
    {
        [Command("disable")]

[Description("disable the feedback")]
        public async Task DisableAsync(SlashCommandContext ctx)
        {
            if (ctx.User.Id != 440584675740876810) return;

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(a => a.IsFeedback, false));

            await ctx.TryCreateResponseAsync("Feedback disabled");
        }
    }
}