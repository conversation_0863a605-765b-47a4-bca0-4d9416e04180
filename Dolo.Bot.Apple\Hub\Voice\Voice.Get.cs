﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("get")]
    [Description("get the collection of banned, muted or moderator")]
    public async Task VoiceGetAsync(SlashCommandContext ctx, [Description("choose one of the types to view the banned, muted or moderators")] VoiceModType type)
    {
        if (Hub.Guild is null)
            return;

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        // get the channel database entry
        var user = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (user is null)
            return;

        // get the list of banned, muted or moderators
        List<DiscordMember> list
            =
            [];

        // get the list of banned users
        if (type == VoiceModType.Banned)
            foreach (var usr in user.Banned)
                if (Hub.Guild.TryGetMember(usr, out var member))
                    list.Add(member);

        // get the list of muted users
        if (type == VoiceModType.Muted)
            foreach (var usr in user.Muted)
                if (Hub.Guild.TryGetMember(usr, out var member))
                    list.Add(member);

        // get the list of moderators
        if (type == VoiceModType.Moderator)
            foreach (var usr in user.Moderator)
                if (Hub.Guild.TryGetMember(usr, out var member))
                    list.Add(member);

        // print the list of banned, muted or moderators if any
        await ctx.TryCreateResponseAsync($"{type} {(!list.Any() ? "has no users in, it's empty.." : "")}\n {(list.Any() ? $"```{string.Join(", ", list.Select(a => a.Username).ToArray())}```" : "")}");
    }
}