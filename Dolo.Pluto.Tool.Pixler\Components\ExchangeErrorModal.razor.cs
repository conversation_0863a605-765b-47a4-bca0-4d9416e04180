using Microsoft.AspNetCore.Components;
using Dolo.Pluto.Tool.Pixler.Services;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class ExchangeErrorModal : ComponentBase
{
    public void Show(ExchangeError error)
    {
        ErrorType = error.Type;
        ErrorMessage = error.Message;
        AffectedAccount = error.AffectedAccount;
        IsVisible = true;
        StateHasChanged();
    }

    public void Hide()
    {
        IsVisible = false;
        StateHasChanged();
    }


}
