﻿global using Microsoft.Extensions.Logging;
global using System.Reflection;
global using System.ComponentModel;
global using System.Text.RegularExpressions;
global using System.Security.Cryptography;
global using MongoDB.Driver;
global using MongoDB.Bson.Serialization.Attributes;
global using DSharpPlus;
global using DSharpPlus.Entities;
global using DSharpPlus.EventArgs;
global using DSharpPlus.VoiceNext;
global using DSharpPlus.Commands;
global using DSharpPlus.Commands.Processors.SlashCommands;
global using DSharpPlus.Commands.Processors.TextCommands;
global using DSharpPlus.Commands.ContextChecks;
global using Dolo.Core.Http;
global using Dolo.Core.Mongo;
global using Dolo.Core.Extension;
global using Dolo.Core.Discord;
global using Dolo.Bot.Apple.Hub;
global using Dolo.Bot.Apple.Hub.Handler;
global using Dolo.Bot.Apple.Hub.System;
global using Dolo.Bot.Apple.Hub.Voice.Entitiy;
global using Dolo.Bot.Apple.Hub.Voice.Event;
global using Dolo.Bot.Apple.Hub.Voice.Enum;
global using Dolo.Pluto.Shard.License;
global using Dolo.Pluto.Shard.Server;
using Dolo.Authentication;
using Dolo.Bot.Apple.Hub.Handler;
using Dolo.Pluto.Shard;
using DSharpPlus.Commands;
using DSharpPlus.VoiceNext;
using Microsoft.Extensions.Logging;

BsonRegister.Register();

var botToken = Authenticator.GetAuthValue("AppleToken");
var password = Authenticator.GetAuthValue("MongoToken");


Hub.Discord = DiscordClientBuilder.CreateDefault(botToken!, DiscordIntents.All)
    .UseCommands((b, a) =>
    {
        a.AddCommands(Assembly.GetExecutingAssembly(), 708318629112053841);
        a.AddCommands<Dolo.Bot.Shard.Rewards>();
        a.AddCheck<RequireUserCheckTemporary>();
        a.AddProcessor(new TextCommandProcessor(new()
        {
            EnableCommandNotFoundException = false,
            IgnoreBots = true,
            PrefixResolver = (_, _) => ValueTask.FromResult(0)
        }));
    }, new()
    {
        UseDefaultCommandErrorHandler = false,
        RegisterDefaultCommandProcessors = true,
    })
    .UseVoiceNext(new() {
        AudioFormat = AudioFormat.Default,
        EnableIncoming = false
    })
    .ConfigureRestClient(a => a.Timeout = new(0, 0, 30))
    .ConfigureLogging(a => a.AddConsole().SetMinimumLevel(LogLevel.Error))
    .ConfigureEventHandlers(a =>
    {
        a.HandleGuildMemberAdded((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageCreated((_, eventArgs) => MessageCreated.InvokeAsync(new(eventArgs)));
        a.HandleMessageUpdated((_, eventArgs) => MessageCreated.InvokeAsync(new(eventArgs)));
        a.HandleMessageDeleted((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleGuildMemberRemoved((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleGuildMemberUpdated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageReactionAdded((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageReactionRemoved((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleInviteCreated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleInviteDeleted((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleChannelCreated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleChannelUpdated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleVoiceStateUpdated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleComponentInteractionCreated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleModalSubmitted((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleInteractionCreated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleGuildDownloadCompleted(async (_, _) =>
        {
            if (Hub.SystemReady)
                return;

            Hub.MemberSearchSystem = new(searchSystemConfig => searchSystemConfig.SetToken(botToken)
                .SetGuild(Hub.Guild)
                .SetDiscord(Hub.Discord));

            await CacheSystem.StartAsync();
            await BirthdaySystem.StartAsync();
            await VoiceSystem.StartAsync();
            await SyncSystem.StartAsync();
            await ActivitySystem.StartAsync();
            await InviteSystem.StartAsync();


            //await HistogramSystem.StartAsync();
            Hub.Logger.LogInformation("Ready ...");
            Hub.SystemReady = true;
        });
    })
    .DisableDefaultLogging()
    .Build();

await Hub.Discord.ConnectAsync(new("Netflix", DiscordActivityType.Watching), DiscordUserStatus.Idle);

// prevent console closing
await Task.Delay(-1);
