﻿namespace Dolo.Core;

/// <summary>
///     An extension class for trying and catching exceptions.
/// </summary>
public static class TryIt
{
    /// <summary>
    ///     Try to execute an async function and ignore any exceptions.
    /// </summary>
    public static async Task ThisAsync(Func<Task> func)
    {
        try
        {
            await func().ConfigureAwait(false);
        }
        catch
        {
            // ignored
        }
    }

    /// <summary>
    ///     Try to execute an async function and invoke the provided action if an exception is thrown.
    /// </summary>
    public static async Task ThisAsync(Func<Task> func, Action<Exception> onError)
    {
        try
        {
            await func().ConfigureAwait(false);
        }
        catch (Exception e)
        {
            onError.Invoke(e);
        }
    }

    /// <summary>
    ///     Try to execute an async function and return the result or default if an exception is thrown.
    /// </summary>
    public static async Task<T?> ThisAsync<T>(Func<Task<T>> func)
    {
        try
        {
            return await func().ConfigureAwait(false);
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    ///     Try to execute an async function, invoke the provided action if an exception is thrown, and return the result or
    ///     default.
    /// </summary>
    public static async Task<T?> ThisAsync<T>(Func<Task<T>> func, Action<Exception> onError)
    {
        try
        {
            return await func().ConfigureAwait(false);
        }
        catch (Exception e)
        {
            onError.Invoke(e);
            return default;
        }
    }

    /// <summary>
    ///     Try to execute a function, invoke the provided action if an exception is thrown, and return the result or default.
    /// </summary>
    public static T? This<T>(Func<T> func, Action<Exception>? onError)
    {
        try
        {
            return func();
        }
        catch (Exception e)
        {
            onError?.Invoke(e);
            return default;
        }
    }

    /// <summary>
    ///     Try to execute a function and return the result or default if an exception is thrown.
    /// </summary>
    public static T? This<T>(Func<T> func)
    {
        try
        {
            return func();
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    ///     Try to execute an async function that returns a ValueTask and ignore any exceptions.
    /// </summary>
    public static async ValueTask TryValueTaskAsync(Func<ValueTask> func)
    {
        try
        {
            await func().ConfigureAwait(false);
        }
        catch
        {
            // ignored
        }
    }

    /// <summary>
    ///     Try to execute an async function that returns a ValueTask and invoke the provided action if an exception is thrown.
    /// </summary>
    public static async ValueTask TryValueTaskAsync(Func<ValueTask> func, Action<Exception> onError)
    {
        try
        {
            await func().ConfigureAwait(false);
        }
        catch (Exception e)
        {
            onError.Invoke(e);
        }
    }

    /// <summary>
    ///     Try to execute an async function that returns a ValueTask and return the result or default if an exception is
    ///     thrown.
    /// </summary>
    public static async ValueTask<T?> TryValueTaskAsync<T>(Func<ValueTask<T>> func)
    {
        try
        {
            return await func().ConfigureAwait(false);
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    ///     Try to execute an async function that returns a ValueTask, invoke the provided action if an exception is thrown,
    ///     and return the result or default.
    /// </summary>
    public static async ValueTask<T?> TryValueTaskAsync<T>(Func<ValueTask<T>> func, Action<Exception> onError)
    {
        try
        {
            return await func().ConfigureAwait(false);
        }
        catch (Exception e)
        {
            onError.Invoke(e);
            return default;
        }
    }
}