﻿namespace Dolo.Pluto.Shard.Services.Initialization;

public static class InitializationServiceExtensions
{
    public static IServiceCollection AddInitializationServices<TMainService>(this IServiceCollection services)
        where TMainService : class, IMainService, IService
    {
        services.AddScoped<IMainService>(provider => provider.GetRequiredService<TMainService>());
        return services;
    }
}