using Dolo.Pluto.Shard.License;
using Dolo.Pluto.Shard.License.Feature;
using Dolo.Pluto.Shard.Services.Api;

namespace Dolo.Pluto.Shard.Services;

public class ApiService(IToolboxApiService toolboxApiService, ILicenseApiService licenseApiService)
{
    public string ToolboxEndpoint => toolboxApiService.GetType().Name;
    public string LicensePermissionsEndpoint => licenseApiService.GetType().Name;
    public string LicenseFeaturesEndpoint => licenseApiService.GetType().Name;

    public async Task<Toolbox.Toolbox?> GetToolboxAsync() => await toolboxApiService.GetToolboxAsync();

    public async Task<LicensePermission[]?> GetLicensePermissionsAsync() => await licenseApiService.GetLicensePermissionsAsync();

    public async Task<IFeature[]?> GetLicenseFeaturesAsync() => await licenseApiService.GetLicenseFeaturesAsync();
}
