﻿using Dolo.Core.Consola;
using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.System;

public static class SyncSystem
{
    public static async Task StartAsync()
    {
        Consola.Information("Sync System started.");

        _ = Task.Run(async () => {
            var timer = new PeriodicTimer(new(0, 0, 15));
            while (await timer.WaitForNextTickAsync())
            {
                var members = await Hub.Guild!.TryGetAllMemberAsync();
                var dbMembers = await Mongo.ServerMembers.GetAsync();

                await AddMissingMembersAsync(members, dbMembers);
                await AddMissingRolesAsync(members, dbMembers);
                await AddMissingStateAsync(members, dbMembers);
                await AddMissingLeftMembersAsync(dbMembers);
            }
        });
    }

    private static async Task AddMissingLeftMembersAsync(List<ServerMember> members)
    {
        foreach (var member in members)
        {
            var discordMember = await Hub.Guild!.TryGetMemberAsync(member.Member.DiscordId);
            await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, member.Member.DiscordId),
            Builders<ServerMember>.Update
                .Set(a => a.State.HasLeft, discordMember == null));
        }
    }
    private static async Task AddMissingStateAsync(IEnumerable<DiscordMember> members, IReadOnlyCollection<ServerMember> dbMembers)
    {
        foreach (var member in members)
        {
            var dbMember = dbMembers.FirstOrDefault(a => a.Member.DiscordId == member.Id);
            if (dbMember is null)
                continue;

            await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, member.Id),
            Builders<ServerMember>.Update
                .Set(a => a.State.IsBanned, member.Roles.ContainsMany(HubRoles.Banned))
                .Set(a => a.State.IsBooster, member.Roles.ContainsMany(HubRoles.Boost)));
        }
    }
    private static async Task AddMissingRolesAsync(IEnumerable<DiscordMember> members, IReadOnlyCollection<ServerMember> dbMembers)
    {
        foreach (var member in members)
        {
            var dbMember = dbMembers.FirstOrDefault(a => a.Member.DiscordId == member.Id);
            if (dbMember is null)
                continue;


            if (!member.Roles.ContainsMany(HubRoles.Astro))
                await member.TryGrantRoleAsync(HubRoles.Astro);

            if (dbMember.State.IsBanned && !member.Roles.ContainsMany(HubRoles.Banned))
                await member.TryGrantRoleAsync(HubRoles.Banned);

            if (dbMember.State.IsBanned)
                await member.TryTimeoutAsync(HubConstant.TimeoutTime, $"You were banned from the server. {dbMember.State.BanReason}");
        }
    }
    private static async Task AddMissingMembersAsync(IEnumerable<DiscordMember> members, IReadOnlyCollection<ServerMember> dbMembers)
    {
        var listMembers = (from member in members
                           let dbMember = dbMembers.FirstOrDefault(a => a.Member.DiscordId == member.Id)
                           where dbMember is null
                           select new ServerMember(member, member.Roles.ContainsMany(HubRoles.Banned))).ToList();

        if (listMembers.Count != 0)
            await Mongo.ServerMembers.AddAsync(listMembers);
    }
}