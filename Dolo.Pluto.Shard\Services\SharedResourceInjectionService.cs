using Microsoft.JSInterop;
using System.Reflection;

namespace Dolo.Pluto.Shard.Services;

/// <summary>
/// Service for automatically injecting shared resources (like tailwind.js) into the DOM
/// </summary>
public class SharedResourceInjectionService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly Assembly _assembly;
    private bool _tailwindInjected = false;

    public SharedResourceInjectionService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
        _assembly = typeof(SharedResourceInjectionService).Assembly;
    }

    /// <summary>
    /// Automatically injects Tailwind.js configuration into the page
    /// </summary>
    public async Task InjectTailwindConfigAsync()
    {
        if (_tailwindInjected) return;

        try
        {
            var tailwindContent = await GetEmbeddedResourceContentAsync("tailwind.js");

            if (!string.IsNullOrEmpty(tailwindContent))
            {
                // Inject the content directly into the page (MAUI Blazor approach)
                await _jsRuntime.InvokeVoidAsync("eval", $@"
                    if (!document.getElementById('shard-tailwind-config')) {{
                        const script = document.createElement('script');
                        script.id = 'shard-tailwind-config';
                        script.type = 'text/javascript';
                        script.textContent = `{tailwindContent.Replace("`", "\\`").Replace("$", "\\$")}`;
                        document.head.appendChild(script);
                        console.log('✅ Shard: Tailwind config injected');
                    }}
                ");

                _tailwindInjected = true;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to inject Tailwind config: {ex.Message}");
        }
    }

    /// <summary>
    /// Injects custom CSS from Shard
    /// </summary>
    public async Task InjectSharedCssAsync(string fileName)
    {
        try
        {
            var cssContent = await GetEmbeddedResourceContentAsync(fileName);
            if (!string.IsNullOrEmpty(cssContent))
            {
                await _jsRuntime.InvokeVoidAsync("eval", $@"
                    if (!document.getElementById('shard-{fileName}')) {{
                        const style = document.createElement('style');
                        style.id = 'shard-{fileName}';
                        style.textContent = `{cssContent.Replace("`", "\\`").Replace("$", "\\$")}`;
                        document.head.appendChild(style);
                        console.log('✅ Shard: {fileName} injected');
                    }}
                ");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to inject {fileName}: {ex.Message}");
        }
    }

    /// <summary>
    /// Gets the content of an embedded resource
    /// </summary>
    private async Task<string?> GetEmbeddedResourceContentAsync(string fileName)
    {
        var resourceName = $"Dolo.Pluto.Shard.wwwroot.{fileName}";

        await using var stream = _assembly.GetManifestResourceStream(resourceName);
        if (stream == null) return null;

        using var reader = new StreamReader(stream);
        return await reader.ReadToEndAsync();
    }

    /// <summary>
    /// Initializes all shared resources automatically
    /// </summary>
    public async Task InitializeSharedResourcesAsync()
    {
        await InjectTailwindConfigAsync();

        // Add other shared resources here as needed
        // await InjectSharedCssAsync("shared-styles.css");
    }
}
