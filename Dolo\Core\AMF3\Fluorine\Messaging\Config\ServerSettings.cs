﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Contains the properties for configuring server settings for message destinations.
///     This is the <b>server</b> element in the services-config.xml file.
/// </summary>
internal sealed class ServerSettings : Hashtable
{
    internal ServerSettings()
    {}

    internal ServerSettings(XmlNode severDefinitionNode)
    {
        foreach (XmlNode propertyNode in severDefinitionNode.SelectNodes("*"))
            if (propertyNode.InnerXml != null && propertyNode.InnerXml != string.Empty)
                this[propertyNode.Name] = propertyNode.InnerXml;
            else
            {
                if (propertyNode.Attributes != null)
                    foreach (XmlAttribute attribute in propertyNode.Attributes)
                        this[propertyNode.Name + "_" + attribute.Name] = attribute.Value;
            }
    }

    /// <summary>
    ///     Gets whether subtopics are allowed.
    /// </summary>
    public bool AllowSubtopics
    {
        get
        {
            if (Contains("allow-subtopics"))
                return this["allow-subtopics"] is {} && (bool)this["allow-subtopics"];
            return false;
        }
    }
}