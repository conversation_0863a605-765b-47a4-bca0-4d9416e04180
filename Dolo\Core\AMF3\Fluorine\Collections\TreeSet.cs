﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     A sorted set.
/// </summary>
internal interface ISortedSet : ICollection, IList
{
    /// <summary>
    ///     Returns a portion of the list whose elements are greater than the limit object parameter.
    /// </summary>
    /// <param name="limit">The start element of the portion to extract.</param>
    /// <returns>The portion of the collection whose elements are greater than the limit object parameter.</returns>
    ISortedSet TailSet(object limit);
}

[Serializable]
internal class TreeSet : ArrayList, ISortedSet
{
    /// <summary>
    ///     Initializes a new instance of the <see cref="TreeSet" /> class.
    /// </summary>
    public TreeSet()
    {}

    /// <summary>
    ///     Initializes a new instance of the <see cref="TreeSet" /> class.
    /// </summary>
    /// <param name="c">The <see cref="T:System.Collections.ICollection" /> whose elements are copied to the new list.</param>
    /// <exception cref="T:System.ArgumentNullException">
    ///     <paramref name="c" /> is <see langword="null" />.
    /// </exception>
    public TreeSet(ICollection c)
    {
        AddAll(c);
    }

    /// <summary>
    ///     Initializes a new instance of the <see cref="TreeSet" /> class.
    /// </summary>
    /// <param name="c">The c.</param>
    public TreeSet(IComparer c)
        => Comparer = c;

    /// <summary>
    ///     Gets the IComparer object used to sort this set.
    /// </summary>
    public IComparer Comparer { get; } = System.Collections.Comparer.Default;

    /// <summary>
    ///     Determines whether an element is in the the current TreeSetSupport collection. The IComparer defined for
    ///     the current set will be used to make comparisons between the elements already inserted in the collection and
    ///     the item specified.
    /// </summary>
    /// <param name="item">The object to be locatet in the current collection.</param>
    /// <returns>true if item is found in the collection; otherwise, false.</returns>
    public override bool Contains(object item)
    {
        var tempEnumerator = GetEnumerator();
        while (tempEnumerator.MoveNext())
            if (Comparer.Compare(tempEnumerator.Current, item) == 0)
                return true;

        return false;
    }


    /// <summary>
    ///     Returns a portion of the list whose elements are greater than the limit object parameter.
    /// </summary>
    /// <param name="limit">The start element of the portion to extract.</param>
    /// <returns>The portion of the collection whose elements are greater than the limit object parameter.</returns>
    public ISortedSet TailSet(object limit)
    {
        ISortedSet newList = new TreeSet();
        var i = 0;
        while (i < Count && Comparer.Compare(this[i], limit) < 0) i++;
        for (; i < Count; i++) newList.Add(this[i]);
        return newList;
    }

    /// <summary>
    ///     Unmodifiables the tree set.
    /// </summary>
    /// <param name="collection">The collection.</param>
    /// <returns></returns>
    public static TreeSet UnmodifiableTreeSet(ICollection collection)
    {
        var items = new ArrayList(collection);
        items = ReadOnly(items);
        return new(items);
    }

    private bool AddWithoutSorting(object obj)
    {
        bool inserted;
        if (!(inserted = Contains(obj))) base.Add(obj);
        return !inserted;
    }

    /// <summary>
    ///     Adds a new element to the ArrayList if it is not already present and sorts the ArrayList.
    /// </summary>
    /// <param name="obj">Element to insert to the ArrayList.</param>
    /// <returns>true if the new element was inserted, false otherwise.</returns>
    public new bool Add(object obj)
    {
        var inserted = AddWithoutSorting(obj);
        Sort(Comparer);
        return inserted;
    }

    /// <summary>
    ///     Adds all the elements of the specified collection that are not present to the list.
    /// </summary>
    /// <param name="c">Collection where the new elements will be added</param>
    /// <returns>Returns true if at least one element was added to the collection.</returns>
    public bool AddAll(ICollection c)
    {
        var e = new ArrayList(c).GetEnumerator();
        var added = false;
        while (e.MoveNext())
            if (AddWithoutSorting(e.Current))
                added = true;

        Sort(Comparer);
        return added;
    }

    /// <summary>
    ///     Returns the first item in the set.
    /// </summary>
    /// <returns>First object.</returns>
    public object First()
        => this[0];
}