﻿using Dolo.Core.Consola;
using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.System;

public static class BirthdaySystem
{
    public static async Task StartAsync()
    {
        Consola.Information("Birthday system started.");

        _ = Task.Run(async () => {
            PeriodicTimer timer = new(new(0, 0, 1));

            while (await timer.WaitForNextTickAsync())
                try
                {
                    // continue if guild is null
                    if (Hub.Guild is null)
                        continue;

                    var members = await Mongo.ServerMembers.GetAsync(a => a.Birthday != null);

                    foreach (var mem in members.ToList().Where(mem => mem.Birthday != null && HubChannel.Chat != null && mem.Birthday.HasBirthday()))
                    {
                        // check if the member is in the server if no then continue
                        if (!Hub.Guild.TryGetMember(mem.Member.DiscordId, out var member))
                            continue;

                        // add birthday role to member
                        await member.TryGrantRoleAsync(HubRoles.Birthday);

                        // send happy birthday to the chat
                        var msg = await HubChannel.Chat!.TrySendMessageAsync($"{member.Mention} Happy Birthday! {HubEmoji.Tada}{HubEmoji.WhiteHeart}{HubEmoji.WhiteFlyingHeart}{HubEmoji.WhitePopHeart}");

                        // send random image
                        await HubChannel.Chat!.TrySendMessageAsync(HubConstant.BirthdayImages.Shuffle().First());

                        // add reaction to the message
                        if (msg != null)
                        {
                            await msg.TryCreateReactionAsync(HubEmoji.Tada);
                            await msg.TryCreateReactionAsync(HubEmoji.WhiteHeart);
                            await msg.TryCreateReactionAsync(HubEmoji.WhitePopHeart);
                        }

                        // update the birthday in the database
                        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, mem.Member.DiscordId), Builders<ServerMember>.Update.Set(a => a.Birthday, null));
                    }

                    foreach (var mem in members.Where(a => a.Birthday != null))
                        if (mem.Birthday!.DayOfBirth.Year != DateTime.Now.Year)
                            await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, mem.Member.DiscordId), Builders<ServerMember>.Update.Set(a => a.Birthday, null));
                }
                catch (Exception ex)
                {
                    ex.Log();
                }
        });
    }
}