using Dolo.Core.Http;
using Dolo.Nebula.Entities;
using Newtonsoft.Json;
namespace Dolo.Nebula.Services;

internal static class GetProfilesService
{
    private static string GetQuery() => "query getProfiles($profileIds: [String!]!, $gameId: String!){ profiles(profileIds: $profileIds){ id name culture avatar(preferredGameId: $gameId){ gameId face full } membership { currentTier } level(gameId: $gameId)} }";

    public static async Task<GetProfilesResponse?> GetProfilesAsync(NebulaClient client, params string?[] profiles)
    {
        if (string.IsNullOrEmpty(client.Config.Auth))
            throw new NullReferenceException("auth-key is required on this method");

        var data = await Http.TrySendAsync<GetProfilesResponse>(a => {
            a.Url = $"{client.Services?.GetEdgeRelationships()}/graphql";
            a.Method = HttpMethod.Post;
            a.AuthToken = client.Config.Auth;
            a.ContentType = HttpContentType.ApplicationJson;
            a.Content = new StringContent(JsonConvert.SerializeObject(new GraphQuery
            {
                Query = GetQuery(),
                Variables = JsonConvert.SerializeObject(new Dictionary<string, object>
                {
                    { "profileIds", profiles },
                    { "gameId", client.Config.Game.GetValueOrDefault().GetNebulaGame() }
                })
            }));
        });

        Console.WriteLine(await data.TryGetStringAsync());

        return data.Body;
    }
}
