{"$schema": "https://raw.githubusercontent.com/neutralinojs/neutralinojs/main/schemas/neutralino.config.schema.json", "applicationId": "Dolo.Pluto.Toolbox", "version": "2025.2.1", "defaultMode": "window", "documentRoot": "/react-src/build/", "url": "/", "enableServer": true, "enableNativeAPI": true, "logging": {"enabled": true, "writeToLogFile": true}, "nativeAllowList": ["app.*", "os.*", "computer.*", "debug.log", "filesystem.*", "window.*"], "modes": {"window": {"title": "Toolbox", "width": 700, "height": 600, "minWidth": 535, "minHeight": 615, "center": true, "fullScreen": false, "alwaysOnTop": false, "icon": "/react-src/public/logo.png", "enableInspector": false, "borderless": false, "maximize": false, "hidden": false, "resizable": true, "exitProcessOnClose": true, "backgroundColor": "#00000000", "transparent": false}}, "cli": {"binaryName": "Toolbox", "resourcesPath": "/react-src/build/", "extensionsPath": "/extensions/", "binaryVersion": "6.2.0", "clientVersion": "6.2.0"}}