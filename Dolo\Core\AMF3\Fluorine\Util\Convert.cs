﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
using System.Xml.Linq;
namespace Dolo.Core.AMF3.Fluorine.Util;

/// <summary>
///     Converts a base data type to another base data type.
/// </summary>
internal class Convert
{
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The String equivalent of the 8-bit signed integer value.</returns>
    public static string ToString(sbyte value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The String equivalent of the 16-bit signed integer value.</returns>
    public static string ToString(short value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The String equivalent of the 32-bit signed integer value.</returns>
    public static string ToString(int value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The String equivalent of the 64-bit signed integer value.</returns>
    public static string ToString(long value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A 8-bit unsigned integer.</param>
    /// <returns>The String equivalent of the 8-bit unsigned integer value.</returns>
    public static string ToString(byte value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The String equivalent of the 16-bit unsigned integer value.</returns>
    public static string ToString(ushort value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The String equivalent of the 32-bit unsigned integer value.</returns>
    public static string ToString(uint value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The String equivalent of the 64-bit unsigned integer value.</returns>
    public static string ToString(ulong value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent String representation.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The String equivalent of the single-precision floating point number.</returns>
    public static string ToString(float value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent String representation.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The String equivalent of the double-precision floating point number.</returns>
    public static string ToString(double value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent String representation.
    /// </summary>
    /// <param name="value">A Boolean value.</param>
    /// <returns>The String equivalent of the Boolean.</returns>
    public static string ToString(bool value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent String representation.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The String equivalent of the Decimal number.</returns>
    public static string ToString(decimal value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent String representation.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The String equivalent of the Unicode character.</returns>
    public static string ToString(char value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified TimeSpan to its equivalent String representation.
    /// </summary>
    /// <param name="value">A TimeSpan.</param>
    /// <returns>The String equivalent of the TimeSpan.</returns>
    public static string ToString(TimeSpan value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified DateTime to its equivalent String representation.
    /// </summary>
    /// <param name="value">A DateTime.</param>
    /// <returns>The String equivalent of the DateTime.</returns>
    public static string ToString(DateTime value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified Guid to its equivalent String representation.
    /// </summary>
    /// <param name="value">A Guid.</param>
    /// <returns>The String equivalent of the Guid.</returns>
    public static string ToString(Guid value)
        => value.ToString();

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The String equivalent of the nullable 8-bit signed integer value.</returns>
    public static string ToString(sbyte? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The String equivalent of the nullable 16-bit signed integer value.</returns>
    public static string ToString(short? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The String equivalent of the nullable 32-bit signed integer value.</returns>
    public static string ToString(int? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The String equivalent of the nullable 64-bit signed integer value.</returns>
    public static string ToString(long? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer  to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The String equivalent of the nullable 8-bit unsigned integer value.</returns>
    public static string ToString(byte? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The String equivalent of the nullable 16-bit unsigned integer value.</returns>
    public static string ToString(ushort? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The String equivalent of the nullable 32-bit unsigned integer value.</returns>
    public static string ToString(uint? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The String equivalent of the nullable 64-bit unsigned integer value.</returns>
    public static string ToString(ulong? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent String
    ///     representation.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The String equivalent of the nullable single-precision floating point number.</returns>
    public static string ToString(float? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent String
    ///     representation.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The String equivalent of the nullable double-precision floating point number.</returns>
    public static string ToString(double? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable Boolean value.</param>
    /// <returns>The String equivalent of the nullable Boolean value.</returns>
    public static string ToString(bool? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The String equivalent of the nullable Decimal number.</returns>
    public static string ToString(decimal? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The String equivalent of the nullable Unicode character.</returns>
    public static string ToString(char? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable TimeSpan to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable TimeSpan.</param>
    /// <returns>The String equivalent of the nullable TimeSpan.</returns>
    public static string ToString(TimeSpan? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable DateTime to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable DateTime.</param>
    /// <returns>The String equivalent of the nullable DateTime.</returns>
    public static string ToString(DateTime? value)
        => value.ToString();
    /// <summary>
    ///     Converts the value of the specified nullable Guid to its equivalent String representation.
    /// </summary>
    /// <param name="value">A nullable Guid.</param>
    /// <returns>The String equivalent of the nullable Guid.</returns>
    public static string ToString(Guid? value)
        => value.ToString();

    /// <summary>
    ///     Converts the value of the specified Type to its equivalent String representation.
    /// </summary>
    /// <param name="value">A Type.</param>
    /// <returns>The String equivalent of the Type.</returns>
    public static string ToString(Type value)
        => value == null ? null : value.FullName;
    /// <summary>
    ///     Converts the value of the specified XmlDocument to its equivalent String representation.
    /// </summary>
    /// <param name="value">An XmlDocument.</param>
    /// <returns>The String equivalent of the XmlDocument.</returns>
    public static string ToString(XmlDocument value)
        => value == null ? null : value.InnerXml;
    /// <summary>
    ///     Converts the value of the specified Object to its equivalent String representation.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The String equivalent of the Object.</returns>
    public static string ToString(object value)
    {
        if (value == null || value is DBNull) return string.Empty;

        if (value is string) return (string)value;

        // Scalar Types.
        //
        if (value is char) return ToString((char)value);
        if (value is TimeSpan) return ToString((TimeSpan)value);
        if (value is DateTime) return ToString((DateTime)value);
        if (value is Guid) return ToString((Guid)value);

        if (value is XmlDocument) return ToString((XmlDocument)value);

        if (value is Type) return ToString((Type)value);

        if (value is IConvertible) return ((IConvertible)value).ToString(null);
        if (value is IFormattable) return ((IFormattable)value).ToString(null, null);

        return value.ToString();
    }

    /// <summary>
    ///     Checks whether the value of the specified Object can be converted to a Unicode character.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>Returns true if the specified Object can be converted to a Unicode character.</returns>
    public static bool CanConvertToChar(object value)
    {
        if (value == null || value is DBNull) return true;

        if (value is char) return true;
        // Scalar Types.
        if (value is string) return value == null || (value as string).Length == 1;
        if (value is bool) return true;

        if (value is IConvertible)
            try
            {
                ((IConvertible)value).ToChar(null);
                return true;
            }
            catch (InvalidCastException)
            {
                return false;
            }
        return false;
    }

    /// <summary>
    ///     Checks whether the value of the specified Object can be converted to a nullable Unicode character.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>Returns true if the specified Object can be converted to a nullable Unicode character.</returns>
    public static bool CanConvertToNullableChar(object value)
    {
        if (value == null || value is DBNull) return true;

        // Scalar Types.
        //
        if (value is char) return true;
        if (value is string) return value == null || (value as string).Length == 1;

        if (value is bool) return true;

        if (value is IConvertible)
            try
            {
                ((IConvertible)value).ToChar(null);
                return true;
            }
            catch (InvalidCastException)
            {
                return false;
            }
        return false;
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(string value)
        => value == null ? (sbyte)0 : sbyte.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(short value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(int value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(long value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(byte value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">An 16-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(ushort value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">An 32-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(uint value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(ulong value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(float value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(double value)
        => checked((sbyte)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(decimal value)
        => (sbyte)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(bool value)
        => (sbyte)(value ? 1 : 0);
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(char value)
        => checked((sbyte)value);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(sbyte? value)
        => value.HasValue ? value.Value : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(short? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(int? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(long? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(byte? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(ushort? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(uint? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(ulong? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(float? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(double? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(decimal? value)
        => value.HasValue ? (sbyte)value.Value : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(char? value)
        => value.HasValue ? checked((sbyte)value.Value) : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(bool? value)
        => value.HasValue && value.Value ? (sbyte)1 : (sbyte)0;
    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 8-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 8-bit signed integer value.</returns>
    public static sbyte ToSByte(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is sbyte) return (sbyte)value;

        // Scalar Types.
        //
        if (value is string) return ToSByte((string)value);

        if (value is bool) return ToSByte((bool)value);
        if (value is char) return ToSByte((char)value);

        // SqlTypes.
        //

        if (value is IConvertible) return ((IConvertible)value).ToSByte(null);

        throw CreateInvalidCastException(value.GetType(), typeof(sbyte));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(string value)
        => value == null ? false : bool.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(sbyte value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(short value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(int value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(long value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(byte value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(ushort value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(uint value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(ulong value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(float value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(double value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(decimal value)
        => value != 0;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(char value)
    {
        switch (value)
        {
            case (char)0: return false;// Allow int <=> Char <=> Boolean
            case '0':     return false;
            case 'n':     return false;
            case 'N':     return false;
            case 'f':     return false;
            case 'F':     return false;

            case (char)1: return true;// Allow int <=> Char <=> Boolean
            case '1':     return true;
            case 'y':     return true;
            case 'Y':     return true;
            case 't':     return true;
            case 'T':     return true;
        }

        throw new InvalidCastException(string.Format("Invalid cast from {0} to {1}", typeof(char).FullName, typeof(bool).FullName));
    }

    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(bool? value)
        => value.HasValue ? value.Value : false;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(sbyte? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(short? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(int? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(long? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(byte? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(ushort? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(uint? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(ulong? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent Boolean
    ///     value.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(float? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent Boolean
    ///     value.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(double? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(decimal? value)
        => value.HasValue ? value.Value != 0 : false;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(char? value)
        => value.HasValue ? ToBoolean(value.Value) : false;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent Boolean value.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent Boolean value.</returns>
    public static bool ToBoolean(object value)
    {
        if (value == null || value is DBNull) return false;

        if (value is bool) return (bool)value;

        // Scalar Types.
        //
        if (value is string) return ToBoolean((string)value);

        if (value is char) return ToBoolean((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToBoolean(null);

        throw CreateInvalidCastException(value.GetType(), typeof(bool));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(string value)
        => value == null ? (byte)0 : byte.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(sbyte value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(short value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(int value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(long value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(ushort value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(uint value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(ulong value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(float value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(double value)
        => checked((byte)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(decimal value)
        => (byte)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(bool value)
        => (byte)(value ? 1 : 0);
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(char value)
        => checked((byte)value);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(byte? value)
        => value.HasValue ? value.Value : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(sbyte? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(short? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(int? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(long? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(ushort? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(uint? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(ulong? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 8-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(float? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 8-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(double? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(decimal? value)
        => value.HasValue ? (byte)value.Value : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(char? value)
        => value.HasValue ? checked((byte)value.Value) : (byte)0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(bool? value)
        => value.HasValue && value.Value ? (byte)1 : (byte)0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 8-bit unsigned integer value.</returns>
    public static byte ToByte(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is byte) return (byte)value;

        // Scalar Types.
        //
        if (value is string) return ToByte((string)value);

        if (value is bool) return ToByte((bool)value);
        if (value is char) return ToByte((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToByte(null);

        throw CreateInvalidCastException(value.GetType(), typeof(byte));
    }

    public static byte[] ToByteArray(string p)
        => p == null ? null : Encoding.UTF8.GetBytes(p);

    public static byte[] ToByteArray(sbyte p)
        =>
        [
            checked((byte)p)
        ];
    public static byte[] ToByteArray(short p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(int p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(long p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(byte p)
        =>
        [
            p
        ];

    public static byte[] ToByteArray(ushort p)
        => BitConverter.GetBytes(p);

    public static byte[] ToByteArray(uint p)
        => BitConverter.GetBytes(p);

    public static byte[] ToByteArray(ulong p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(char p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(float p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(double p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(bool p)
        => BitConverter.GetBytes(p);
    public static byte[] ToByteArray(DateTime p)
        => ToByteArray(p.ToBinary());
    public static byte[] ToByteArray(TimeSpan p)
        => ToByteArray(p.Ticks);
    public static byte[] ToByteArray(Guid p)
        => p == Guid.Empty ? null : p.ToByteArray();

    public static byte[] ToByteArray(decimal p)
    {
        var bits = decimal.GetBits(p);
        var bytes = new byte[bits.Length << 2];

        for (var i = 0; i < bits.Length; ++i)
            Buffer.BlockCopy(BitConverter.GetBytes(bits[i]), 0, bytes, i * 4, 4);

        return bytes;
    }

    public static byte[] ToByteArray(Stream p)
    {
        if (p == null) return null;
        if (p is MemoryStream) return ((MemoryStream)p).ToArray();

        var position = p.Seek(0, SeekOrigin.Begin);
        var bytes = new byte[p.Length];
        p.Read(bytes, 0, bytes.Length);
        p.Position = position;

        return bytes;
    }

    public static byte[] ToByteArray(ByteArray p)
        => p?.GetBuffer();

    public static byte[] ToByteArray(Guid? p)
        => p.HasValue ? p.Value.ToByteArray() : null;

    public static byte[] ToByteArray(sbyte? p)
        => p.HasValue ?
        [
            checked((byte)p.Value)
        ] : null;
    public static byte[] ToByteArray(short? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(int? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(long? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(byte? p)
        => p.HasValue ?
        [
            p.Value
        ] : null;

    public static byte[] ToByteArray(ushort? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;

    public static byte[] ToByteArray(uint? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;

    public static byte[] ToByteArray(ulong? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(char? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(float? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(double? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(bool? p)
        => p.HasValue ? BitConverter.GetBytes(p.Value) : null;
    public static byte[] ToByteArray(DateTime? p)
        => p.HasValue ? ToByteArray(p.Value.ToBinary()) : null;
    public static byte[] ToByteArray(TimeSpan? p)
        => p.HasValue ? ToByteArray(p.Value.Ticks) : null;
    public static byte[] ToByteArray(decimal? p)
        => p.HasValue ? ToByteArray(p.Value) : null;

    public static byte[] ToByteArray(object p)
    {
        if (p == null || p is DBNull) return null;

        if (p is byte[]) return (byte[])p;

        // Scalar Types.
        //
        if (p is string) return ToByteArray((string)p);

        if (p is sbyte) return ToByteArray((sbyte)p);
        if (p is short) return ToByteArray((short)p);
        if (p is int) return ToByteArray((int)p);
        if (p is long) return ToByteArray((long)p);

        if (p is byte) return ToByteArray((byte)p);
        if (p is ushort) return ToByteArray((ushort)p);
        if (p is uint) return ToByteArray((uint)p);
        if (p is ulong) return ToByteArray((ulong)p);

        if (p is char) return ToByteArray((char)p);
        if (p is float) return ToByteArray((float)p);
        if (p is double) return ToByteArray((double)p);
        if (p is bool) return ToByteArray((bool)p);
        if (p is decimal) return ToByteArray((decimal)p);

        if (p is DateTime) return ToByteArray((DateTime)p);
        if (p is TimeSpan) return ToByteArray((TimeSpan)p);

        if (p is Stream) return ToByteArray((Stream)p);
        if (p is Guid) return ToByteArray((Guid)p);

        if (p is ByteArray) return ToByteArray((ByteArray)p);

        throw CreateInvalidCastException(p.GetType(), typeof(byte[]));
    }

    /// <summary>
    ///     Converts the first character of the specified String to a Unicode character.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent Unicode character.</returns>
    public static char ToChar(string value)
    {
        char result;
        if (char.TryParse(value, out result))
            return result;
        return (char)0;
    }
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(sbyte value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(short value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(int value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(long value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(byte value)
        => (char)value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character.</returns>
    public static char ToChar(ushort value)
        => (char)value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(uint value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(ulong value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(float value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(double value)
        => checked((char)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The Unicode character value.</returns>
    public static char ToChar(decimal value)
        => (char)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to a Unicode character.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The Unicode character value.</returns>
    public static char ToChar(bool value)
        => (char)(value ? 1 : 0);

    /// <summary>
    ///     Converts the specified nullable character to a Unicode character.
    /// </summary>
    /// <param name="value">A nullable Char.</param>
    /// <returns>The Unicode character value.</returns>
    public static char ToChar(char? value)
        => value.HasValue ? value.Value : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent Unicode character.</returns>
    public static char ToChar(sbyte? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(short? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(int? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(long? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(byte? value)
        => value.HasValue ? (char)value.Value : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(ushort? value)
        => value.HasValue ? (char)value.Value : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(uint? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(ulong? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to a Unicode character.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The Unicode character value.</returns>
    public static char ToChar(float? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to a Unicode character.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The Unicode character value.</returns>
    public static char ToChar(double? value)
        => value.HasValue ? checked((char)value.Value) : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to a Unicode character.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The Unicode character value.</returns>
    public static char ToChar(decimal? value)
        => value.HasValue ? (char)value.Value : (char)0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to a Unicode character.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The Unicode character value.</returns>
    public static char ToChar(bool? value)
        => value.HasValue && value.Value ? (char)1 : (char)0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent Unicode character.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent Unicode character value.</returns>
    public static char ToChar(object value)
    {
        if (value == null || value is DBNull) return '\x0';

        if (value is char) return (char)value;

        // Scalar Types.
        //
        if (value is string) return ToChar((string)value);
        if (value is bool) return ToChar((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToChar(null);

        throw CreateInvalidCastException(value.GetType(), typeof(char));
    }

    public static char[] ToCharArray(string p)
        => p == null ? null : p.ToCharArray();

    public static char[] ToCharArray(object p)
    {
        if (p == null || p is DBNull) return null;

        if (p is char[]) return (char[])p;

        // Scalar Types.
        //
        if (p is string) return ToCharArray((string)p);

        return ToString(p).ToCharArray();
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(string value)
        => value == null ? DateTime.MinValue : DateTime.Parse(value);
    /// <summary>
    ///     Converts the value of the specified TimeSpan to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A TimeSpan.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(TimeSpan value)
        => DateTime.MinValue + value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(long value)
        => DateTime.MinValue + TimeSpan.FromTicks(value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(double value)
        => DateTime.MinValue + TimeSpan.FromDays(value);

    /// <summary>
    ///     Converts the value of the specified nullable DateTime to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A nullable DateTime.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(DateTime? value)
        => value.HasValue ? value.Value : DateTime.MinValue;
    /// <summary>
    ///     Converts the value of the specified nullable TimeSpan to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A nullable TimeSpan.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(TimeSpan? value)
        => value.HasValue ? DateTime.MinValue + value.Value : DateTime.MinValue;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer number to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(long? value)
        => value.HasValue ? DateTime.MinValue + TimeSpan.FromTicks(value.Value) : DateTime.MinValue;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent DateTime.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(double? value)
        => value.HasValue ? DateTime.MinValue + TimeSpan.FromDays(value.Value) : DateTime.MinValue;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent DateTime.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent DateTime.</returns>
    public static DateTime ToDateTime(object value)
    {
        if (value == null || value is DBNull) return DateTime.MinValue;

        if (value is DateTime) return (DateTime)value;

        // Scalar Types.
        //
        if (value is string) return ToDateTime((string)value);
        if (value is TimeSpan) return ToDateTime((TimeSpan)value);
        if (value is long) return ToDateTime((long)value);
        if (value is double) return ToDateTime((double)value);

        if (value is IConvertible) return ((IConvertible)value).ToDateTime(null);

        throw CreateInvalidCastException(value.GetType(), typeof(DateTime));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(string value)
        => value == null ? 0.0m : decimal.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(long value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(ulong value)
        => value;
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(float value)
        => (decimal)value;
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(double value)
        => (decimal)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(bool value)
        => value ? 1.0m : 0.0m;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(char value)
        => value;

    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(decimal? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(sbyte? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(short? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(int? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(long? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(byte? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(ushort? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(uint? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(ulong? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent Decimal
    ///     number.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(float? value)
        => value.HasValue ? (decimal)value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent Decimal
    ///     number.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(double? value)
        => value.HasValue ? (decimal)value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(char? value)
        => value.HasValue ? value.Value : 0.0m;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(bool? value)
        => value.HasValue && value.Value ? 1.0m : 0.0m;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent Decimal number.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent Decimal number.</returns>
    public static decimal ToDecimal(object value)
    {
        if (value == null || value is DBNull) return 0.0m;

        if (value is decimal) return (decimal)value;

        // Scalar Types.
        //
        if (value is string) return ToDecimal((string)value);

        if (value is bool) return ToDecimal((bool)value);
        if (value is char) return ToDecimal((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToDecimal(null);

        throw CreateInvalidCastException(value.GetType(), typeof(decimal));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(string value)
        => value == null ? 0.0 : double.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(long value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(ulong value)
        => value;
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(float value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(decimal value)
        => (double)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(bool value)
        => value ? 1.0 : 0.0;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(char value)
        => value;
    public static double ToDouble(DateTime value)
        => (value - DateTime.MinValue).TotalDays;
    public static double ToDouble(TimeSpan value)
        => value.TotalDays;

    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent
    ///     double-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(double? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(sbyte? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(short? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(int? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(long? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(byte? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(ushort? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(uint? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(ulong? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent
    ///     double-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(float? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(decimal? value)
        => value.HasValue ? (double)value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(char? value)
        => value.HasValue ? value.Value : 0.0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(bool? value)
        => value.HasValue && value.Value ? 1.0 : 0.0;
    public static double ToDouble(DateTime? value)
        => value.HasValue ? (value.Value - DateTime.MinValue).TotalDays : 0.0;
    public static double ToDouble(TimeSpan? value)
        => value.HasValue ? value.Value.TotalDays : 0.0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent double-precision floating point number.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent double-precision floating point number.</returns>
    public static double ToDouble(object value)
    {
        if (value == null || value is DBNull) return 0.0;

        if (value is double) return (double)value;

        // Scalar Types.
        //
        if (value is string) return ToDouble((string)value);

        if (value is bool) return ToDouble((bool)value);
        if (value is char) return ToDouble((char)value);
        if (value is DateTime) return ToDouble((DateTime)value);
        if (value is TimeSpan) return ToDouble((TimeSpan)value);

        if (value is IConvertible) return ((IConvertible)value).ToDouble(null);

        throw CreateInvalidCastException(value.GetType(), typeof(double));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(string value)
        => value == null ? (short)0 : short.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(int value)
        => checked((short)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(long value)
        => checked((short)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(ushort value)
        => checked((short)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(uint value)
        => checked((short)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(ulong value)
        => checked((short)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(float value)
        => checked((short)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(double value)
        => checked((short)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(decimal value)
        => (short)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(bool value)
        => (short)(value ? 1 : 0);
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(char value)
        => checked((short)value);

    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(short? value)
        => value.HasValue ? value.Value : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(sbyte? value)
        => value.HasValue ? value.Value : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(int? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(long? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(byte? value)
        => value.HasValue ? value.Value : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(ushort? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(uint? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(ulong? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(float? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(double? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(decimal? value)
        => value.HasValue ? (short)value.Value : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(char? value)
        => value.HasValue ? checked((short)value.Value) : (short)0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(bool? value)
        => value.HasValue && value.Value ? (short)1 : (short)0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 16-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 16-bit signed integer value.</returns>
    public static short ToInt16(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is short) return (short)value;

        // Scalar Types.
        //
        if (value is string) return ToInt16((string)value);

        if (value is bool) return ToInt16((bool)value);
        if (value is char) return ToInt16((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToInt16(null);

        throw CreateInvalidCastException(value.GetType(), typeof(short));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(string value)
        => value == null ? 0 : int.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(long value)
        => checked((int)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(uint value)
        => checked((int)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(ulong value)
        => checked((int)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(float value)
        => checked((int)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(double value)
        => checked((int)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(decimal value)
        => (int)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(bool value)
        => value ? 1 : 0;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(char value)
        => value;

    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(int? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(sbyte? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(short? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(long? value)
        => value.HasValue ? checked((int)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(byte? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(ushort? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(uint? value)
        => value.HasValue ? checked((int)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(ulong? value)
        => value.HasValue ? checked((int)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(float? value)
        => value.HasValue ? checked((int)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(double? value)
        => value.HasValue ? checked((int)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(decimal? value)
        => value.HasValue ? (int)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(char? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(bool? value)
        => value.HasValue && value.Value ? 1 : 0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 32-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 32-bit signed integer value.</returns>
    public static int ToInt32(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is int) return (int)value;

        // Scalar Types.
        //
        if (value is string) return ToInt32((string)value);

        if (value is bool) return ToInt32((bool)value);
        if (value is char) return ToInt32((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToInt32(null);

        throw CreateInvalidCastException(value.GetType(), typeof(int));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(string value)
        => value == null ? 0 : long.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(ulong value)
        => checked((long)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(float value)
        => checked((long)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(double value)
        => checked((long)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(decimal value)
        => (long)value;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(bool value)
        => value ? 1 : 0;
    /// <summary>
    ///     Converts the value of the specified DateTime to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A DateTime.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(DateTime value)
        => (value - DateTime.MinValue).Ticks;
    /// <summary>
    ///     Converts the value of the specified TimeSpan to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A TimeSpan.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(TimeSpan value)
        => value.Ticks;

    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(long? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(sbyte? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(short? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(int? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(byte? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(ushort? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(uint? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(ulong? value)
        => value.HasValue ? checked((long)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(float? value)
        => value.HasValue ? checked((long)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(double? value)
        => value.HasValue ? checked((long)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(decimal? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(char? value)
        => value.HasValue ? (long)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(bool? value)
        => value.HasValue && value.Value ? 1 : 0;
    /// <summary>
    ///     Converts the value of the specified nullable DateTime to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable DateTime.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(DateTime? value)
        => value.HasValue ? (value.Value - DateTime.MinValue).Ticks : 0;
    /// <summary>
    ///     Converts the value of the specified nullable TimeSpan to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable TimeSpan.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(TimeSpan? value)
        => value.HasValue ? value.Value.Ticks : 0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 64-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 64-bit signed integer value.</returns>
    public static long ToInt64(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is long) return (long)value;

        // Scalar Types.
        //
        if (value is string) return ToInt64((string)value);

        if (value is char) return ToInt64((char)value);
        if (value is bool) return ToInt64((bool)value);
        if (value is DateTime) return ToInt64((DateTime)value);
        if (value is TimeSpan) return ToInt64((TimeSpan)value);

        if (value is IConvertible) return ((IConvertible)value).ToInt64(null);

        throw CreateInvalidCastException(value.GetType(), typeof(long));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent Guid.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent Guid.</returns>
    public static Guid ToGuid(string value)
        => value == null ? Guid.Empty : new(value);

    /// <summary>
    ///     Converts the value of the specified nullable Guid to its equivalent Guid.
    /// </summary>
    /// <param name="value">A nullable Guid.</param>
    /// <returns>The equivalent Guid.</returns>
    public static Guid ToGuid(Guid? value)
        => value.HasValue ? value.Value : Guid.Empty;

    /// <summary>
    ///     Converts the value of the specified memory buffer to its equivalent Guid.
    /// </summary>
    /// <param name="value">A memory buffer.</param>
    /// <returns>The equivalent Guid.</returns>
    public static Guid ToGuid(byte[] value)
        => value == null ? Guid.Empty : new(value);
    /// <summary>
    ///     Converts the value of the specified Type to its equivalent Guid.
    /// </summary>
    /// <param name="value">A Type.</param>
    /// <returns>The equivalent Guid.</returns>
    public static Guid ToGuid(Type value)
        => value == null ? Guid.Empty : value.GUID;
    /// <summary>
    ///     Converts the value of the specified Object to its equivalent Guid.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent Guid.</returns>
    public static Guid ToGuid(object value)
    {
        if (value == null || value is DBNull) return Guid.Empty;

        if (value is Guid) return (Guid)value;

        // Scalar Types.
        //
        if (value is string) return ToGuid((string)value);

        // Other Types.
        //
        if (value is byte[]) return ToGuid((byte[])value);
        if (value is Type) return ToGuid((Type)value);

        throw CreateInvalidCastException(value.GetType(), typeof(Guid));
    }

    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable
    ///     single-precision floating point number.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(float value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable single-precision floating point number.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(string value)
        => value == null ? null : float.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(long value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(ulong value)
        => value;
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable
    ///     single-precision floating point number.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(double value)
        => (float?)value;
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(decimal value)
        => (float?)value;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent nullable single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable single-precision floating point number.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(bool value)
        => value ? 1.0f : 0.0f;

    // Nullable Types.
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(sbyte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(short? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(int? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(long? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(ushort? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(uint? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(ulong? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     single-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(double? value)
        => value.HasValue ? (float?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(decimal? value)
        => value.HasValue ? (float?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(bool? value)
        => value.HasValue ? value.Value ? 1.0f : 0.0f : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable single-precision floating point number.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable single-precision floating point number.</returns>
    public static float? ToNullableSingle(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is float) return ToNullableSingle((float)value);
        if (value is string) return ToNullableSingle((string)value);

        if (value is char) return ToNullableSingle((char)value);
        if (value is bool) return ToNullableSingle((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToSingle(null);

        throw CreateInvalidCastException(value.GetType(), typeof(float?));
    }

    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable
    ///     double-precision floating point number.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(double value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable double-precision floating point number.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(string value)
        => value == null ? null : double.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(long value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(ulong value)
        => value;
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable
    ///     double-precision floating point number.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(float value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(decimal value)
        => (double?)value;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent nullable double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable double-precision floating point number.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(bool value)
        => value ? 1.0 : 0.0;
    public static double? ToNullableDouble(DateTime value)
        => (value - DateTime.MinValue).TotalDays;
    public static double? ToNullableDouble(TimeSpan value)
        => value.TotalDays;

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(sbyte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(short? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(int? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(long? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(ushort? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(uint? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable double-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(ulong? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     double-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(float? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(decimal? value)
        => value.HasValue ? (double?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable double-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable double-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(bool? value)
        => value.HasValue ? value.Value ? 1.0 : 0.0 : null;
    public static double? ToNullableDouble(DateTime? value)
        => value.HasValue ? (value.Value - DateTime.MinValue).TotalDays : null;
    public static double? ToNullableDouble(TimeSpan? value)
        => value.HasValue ? value.Value.TotalDays : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable double-precision floating point number.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable double-precision floating point number.</returns>
    public static double? ToNullableDouble(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is double) return ToNullableDouble((double)value);
        if (value is string) return ToNullableDouble((string)value);

        if (value is char) return ToNullableDouble((char)value);
        if (value is bool) return ToNullableDouble((bool)value);
        if (value is DateTime) return ToNullableDouble((DateTime)value);
        if (value is TimeSpan) return ToNullableDouble((TimeSpan)value);

        if (value is IConvertible) return ((IConvertible)value).ToDouble(null);

        throw CreateInvalidCastException(value.GetType(), typeof(double?));
    }

    /// <summary>
    ///     Converts the value of the specified Boolean value to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A Boolean value.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(bool value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(string value)
        => value == null ? null : bool.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(sbyte value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(short value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(int value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(long value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(byte value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(ushort value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(uint value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(ulong value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable Boolean
    ///     value.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(float value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable Boolean
    ///     value.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(double value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(decimal value)
        => ToBoolean(value);
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(char value)
        => ToBoolean(value);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(sbyte? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(short? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(int? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(long? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(byte? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(ushort? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(uint? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(ulong? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     Boolean value.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(float? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     Boolean value.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(double? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(decimal? value)
        => value.HasValue ? ToBoolean(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(char? value)
        => value.HasValue ? ToBoolean(value.Value) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable Boolean value.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable Boolean value.</returns>
    public static bool? ToNullableBoolean(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is bool) return ToNullableBoolean((bool)value);
        if (value is string) return ToNullableBoolean((string)value);

        if (value is char) return ToNullableBoolean((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToBoolean(null);

        throw CreateInvalidCastException(value.GetType(), typeof(bool?));
    }

    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(decimal value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(string value)
        => value == null ? null : decimal.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(long value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(ulong value)
        => value;
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable Decimal
    ///     number.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(float value)
        => (decimal?)value;
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable Decimal
    ///     number.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(double value)
        => (decimal?)value;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(bool value)
        => value ? 1.0m : 0.0m;

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(sbyte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(short? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(int? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(long? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(ushort? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(uint? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(ulong? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     Decimal number.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(float? value)
        => value.HasValue ? (decimal?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     Decimal number.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(double? value)
        => value.HasValue ? (decimal?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(bool? value)
        => value.HasValue ? value.Value ? 1.0m : 0.0m : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable Decimal number.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable Decimal number.</returns>
    public static decimal? ToNullableDecimal(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is double && double.IsNaN((double)value)) return null;
        if (value is decimal) return ToNullableDecimal((decimal)value);
        if (value is string) return ToNullableDecimal((string)value);

        if (value is char) return ToNullableDecimal((char)value);
        if (value is bool) return ToNullableDecimal((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToDecimal(null);

        throw CreateInvalidCastException(value.GetType(), typeof(decimal?));
    }

    /// <summary>
    ///     Converts the value of the specified DateTime to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">A DateTime.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(DateTime value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(string value)
        => value == null ? null : DateTime.Parse(value);
    /// <summary>
    ///     Converts the value of the specified TimeSpan to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">A TimeSpan.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(TimeSpan value)
        => DateTime.MinValue + value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(long value)
        => DateTime.MinValue + TimeSpan.FromTicks(value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(double value)
        => DateTime.MinValue + TimeSpan.FromDays(value);

    /// <summary>
    ///     Converts the value of the specified nullable TimeSpan to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">A nullable TimeSpan.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(TimeSpan? value)
        => value.HasValue ? DateTime.MinValue + value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer number to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(long? value)
        => value.HasValue ? DateTime.MinValue + TimeSpan.FromTicks(value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     DateTime.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(double? value)
        => value.HasValue ? DateTime.MinValue + TimeSpan.FromDays(value.Value) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable DateTime.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable DateTime.</returns>
    public static DateTime? ToNullableDateTime(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is DateTime) return ToNullableDateTime((DateTime)value);
        if (value is string) return ToNullableDateTime((string)value);

        if (value is TimeSpan) return ToNullableDateTime((TimeSpan)value);
        if (value is long) return ToNullableDateTime((long)value);
        if (value is double) return ToNullableDateTime((double)value);

        if (value is IConvertible) return ((IConvertible)value).ToDateTime(null);

        throw CreateInvalidCastException(value.GetType(), typeof(DateTime?));
    }

    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(string value)
        => value == null ? null : byte.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(sbyte value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(short value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(int value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(long value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(ushort value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(uint value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(ulong value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 8-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(float value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 8-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(double value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(decimal value)
        => (byte?)value;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(char value)
        => checked((byte?)value);
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(bool value)
        => (byte?)(value ? 1 : 0);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(sbyte? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(short? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(int? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(long? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(ushort? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(uint? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable 8-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(ulong? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(float? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(double? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(decimal? value)
        => value.HasValue ? (byte?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(char? value)
        => value.HasValue ? checked((byte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(bool? value)
        => value.HasValue ? (byte?)(value.Value ? 1 : 0) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 8-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 8-bit unsigned integer value.</returns>
    public static byte? ToNullableByte(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is byte) return ToNullableByte((byte)value);
        if (value is string) return ToNullableByte((string)value);

        if (value is char) return ToNullableByte((char)value);
        if (value is bool) return ToNullableByte((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToByte(null);

        throw CreateInvalidCastException(value.GetType(), typeof(byte?));
    }

    // Scalar Types.
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(string value)
        => value == null ? null : ushort.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(sbyte value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(short value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(int value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(long value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(uint value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(ulong value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 16-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(float value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 16-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(double value)
        => checked((ushort?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(decimal value)
        => (ushort?)value;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(bool value)
        => (ushort?)(value ? 1 : 0);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(sbyte? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(short? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(int? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(long? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(uint? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(ulong? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(float? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(double? value)
        => value.HasValue ? checked((ushort?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(decimal? value)
        => value.HasValue ? (ushort?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(bool? value)
        => value.HasValue ? (ushort?)(value.Value ? 1 : 0) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 16-bit unsigned integer value.</returns>
    public static ushort? ToNullableUInt16(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is ushort) return ToNullableUInt16((ushort)value);
        if (value is string) return ToNullableUInt16((string)value);

        if (value is char) return ToNullableUInt16((char)value);
        if (value is bool) return ToNullableUInt16((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToUInt16(null);

        throw CreateInvalidCastException(value.GetType(), typeof(ushort?));
    }

    // Scalar Types.
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(string value)
        => value == null ? null : uint.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(sbyte value)
        => checked((uint?)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(short value)
        => checked((uint?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(int value)
        => checked((uint?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(long value)
        => checked((uint?)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(ulong value)
        => checked((uint?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 32-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(float value)
        => checked((uint?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 32-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(double value)
        => checked((uint?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(decimal value)
        => (uint?)value;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(bool value)
        => (uint?)(value ? 1 : 0);

    // Nullable Types.
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(sbyte? value)
        => value.HasValue ? checked((uint?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(short? value)
        => value.HasValue ? checked((uint?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(int? value)
        => value.HasValue ? checked((uint?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(long? value)
        => value.HasValue ? checked((uint?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(ushort? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(ulong? value)
        => value.HasValue ? checked((uint?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(float? value)
        => value.HasValue ? checked((uint?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(double? value)
        => value.HasValue ? checked((uint?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(decimal? value)
        => value.HasValue ? (uint?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(bool? value)
        => value.HasValue ? (uint?)(value.Value ? 1 : 0) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 32-bit unsigned integer value.</returns>
    public static uint? ToNullableUInt32(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is uint) return ToNullableUInt32((uint)value);
        if (value is string) return ToNullableUInt32((string)value);

        if (value is char) return ToNullableUInt32((char)value);
        if (value is bool) return ToNullableUInt32((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToUInt32(null);

        throw CreateInvalidCastException(value.GetType(), typeof(uint?));
    }

    // Scalar Types.
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(ulong value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(string value)
        => value == null ? null : ulong.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(sbyte value)
        => checked((ulong?)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(short value)
        => checked((ulong?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(int value)
        => checked((ulong?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(long value)
        => checked((ulong?)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 64-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(float value)
        => checked((ulong?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 64-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(double value)
        => checked((ulong?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(decimal value)
        => (ulong?)value;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(bool value)
        => (ulong?)(value ? 1 : 0);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(sbyte? value)
        => value.HasValue ? checked((ulong?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(short? value)
        => value.HasValue ? checked((ulong?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(int? value)
        => value.HasValue ? checked((ulong?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(long? value)
        => value.HasValue ? checked((ulong?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(ushort? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(uint? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(float? value)
        => value.HasValue ? checked((ulong?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(double? value)
        => value.HasValue ? checked((ulong?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(decimal? value)
        => value.HasValue ? (ulong?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(bool? value)
        => value.HasValue ? (ulong?)(value.Value ? 1 : 0) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 64-bit unsigned integer value.</returns>
    public static ulong? ToNullableUInt64(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is ulong) return ToNullableUInt64((ulong)value);
        if (value is string) return ToNullableUInt64((string)value);

        if (value is char) return ToNullableUInt64((char)value);
        if (value is bool) return ToNullableUInt64((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToUInt64(null);

        throw CreateInvalidCastException(value.GetType(), typeof(ulong?));
    }

    // Scalar Types.
    /// <summary>
    ///     Converts a Unicode character to a nullable Unicode character.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent nullable Unicode character.</returns>
    public static char? ToNullableChar(char value)
        => value;
    /// <summary>
    ///     Converts the first character of the specified String to a nullable Unicode character.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable Unicode character.</returns>
    public static char? ToNullableChar(string value)
    {
        char result;
        if (char.TryParse(value, out result))
            return result;
        return (char)0;
    }
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(sbyte value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(short value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(int value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(long value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(byte value)
        => (char?)value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character.</returns>
    public static char? ToNullableChar(ushort value)
        => (char?)value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(uint value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(ulong value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable Unicode
    ///     character.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(float value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable Unicode
    ///     character.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(double value)
        => checked((char?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The nullable Unicode character value.</returns>
    public static char? ToNullableChar(decimal value)
        => (char?)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to a nullable Unicode character.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The nullable Unicode character value.</returns>
    public static char? ToNullableChar(bool value)
        => (char?)(value ? 1 : 0);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character.</returns>
    public static char? ToNullableChar(sbyte? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(short? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(int? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(long? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(byte? value)
        => value.HasValue ? (char?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(ushort? value)
        => value.HasValue ? (char?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(uint? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(ulong? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to a nullable Unicode
    ///     character.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The nullable Unicode character value.</returns>
    public static char? ToNullableChar(float? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to a nullable Unicode
    ///     character.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The nullable Unicode character value.</returns>
    public static char? ToNullableChar(double? value)
        => value.HasValue ? checked((char?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to a nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The nullable Unicode character value.</returns>
    public static char? ToNullableChar(decimal? value)
        => value.HasValue ? (char?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to a nullable Unicode character.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The nullable Unicode character value.</returns>
    public static char? ToNullableChar(bool? value)
        => value.HasValue ? (char?)(value.Value ? 1 : 0) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable Unicode character.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable Unicode character value.</returns>
    public static char? ToNullableChar(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is char) return ToNullableChar((char)value);
        if (value is string) return ToNullableChar((string)value);

        if (value is bool) return ToNullableChar((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToChar(null);

        throw CreateInvalidCastException(value.GetType(), typeof(char?));
    }

    /// <summary>
    ///     Converts the value of the specified Guid to its equivalent nullable Guid.
    /// </summary>
    /// <param name="value">A Guid.</param>
    /// <returns>The equivalent nullable Guid.</returns>
    public static Guid? ToNullableGuid(Guid value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable Guid.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable Guid.</returns>
    public static Guid? ToNullableGuid(string value)
        => value == null ? null : new Guid(value);

    /// <summary>
    ///     Converts the value of the specified Type to its equivalent nullable Guid.
    /// </summary>
    /// <param name="value">A Type.</param>
    /// <returns>The equivalent nullable Guid.</returns>
    public static Guid? ToNullableGuid(Type value)
        => value == null ? null : value.GUID;
    /// <summary>
    ///     Converts the value of the specified memory buffer to its equivalent nullable Guid.
    /// </summary>
    /// <param name="value">A memory buffer.</param>
    /// <returns>The equivalent nullable Guid.</returns>
    public static Guid? ToNullableGuid(byte[] value)
        => value == null ? null : new Guid(value);
    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable Guid.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable Guid.</returns>
    public static Guid? ToNullableGuid(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is Guid) return ToNullableGuid((Guid)value);
        if (value is string) return ToNullableGuid((string)value);

        // Other Types.
        //
        if (value is Type) return ToNullableGuid((Type)value);
        if (value is byte[]) return ToNullableGuid((byte[])value);

        throw CreateInvalidCastException(value.GetType(), typeof(Guid?));
    }

    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(string value)
        => value == null ? null : short.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(int value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(long value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(ushort value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(uint value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(ulong value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(float value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(double value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(decimal value)
        => (short?)value;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(char value)
        => checked((short?)value);
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(bool value)
        => (short?)(value ? 1 : 0);

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(sbyte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(int? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(long? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(ushort? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(uint? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable 16-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(ulong? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(float? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(double? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(decimal? value)
        => value.HasValue ? (short?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(char? value)
        => value.HasValue ? checked((short?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(bool? value)
        => value.HasValue ? (short?)(value.Value ? 1 : 0) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 16-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 16-bit signed integer value.</returns>
    public static short? ToNullableInt16(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is short) return ToNullableInt16((short)value);
        if (value is string) return ToNullableInt16((string)value);

        if (value is char) return ToNullableInt16((char)value);
        if (value is bool) return ToNullableInt16((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToInt16(null);

        throw CreateInvalidCastException(value.GetType(), typeof(short?));
    }

    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(string value)
        => value == null ? null : int.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(long value)
        => checked((int?)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(uint value)
        => checked((int?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(ulong value)
        => checked((int?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(float value)
        => checked((int?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(double value)
        => checked((int?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(decimal value)
        => (int?)value;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(bool value)
        => value ? 1 : 0;

    // Nullable Types.
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(sbyte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(short? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(long? value)
        => value.HasValue ? checked((int?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(ushort? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(uint? value)
        => value.HasValue ? checked((int?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable 32-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(ulong? value)
        => value.HasValue ? checked((int?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(float? value)
        => value.HasValue ? checked((int?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(double? value)
        => value.HasValue ? checked((int?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(decimal? value)
        => value.HasValue ? (int?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(bool? value)
        => value.HasValue ? value.Value ? 1 : 0 : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 32-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 32-bit signed integer value.</returns>
    public static int? ToNullableInt32(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is int) return ToNullableInt32((int)value);
        if (value is string) return ToNullableInt32((string)value);

        if (value is char) return ToNullableInt32((char)value);
        if (value is bool) return ToNullableInt32((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToInt32(null);

        throw CreateInvalidCastException(value.GetType(), typeof(int?));
    }

    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(long value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(string value)
        => value == null ? null : long.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(ulong value)
        => checked((long?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(float value)
        => checked((long?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(double value)
        => checked((long?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(decimal value)
        => (long?)value;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(char value)
        => value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(bool value)
        => value ? 1 : 0;
    /// <summary>
    ///     Converts the value of the specified DateTime to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A DateTime.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(DateTime value)
        => (value - DateTime.MinValue).Ticks;
    /// <summary>
    ///     Converts the value of the specified TimeSpan to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A TimeSpan.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(TimeSpan value)
        => value.Ticks;

    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(sbyte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(short? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(int? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(byte? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(ushort? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(uint? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable 64-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(ulong? value)
        => value.HasValue ? checked((long?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(float? value)
        => value.HasValue ? checked((long?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(double? value)
        => value.HasValue ? checked((long?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(decimal? value)
        => value.HasValue ? (long?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(char? value)
        => value.HasValue ? value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(bool? value)
        => value.HasValue ? value.Value ? 1 : 0 : null;
    /// <summary>
    ///     Converts the value of the specified nullable DateTime to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable DateTime.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(DateTime? value)
        => value.HasValue ? (value.Value - DateTime.MinValue).Ticks : null;
    /// <summary>
    ///     Converts the value of the specified nullable TimeSpan to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable TimeSpan.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(TimeSpan? value)
        => value.HasValue ? value.Value.Ticks : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 64-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 64-bit signed integer value.</returns>
    public static long? ToNullableInt64(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        //
        if (value is long) return ToNullableInt64((long)value);
        if (value is string) return ToNullableInt64((string)value);

        if (value is char) return ToNullableInt64((char)value);
        if (value is bool) return ToNullableInt64((bool)value);
        if (value is DateTime) return ToNullableInt64((DateTime)value);
        if (value is TimeSpan) return ToNullableInt64((TimeSpan)value);

        if (value is IConvertible) return ((IConvertible)value).ToInt64(null);

        throw CreateInvalidCastException(value.GetType(), typeof(long?));
    }

    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified String to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(string value)
        => value == null ? null : sbyte.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(short value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(int value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(long value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(byte value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">An 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(ushort value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">An 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(uint value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(ulong value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent nullable 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(float value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent nullable 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(double value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(decimal value)
        => (sbyte?)value;
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(char value)
        => checked((sbyte?)value);
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(bool value)
        => (sbyte?)(value ? 1 : 0);

    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(short? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(int? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(long? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent nullable 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(byte? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent nullable 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(ushort? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent nullable 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(uint? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent nullable 8-bit signed
    ///     integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(ulong? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent nullable
    ///     8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(float? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent nullable
    ///     8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(double? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(decimal? value)
        => value.HasValue ? (sbyte?)value.Value : null;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(char? value)
        => value.HasValue ? checked((sbyte?)value.Value) : null;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(bool? value)
        => value.HasValue ? (sbyte?)(value.Value ? 1 : 0) : null;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent nullable 8-bit signed integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent nullable 8-bit signed integer value.</returns>
    public static sbyte? ToNullableSByte(object value)
    {
        if (value == null || value is DBNull) return null;

        // Scalar Types.
        if (value is sbyte) return ToNullableSByte((sbyte)value);
        if (value is string) return ToNullableSByte((string)value);

        if (value is char) return ToNullableSByte((char)value);
        if (value is bool) return ToNullableSByte((bool)value);

        if (value is IConvertible) return ((IConvertible)value).ToSByte(null);

        throw CreateInvalidCastException(value.GetType(), typeof(sbyte?));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(string value)
        => value == null ? 0.0f : float.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(sbyte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(short value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(int value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(long value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(ulong value)
        => value;
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent single-precision
    ///     floating point number.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(double value)
        => (float)value;
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(decimal value)
        => (float)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(bool value)
        => value ? 1.0f : 0.0f;
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(char value)
        => value;

    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent
    ///     single-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(float? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(sbyte? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(short? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(int? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(long? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(byte? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(ushort? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(uint? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent single-precision floating
    ///     point number.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(ulong? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent
    ///     single-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(double? value)
        => value.HasValue ? (float)value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(decimal? value)
        => value.HasValue ? (float)value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent single-precision floating point
    ///     number.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(char? value)
        => value.HasValue ? value.Value : 0.0f;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(bool? value)
        => value.HasValue && value.Value ? 1.0f : 0.0f;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent single-precision floating point number.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent single-precision floating point number.</returns>
    public static float ToSingle(object value)
    {
        if (value == null || value is DBNull) return 0.0f;

        if (value is float) return (float)value;

        // Scalar Types.
        //
        if (value is string) return ToSingle((string)value);

        if (value is bool) return ToSingle((bool)value);
        if (value is char) return ToSingle((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToSingle(null);

        throw CreateInvalidCastException(value.GetType(), typeof(float));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(string value)
        => value == null ? TimeSpan.MinValue : TimeSpan.Parse(value);
    /// <summary>
    ///     Converts the value of the specified DateTime to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A DateTime.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(DateTime value)
        => value - DateTime.MinValue;
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(long value)
        => TimeSpan.FromTicks(value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(double value)
        => TimeSpan.FromDays(value);

    /// <summary>
    ///     Converts the value of the specified nullable TimeSpan to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A nullable TimeSpan.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(TimeSpan? value)
        => value.HasValue ? value.Value : TimeSpan.MinValue;
    /// <summary>
    ///     Converts the value of the specified nullable DateTime to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A nullable DateTime.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(DateTime? value)
        => value.HasValue ? value.Value - DateTime.MinValue : TimeSpan.MinValue;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer number to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(long? value)
        => value.HasValue ? TimeSpan.FromTicks(value.Value) : TimeSpan.MinValue;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(double? value)
        => value.HasValue ? TimeSpan.FromDays(value.Value) : TimeSpan.MinValue;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent TimeSpan.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent TimeSpan.</returns>
    public static TimeSpan ToTimeSpan(object value)
    {
        if (value == null || value is DBNull) return TimeSpan.MinValue;

        if (value is TimeSpan) return (TimeSpan)value;

        // Scalar Types.
        //
        if (value is string) return ToTimeSpan((string)value);
        if (value is DateTime) return ToTimeSpan((DateTime)value);
        if (value is long) return ToTimeSpan((long)value);
        if (value is double) return ToTimeSpan((double)value);

        throw CreateInvalidCastException(value.GetType(), typeof(TimeSpan));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(string value)
        => value == null ? (ushort)0 : ushort.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(sbyte value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(short value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(int value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(long value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(uint value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(ulong value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(float value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 16-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(double value)
        => checked((ushort)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(decimal value)
        => (ushort)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(bool value)
        => (ushort)(value ? 1 : 0);
    /// <summary>
    ///     Converts the value of the specified Unicode character to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Unicode character.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(char value)
        => value;

    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(ushort? value)
        => value.HasValue ? value.Value : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(sbyte? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(short? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(int? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(long? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(byte? value)
        => value.HasValue ? value.Value : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(uint? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(ulong? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 16-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(float? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 16-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(double? value)
        => value.HasValue ? checked((ushort)value.Value) : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(decimal? value)
        => value.HasValue ? (ushort)value.Value : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(char? value)
        => value.HasValue ? value.Value : (ushort)0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(bool? value)
        => value.HasValue && value.Value ? (ushort)1 : (ushort)0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 16-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 16-bit unsigned integer value.</returns>
    public static ushort ToUInt16(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is ushort) return (ushort)value;

        // Scalar Types.
        //
        if (value is string) return ToUInt16((string)value);

        if (value is bool) return ToUInt16((bool)value);
        if (value is char) return ToUInt16((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToUInt16(null);

        throw CreateInvalidCastException(value.GetType(), typeof(ushort));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(string value)
        => value == null ? 0 : uint.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(sbyte value)
        => checked((uint)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(short value)
        => checked((uint)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(int value)
        => checked((uint)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(long value)
        => checked((uint)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 64-bit unsigned integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(ulong value)
        => checked((uint)value);
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(float value)
        => checked((uint)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 32-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(double value)
        => checked((uint)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(decimal value)
        => (uint)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(bool value)
        => (uint)(value ? 1 : 0);
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(char value)
        => value;

    /// <summary>
    ///     Converts the value of the specified nullable 32-bit unsigned integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(uint? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(sbyte? value)
        => value.HasValue ? checked((uint)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(short? value)
        => value.HasValue ? checked((uint)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(int? value)
        => value.HasValue ? checked((uint)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(long? value)
        => value.HasValue ? checked((uint)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(byte? value)
        => value.HasValue ? (uint)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(ushort? value)
        => value.HasValue ? (uint)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit unsigned integer.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(ulong? value)
        => value.HasValue ? checked((uint)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 32-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(float? value)
        => value.HasValue ? checked((uint)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 32-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(double? value)
        => value.HasValue ? checked((uint)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(decimal? value)
        => value.HasValue ? (uint)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(char? value)
        => value.HasValue ? (uint)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(bool? value)
        => value.HasValue && value.Value ? (uint)1 : 0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 32-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 32-bit unsigned integer value.</returns>
    public static uint ToUInt32(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is uint) return (uint)value;

        // Scalar Types.
        //
        if (value is string) return ToUInt32((string)value);

        if (value is bool) return ToUInt32((bool)value);
        if (value is char) return ToUInt32((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToUInt32(null);

        throw CreateInvalidCastException(value.GetType(), typeof(uint));
    }

    /// <summary>
    ///     Converts the value of the specified String to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A String.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(string value)
        => value == null ? 0 : ulong.Parse(value);
    /// <summary>
    ///     Converts the value of the specified 8-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(sbyte value)
        => checked((ulong)value);
    /// <summary>
    ///     Converts the value of the specified 16-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(short value)
        => checked((ulong)value);
    /// <summary>
    ///     Converts the value of the specified 32-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(int value)
        => checked((ulong)value);
    /// <summary>
    ///     Converts the value of the specified 64-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 64-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(long value)
        => checked((ulong)value);
    /// <summary>
    ///     Converts the value of the specified 8-bit unsigned integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An 8-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(byte value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 16-bit unsigned integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 16-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(ushort value)
        => value;
    /// <summary>
    ///     Converts the value of the specified 32-bit unsigned integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A 32-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(uint value)
        => value;
    /// <summary>
    ///     Converts the value of the specified single-precision floating point number to its equivalent 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A single-precision floating point number.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(float value)
        => checked((ulong)value);
    /// <summary>
    ///     Converts the value of the specified double-precision floating point number to its equivalent 64-bit unsigned
    ///     integer.
    /// </summary>
    /// <param name="value">A double-precision floating point number.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(double value)
        => checked((ulong)value);
    /// <summary>
    ///     Converts the value of the specified Decimal number to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Decimal number.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(decimal value)
        => (ulong)value;
    /// <summary>
    ///     Converts the value of the specified Boolean to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A Boolean.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(bool value)
        => (ulong)(value ? 1 : 0);
    /// <summary>
    ///     Converts the value of the specified Unsigned character to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Unsigned character.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(char value)
        => value;

    /// <summary>
    ///     Converts the value of the specified nullable 64-bit unsigned integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(ulong? value)
        => value.HasValue ? value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(sbyte? value)
        => value.HasValue ? checked((ulong)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(short? value)
        => value.HasValue ? checked((ulong)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 32-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 32-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(int? value)
        => value.HasValue ? checked((ulong)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 64-bit signed integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 64-bit signed integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(long? value)
        => value.HasValue ? checked((ulong)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(byte? value)
        => value.HasValue ? (ulong)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 8-bit unsigned integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 8-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(ushort? value)
        => value.HasValue ? (ulong)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable 16-bit unsigned integer to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable 16-bit unsigned integer.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(uint? value)
        => value.HasValue ? (ulong)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable single-precision floating point number to its equivalent 64-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable single-precision floating point number.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(float? value)
        => value.HasValue ? checked((ulong)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable double-precision floating point number to its equivalent 64-bit
    ///     unsigned integer.
    /// </summary>
    /// <param name="value">A nullable double-precision floating point number.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(double? value)
        => value.HasValue ? checked((ulong)value.Value) : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Decimal number to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Decimal number.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(decimal? value)
        => value.HasValue ? (ulong)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Unicode character to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Unicode character.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(char? value)
        => value.HasValue ? (ulong)value.Value : 0;
    /// <summary>
    ///     Converts the value of the specified nullable Boolean to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">A nullable Boolean.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(bool? value)
        => value.HasValue && value.Value ? (ulong)1 : 0;

    /// <summary>
    ///     Converts the value of the specified Object to its equivalent 64-bit unsigned integer.
    /// </summary>
    /// <param name="value">An Object.</param>
    /// <returns>The equivalent 64-bit unsigned integer value.</returns>
    public static ulong ToUInt64(object value)
    {
        if (value == null || value is DBNull) return 0;

        if (value is ulong) return (ulong)value;

        // Scalar Types.
        //
        if (value is string) return ToUInt64((string)value);

        if (value is bool) return ToUInt64((bool)value);
        if (value is char) return ToUInt64((char)value);

        if (value is IConvertible) return ((IConvertible)value).ToUInt64(null);

        throw CreateInvalidCastException(value.GetType(), typeof(ulong));
    }

    // Scalar Types.
    public static XmlDocument ToXmlDocument(string p)
    {
        if (p == null) return null;

        var doc = new XmlDocument();
        doc.LoadXml(p);
        return doc;
    }

    // Other Types.
    // 
    public static XmlDocument ToXmlDocument(Stream p)
    {
        if (p == null) return null;

        var doc = new XmlDocument();
        doc.Load(p);
        return doc;
    }

    public static XmlDocument ToXmlDocument(TextReader p)
    {
        if (p == null) return null;

        var doc = new XmlDocument();
        doc.Load(p);
        return doc;
    }

    public static XmlDocument ToXmlDocument(XDocument p)
    {
        if (p == null) return null;

        var doc = new XmlDocument();
        doc.Load(p.ToString());
        return doc;
    }

    public static XmlDocument ToXmlDocument(XElement p)
    {
        if (p == null) return null;

        var doc = new XmlDocument();
        doc.Load(p.ToString());
        return doc;
    }

    public static XmlDocument ToXmlDocument(char[] p)
        => p == null ? null : ToXmlDocument(new string(p));
    public static XmlDocument ToXmlDocument(byte[] p)
        => p == null ? null : ToXmlDocument(new MemoryStream(p));

    public static XmlDocument ToXmlDocument(XmlReader p)
    {
        if (p == null) return null;

        var doc = new XmlDocument();
        doc.Load(p);
        return doc;
    }

    public static XmlDocument ToXmlDocument(object p)
    {
        if (p == null || p is DBNull) return null;

        if (p is XmlDocument) return (XmlDocument)p;

        // Scalar Types.
        //
        if (p is string) return ToXmlDocument((string)p);

        // Other Types.
        //
        if (p is Stream) return ToXmlDocument((Stream)p);
        if (p is TextReader) return ToXmlDocument((TextReader)p);
        if (p is XmlReader) return ToXmlDocument((XmlReader)p);

        if (p is char[]) return ToXmlDocument((char[])p);
        if (p is byte[]) return ToXmlDocument((byte[])p);

        if (p is XDocument) return ToXmlDocument((XDocument)p);
        if (p is XElement) return ToXmlDocument((XElement)p);

        throw CreateInvalidCastException(p.GetType(), typeof(XmlDocument));
    }

    // Scalar Types.
    public static XDocument ToXDocument(string p)
    {
        if (p == null) return null;

        var doc = XDocument.Parse(p);
        return doc;
    }

    public static XDocument ToXDocument(XmlDocument p)
    {
        if (p == null) return null;

        var doc = XDocument.Parse(p.OuterXml);
        return doc;
    }

    public static XDocument ToXDocument(object p)
    {
        if (p == null || p is DBNull) return null;

        if (p is XDocument) return (XDocument)p;

        // Scalar Types.
        //
        if (p is string) return ToXDocument((string)p);

        if (p is XmlDocument) return ToXDocument((XmlDocument)p);

        throw CreateInvalidCastException(p.GetType(), typeof(XDocument));
    }

    // Scalar Types.
    public static XElement ToXElement(string p)
    {
        if (p == null) return null;

        var element = XElement.Parse(p);
        return element;
    }

    public static XElement ToXElement(XmlDocument p)
    {
        if (p == null) return null;

        var element = XElement.Parse(p.OuterXml);
        return element;
    }

    public static XElement ToXElement(object p)
    {
        if (p == null || p is DBNull) return null;

        if (p is XElement) return (XElement)p;

        // Scalar Types.
        //
        if (p is string) return ToXElement((string)p);

        if (p is XmlDocument) return ToXElement((XmlDocument)p);

        throw CreateInvalidCastException(p.GetType(), typeof(XElement));
    }

    // Scalar Types.
    // 
    public static XmlReader ToXmlReader(string p)
        => p == null ? null : new XmlTextReader(new StringReader(p));

    // Other Types.
    // 
    public static XmlReader ToXmlReader(Stream p)
        => p == null ? null : new XmlTextReader(p);
    public static XmlReader ToXmlReader(TextReader p)
        => p == null ? null : new XmlTextReader(p);

    public static XmlReader ToXmlReader(XmlDocument p)
        => p == null ? null : new XmlTextReader(new StringReader(p.InnerXml));

    public static XmlReader ToXmlReader(char[] p)
        => p == null ? null : new XmlTextReader(new StringReader(new(p)));
    public static XmlReader ToXmlReader(byte[] p)
        => p == null ? null : new XmlTextReader(new MemoryStream(p));

    public static XmlReader ToXmlReader(object p)
    {
        if (p == null || p is DBNull) return null;

        if (p is XmlReader) return (XmlReader)p;

        // Scalar Types.
        //
        if (p is string) return ToXmlReader((string)p);

        // Other Types.
        //
        if (p is Stream) return ToXmlReader((Stream)p);
        if (p is TextReader) return ToXmlReader((TextReader)p);
        if (p is XmlDocument) return ToXmlReader((XmlDocument)p);

        if (p is char[]) return ToXmlReader((char[])p);
        if (p is byte[]) return ToXmlReader((byte[])p);

        throw CreateInvalidCastException(p.GetType(), typeof(XmlReader));
    }

    private static InvalidCastException CreateInvalidCastException(Type originalType, Type conversionType)
        => new(string.Format("Invalid cast from {0} to {1}", originalType.FullName, conversionType.FullName));
}