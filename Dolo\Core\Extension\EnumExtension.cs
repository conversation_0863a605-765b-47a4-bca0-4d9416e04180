namespace Dolo.Core.Extension;

public static class EnumExtension
{

    /// <summary>
    ///     Get the value of the enum
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public static int GetValue(this Enum value) => System.Convert.ToInt32(value);

    /// <summary>
    ///     Get a random enum
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static T GetRandom<T>() => Enum.GetValues(typeof(T)).Cast<T>().ToList().Shuffle().First();

    /// <summary>
    ///     Get the enum name
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public static string? GetName(this Enum value) => Enum.GetName(value.GetType(), value);
}