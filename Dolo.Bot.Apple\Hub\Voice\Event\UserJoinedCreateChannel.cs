﻿using Dolo.Database;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Bot.Apple;

#pragma warning disable CS0618
namespace Dolo.Bot.Apple.Hub.Voice.Event;

public static class UserJoinedCreateChannel
{
    // will be triggered when the user joined the creation channel
    public static async Task InvokeAsync(VoiceArgs e)
    {
        Console.WriteLine($"[Voice Debug] UserJoinedCreateChannel triggered for user {e.User?.Username} ({e.User?.Id})");

        if (e.Channel is null
            || e.Guild is null
            || e.User is null)
        {
            Console.WriteLine("[Voice Debug] Early return: Channel, Guild, or User is null");
            return;
        }

        // return if member not exist or voice channel is null
        if (HubChannel.Voice is null
            || Hub.Guild is null
            || !e.Guild.TryGetMember(e.User.Id, out var member))
        {
            Console.WriteLine("[Voice Debug] Early return: HubChannel.Voice is null, Hub.Guild is null, or member not found");
            return;
        }

        // try to get the database voice channel
        var users = await Mongo.Voice.GetOneAsync(a => a.Owner == e.User.Id);
        Console.WriteLine($"[Voice Debug] Existing channel check: {(users != null ? "Found existing channel" : "No existing channel")}");

        // if the channel exist than place the user in the channel
        if (users != null)
        {
            Console.WriteLine($"[Voice Debug] User has existing channel {users.Channel}, attempting to place user");
            // get the channel from the guild
            var channel = e.Guild.TryGetChannel(users.Channel);

            // if null then kick the member
            if (channel is null)
            {
                Console.WriteLine($"[Voice Debug] Existing channel {users.Channel} not found, cleaning up and kicking user");
                await member.TryModifyAsync(a => a.VoiceChannel = null);
                await Mongo.Voice.DeleteAsync(a => a.Owner == e.User.Id);
                return;
            }

            Console.WriteLine($"[Voice Debug] Placing user in existing channel {channel.Name}");
            // place member into channel again
            await channel.TryPlaceMemberAsync(member);
            return;
        }

        // check rate limiting for new channel creation
        Console.WriteLine($"[Voice Debug] Checking rate limiting for user {e.User.Id}");
        if (!VoiceUtilities.CanCreateChannel(e.User.Id))
        {
            var cooldown = VoiceUtilities.GetChannelCreationCooldown(e.User.Id);
            Console.WriteLine($"[Voice Debug] User {e.User.Username} ({e.User.Id}) hit rate limit. Cooldown: {cooldown.TotalSeconds:F0}s - KICKING USER");
            await member.TryModifyAsync(a => a.VoiceChannel = null);
            return;
        }
        Console.WriteLine($"[Voice Debug] Rate limiting passed, proceeding with channel creation");

        // create the voice channel
        var vc = await Hub.Guild.TryCreateVoiceChannelAsync($"🌸 » {e.User.Username}", HubChannel.VoiceTopic);

        // check if the channel is created if not kick the user
        if (vc is null)
        {
            Console.WriteLine($"[Voice] Failed to create voice channel for user {e.User.Username} ({e.User.Id})");
            await member.TryModifyAsync(a => a.VoiceChannel = null);
            return;
        }

        // add the user to the channel
        try
        {
            await vc.TryPlaceMemberAsync(member);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Voice] Failed to place user {e.User.Username} ({e.User.Id}) in their voice channel: {ex.Message}");
        }

        // create a text channel with permissions
        var tc = await Hub.Guild.TryCreateTextChannelAsync("\ud83c\udf67\u232fchat", HubChannel.VoiceTopic, $"👑 **Channel-Owner »** <@{e.User.Id}>",
                 new List<DiscordOverwriteBuilder>
                 {
                     new DiscordOverwriteBuilder(member)
                         .Allow(DiscordPermission.ViewChannel)
                         .Allow(DiscordPermission.ReadMessageHistory),
                     new DiscordOverwriteBuilder(Hub.Guild.EveryoneRole)
                         .Deny(DiscordPermission.ViewChannel)
                         .Deny(DiscordPermission.ReadMessageHistory)
                 }, null, null, 0);

        if (tc is null)
        {
            Console.WriteLine($"[Voice] Failed to create text channel for user {e.User.Username} ({e.User.Id})");
        }
        else
        {
            try
            {
                await tc.TrySendMessageAsync(HubEmbed.Voice(e.User));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[Voice] Failed to send welcome message in text channel for user {e.User.Username} ({e.User.Id}): {ex.Message}");
            }
        }

        // add into database
        try
        {
            await Mongo.Voice.AddAsync(new VoiceUser
            {
                Owner = e.User.Id,
                Channel = vc.Id,
                TextChannel = tc?.Id ?? 0
            });

            // record the channel creation for rate limiting
            VoiceUtilities.RecordChannelCreation(e.User.Id);
            Console.WriteLine($"[Voice] Successfully created voice system for user {e.User.Username} ({e.User.Id})");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Voice] Failed to save voice channel to database for user {e.User.Username} ({e.User.Id}): {ex.Message}");

            // cleanup channels if database save failed
            await vc.TryDeleteAsync();
            if (tc != null)
                await tc.TryDeleteAsync();
        }
    }
}
