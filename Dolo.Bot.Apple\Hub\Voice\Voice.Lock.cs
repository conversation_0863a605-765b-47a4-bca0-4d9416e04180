﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("lock")]
    [Description("lock the voice channel")]
    public async Task VoiceLockAsync(SlashCommandContext ctx)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
            return;
        }

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // try to get the channel 
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
            return;

        // check if the limit is the same
        var perm = channel.PermissionOverwrites.SingleOrDefault(a => a.Id == Hub.Guild.EveryoneRole.Id);
        if (perm != null && perm.Denied.HasPermission(DiscordPermission.Connect))
        {
            await ctx.TryEditResponseAsync("This channel is already locked.");
            return;
        }

        // check if the user is allowed to perform the command
        if (usr.Owner != ctx.User.Id && !usr.Moderator.Contains(ctx.User.Id))
        {
            await ctx.TryEditResponseAsync("You are not a channel moderator.");
            return;
        }

        // modify the channel limit
        await channel.TryAddOverrideAsync(HubRoles.Everyone, default, DiscordPermission.Connect);

        // send the message
        await ctx.TryEditResponseAsync("The channel has been locked.");
    }
}