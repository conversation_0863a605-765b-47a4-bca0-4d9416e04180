﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Contains the properties for configuring Flex factories.
///     This is the <b>factories</b> element in the services-config.xml file.
///     Flex factories are global, a single FlexFactory instance is created for each Flex-enabled web application.
/// </summary>
internal sealed class FactorySettings : Hashtable
{
    private FactorySettings()
    {}

    internal FactorySettings(XmlNode factoryDefinitionNode)
    {
        Id = factoryDefinitionNode.Attributes["id"].Value;
        ClassId = factoryDefinitionNode.Attributes["class"].Value;

        var propertiesNode = factoryDefinitionNode.SelectSingleNode("properties");
        if (propertiesNode != null)
            foreach (XmlNode propertyNode in propertiesNode.SelectNodes("*"))
                this[propertyNode.Name] = propertyNode.InnerXml;
    }

    /// <summary>
    ///     Gets or sets the identity of the factory.
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     Gets the IFlexFactory type.
    /// </summary>
    public string ClassId { get; set; }
}

/// <summary>
///     Strongly typed FactorySettings collection.
/// </summary>
internal sealed class FactorySettingsCollection : CollectionBase
{
    /// <summary>
    ///     Initializes a new instance of the FactorySettingsCollection class.
    /// </summary>
    public FactorySettingsCollection()
    {}

    /// <summary>
    ///     Gets or sets the FactorySettings element at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the element to get or set.</param>
    /// <returns>The element at the specified index.</returns>
    public FactorySettings this[int index]
    {
        get => List[index] as FactorySettings;
        set => List[index] = value;
    }

    /// <summary>
    ///     Adds a FactorySettings to the collection.
    /// </summary>
    /// <param name="value">The FactorySettings to add to the collection.</param>
    /// <returns>The position into which the new element was inserted.</returns>
    public int Add(FactorySettings value) => List.Add(value);

    /// <summary>
    ///     Determines the index of a specific item in the collection.
    /// </summary>
    /// <param name="value">The FactorySettings to locate in the collection.</param>
    /// <returns>The index of value if found in the collection; otherwise, -1.</returns>
    public int IndexOf(FactorySettings value) => List.IndexOf(value);

    /// <summary>
    ///     Inserts a FactorySettings item to the collection at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index at which value should be inserted.</param>
    /// <param name="value">The FactorySettings to insert into the collection.</param>
    public void Insert(int index, FactorySettings value)
    {
        List.Insert(index, value);
    }

    /// <summary>
    ///     Removes the first occurrence of a specific FactorySettings from the collection.
    /// </summary>
    /// <param name="value">The FactorySettings to remove from the collection.</param>
    public void Remove(FactorySettings value)
    {
        List.Remove(value);
    }

    /// <summary>
    ///     Determines whether the collection contains a specific FactorySettings value.
    /// </summary>
    /// <param name="value">The FactorySettings to locate in the collection.</param>
    /// <returns>true if the FactorySettings is found in the collection; otherwise, false.</returns>
    public bool Contains(FactorySettings value) => List.Contains(value);
}