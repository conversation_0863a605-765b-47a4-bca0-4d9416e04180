﻿namespace Dolo.Core.Flash;

public class SharedObject
{
    public readonly List<SoValue> Values = new();

    public SoValue? Get(string keyword) => Values.SingleOrDefault(a => a.Key == keyword);
    public string GetString(string keyword) => Get(keyword).GetValueOrDefault().StringVal;
    public bool GetBoolean(string keyword) => Get(keyword).GetValueOrDefault().BoolVal;
    public int GetInt(string keyword) => Get(keyword).GetValueOrDefault().IntVal;
}