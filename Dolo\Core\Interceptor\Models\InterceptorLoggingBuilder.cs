using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Custom logging builder for the HTTPS interceptor with specific logging categories
/// </summary>
public class InterceptorLoggingBuilder
{
    private readonly ILoggingBuilder _builder;
    private bool _consoleAdded = false;    internal InterceptorLoggingBuilder(ILoggingBuilder builder)
    {
        _builder = builder;
        _builder.AddConsole(); // Default console logging
        _consoleAdded = true;

        // Block all interceptor categories by default - they must be explicitly enabled
        _builder.AddFilter("Dolo.Core.Interceptor.Services.TunnelingService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpRequestHandler", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.SslConnectionHandler", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpDataRelayService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.BreakpointManager", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.ConnectionManager", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.RequestResponsePairingEngine", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.AmfService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.AmfModificationService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Tunneling", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Certificate", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.CertificateService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Failure", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.ResponseDecodingService", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Ssl", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Transaction", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpTransactionManager", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Main", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.HttpsInterceptor", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.DataRelay", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Breakpoint", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Connection", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Pairing", LogLevel.None);
        _builder.AddFilter("Dolo.Core.Interceptor.Amf", LogLevel.None);
    }

    /// <summary>
    /// Adds console logging (enabled by default)
    /// </summary>
    public InterceptorLoggingBuilder AddConsole()
    {
        if (!_consoleAdded)
        {
            _builder.AddConsole();
            _consoleAdded = true;
        }
        return this;
    }    /// <summary>
    /// Enables logging for tunneling requests and data relay operations
    /// </summary>
    public InterceptorLoggingBuilder AddTunneling()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.TunnelingService", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpRequestHandler", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Tunneling", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Enables logging for data relay operations and HTTP processing
    /// </summary>
    public InterceptorLoggingBuilder AddDataRelay() {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpDataRelayService", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.DataRelay", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Enables logging for breakpoint operations and debugging
    /// </summary>
    public InterceptorLoggingBuilder AddBreakpoint() {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.BreakpointManager", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Breakpoint", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Enables logging for connection management operations
    /// </summary>
    public InterceptorLoggingBuilder AddConnection() {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.ConnectionManager", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Connection", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Enables logging for request-response pairing operations
    /// </summary>
    public InterceptorLoggingBuilder AddPairing() {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.RequestResponsePairingEngine", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Pairing", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Enables logging for AMF processing and modification operations
    /// </summary>
    public InterceptorLoggingBuilder AddAmf() {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.AmfService", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.AmfModificationService", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Amf", LogLevel.Information);
        return this;
    }    /// <summary>
    /// Enables logging for certificate generation and validation operations
    /// </summary>
    public InterceptorLoggingBuilder AddCertificate()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Certificate", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.CertificateService", LogLevel.Information);
        return this;
    }/// <summary>
    /// Enables logging for all failures and exceptions
    /// </summary>
    public InterceptorLoggingBuilder AddFailure()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Failure", LogLevel.Warning);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.ResponseDecodingService", LogLevel.Warning);
        return this;
    }    /// <summary>
    /// Enables logging for SSL handshake operations
    /// </summary>
    public InterceptorLoggingBuilder AddSsl()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Services.SslConnectionHandler", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Ssl", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Enables logging for HTTP transaction processing
    /// </summary>
    public InterceptorLoggingBuilder AddTransaction()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Transaction", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.Services.HttpTransactionManager", LogLevel.Information);
        return this;
    }/// <summary>
    /// Enables all logging categories with detailed information
    /// </summary>
    public InterceptorLoggingBuilder AddAll()
    {
        return AddTunneling()
               .AddDataRelay()
               .AddBreakpoint()
               .AddConnection()
               .AddPairing()
               .AddAmf()
               .AddCertificate()
               .AddFailure()
               .AddSsl()
               .AddTransaction()
               .AddMain();
    }    /// <summary>
    /// Enables logging for main interceptor operations (connection handling, startup/shutdown)
    /// </summary>
    public InterceptorLoggingBuilder AddMain()
    {
        _builder.AddFilter("Dolo.Core.Interceptor.Main", LogLevel.Information);
        _builder.AddFilter("Dolo.Core.Interceptor.HttpsInterceptor", LogLevel.Information);
        return this;
    }

    /// <summary>
    /// Sets the minimum log level for all categories
    /// </summary>
    public InterceptorLoggingBuilder SetMinimumLevel(LogLevel level)
    {
        _builder.SetMinimumLevel(level);
        return this;
    }

    internal ILoggingBuilder GetBuilder() => _builder;
}
