using Dolo.Pluto.Shard.Components.Toast;
using Dolo.Pluto.Shard.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Dolo.Pluto.Shard.Extensions;

public static class SharedComponentsExtensions
{
    /// <summary>
    /// Adds the shared Toast system to the service collection
    /// </summary>
    private static IServiceCollection AddSharedToastSystem(this IServiceCollection services)
    {
        services.AddSingleton<ToastService>();
        return services;
    }

    /// <summary>
    /// Adds the shared resource injection service
    /// </summary>
    private static IServiceCollection AddSharedResourceInjection(this IServiceCollection services)
    {
        services.AddScoped<SharedResourceInjectionService>();
        return services;
    }

    /// <summary>
    /// Adds all shared UI components and services
    /// </summary>
    public static IServiceCollection AddSharedUIComponents(this IServiceCollection services)
    {
        services.AddSharedToastSystem();
        services.AddSharedResourceInjection();
        return services;
    }
}
