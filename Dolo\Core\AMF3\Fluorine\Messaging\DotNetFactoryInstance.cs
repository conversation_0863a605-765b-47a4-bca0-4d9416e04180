﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class DotNetFactoryInstance : FactoryInstance
{
    private object _applicationInstance;
    private Type _cachedType;

    /// <summary>
    ///     Initializes a new instance of the DotNetFactoryInstance class.
    /// </summary>
    /// <param name="flexFactory"></param>
    /// <param name="id"></param>
    /// <param name="properties"></param>
    public DotNetFactoryInstance(IFlexFactory flexFactory, string id, Hashtable properties)
        : base(flexFactory, id, properties)
    {}

    public override string Source
    {
        get => base.Source;
        set
        {
            if (base.Source != value)
            {
                base.Source = value;
                _cachedType = null;
            }
        }
    }

    public object ApplicationInstance
    {
        get
        {
            if (_applicationInstance == null)
                lock (typeof(DotNetFactoryInstance))
                    if (_applicationInstance == null)
                        _applicationInstance = CreateInstance();

            return _applicationInstance;
        }
    }

    public object CreateInstance()
    {
        var type = GetInstanceClass();
        object instance = null;
        if (type == null)
        {
            var msg = __Res.GetString(__Res.Type_InitError, Source);
            throw new MessageException(msg, new TypeLoadException(msg));
        }

        if (type.IsAbstract && type.IsSealed)
            instance = type;
        else
            instance = Activator.CreateInstance(type,
            BindingFlags.CreateInstance | BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static, null,
            [], null);
        return instance;
    }


    public override Type GetInstanceClass()
    {
        if (_cachedType == null)
            _cachedType = ObjectFactory.LocateInLac(Source);
        return _cachedType;
    }
}