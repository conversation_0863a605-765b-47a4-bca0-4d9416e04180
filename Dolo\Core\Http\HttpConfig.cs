namespace Dolo.Core.Http;

/// <summary>
///     Represents the configuration for an HTTP request.
/// </summary>
public class HttpConfig {
    /// <summary>
    ///     Gets the headers to be included in the HTTP request.
    /// </summary>
    public List<KeyValuePair<string, string?>> HttpHeaders { get; } = [];

    /// <summary>
    ///     Gets or sets the URL for the HTTP request.
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    ///     Gets or sets the authentication token for the HTTP request.
    /// </summary>
    public string? AuthToken { get; set; }

    /// <summary>
    ///     Gets or sets the authentication type for the HTTP request.
    /// </summary>
    public string? AuthType { get; set; }

    /// <summary>
    ///     Gets or sets the referer for the HTTP request.
    /// </summary>
    public string? Referer { get; set; }

    /// <summary>
    ///     Gets or sets the origin for the HTTP request.
    /// </summary>
    public string? Origin { get; set; }

    /// <summary>
    ///     Gets or sets the accept header for the HTTP request.
    /// </summary>
    public string? Accept { get; set; }

    /// <summary>
    ///     Gets or sets the encoding for the HTTP request.
    /// </summary>
    public string? Encoding { get; set; }

    /// <summary>
    ///     Gets or sets the user agent for the HTTP request.
    /// </summary>
    public string? Agent { get; set; }

    /// <summary>
    ///     Gets or sets a value indicating whether the token type should be included in the authentication header.
    /// </summary>
    public bool WithoutTokenType { get; set; }
    /// <summary>
    ///    Gets or sets a value indicating whether decryption should be used for the HTTP request.
    /// </summary>
    public bool UseDecryption { get; set; }

    /// <summary>
    ///    Gets or sets the OCSP flag for the HTTP request.
    ///    If true, the request will use OCSP.
    /// </summary>
    public bool UseOCSP { get; set; }

    /// <summary>
    ///     Gets or sets the HTTP version for the request.
    /// </summary>
    public Version? Version { get; set; }

    /// <summary>
    ///     Gets or sets the proxy to be used for the HTTP request.
    /// </summary>
    public WebProxy? Proxy { get; set; }

    /// <summary>
    ///     Gets or sets the content for the HTTP request.
    /// </summary>
    public HttpContent? Content { get; set; }

    /// <summary>
    ///     Gets or sets the content type for the HTTP request.
    /// </summary>
    public HttpContentType? ContentType { get; set; }

    /// <summary>
    ///     Gets or sets the cancellation token for the HTTP request.
    /// </summary>
    public CancellationToken? CancellationToken { get; set; }

    /// <summary>
    ///     Gets or sets the HTTP method for the request.
    /// </summary>
    public HttpMethod Method { get; set; } = HttpMethod.Get;

    /// <summary>
    ///     Adds a header to the HTTP request.
    /// </summary>
    /// <param name="key">The name of the header.</param>
    /// <param name="value">The value of the header.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig AddHeader(string key, string? value) {
        HttpHeaders.Add(new(key, value));
        return this;
    }

    /// <summary>
    ///     Sets the URL for the HTTP request.
    /// </summary>
    /// <param name="url">The URL to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithUrl(string url) {
        Url = url;
        return this;
    }

    /// <summary>
    ///    Sets the OCSP flag for the HTTP request.
    /// </summary>
    /// <param name="useOCSP">The OCSP flag to be set.</param>
    public HttpConfig AllowOCSP() {
        UseOCSP = true;
        return this;
    }

    /// <summary>
    ///     Sets the authentication token for the HTTP request.
    /// </summary>
    /// <param name="token">The authentication token to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithAuthToken(string token) {
        AuthToken = token;
        return this;
    }

    /// <summary>
    ///     Sets the authentication type for the HTTP request.
    /// </summary>
    /// <param name="type">The authentication type to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithAuthType(string type) {
        AuthType = type;
        return this;
    }

    /// <summary>
    ///     Sets the referer for the HTTP request.
    /// </summary>
    /// <param name="referer">The referer to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithReferer(string referer) {
        Referer = referer;
        return this;
    }

    /// <summary>
    ///     Sets the origin for the HTTP request.
    /// </summary>
    /// <param name="origin">The origin to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithOrigin(string origin) {
        Origin = origin;
        return this;
    }

    /// <summary>
    ///     Sets the accept header for the HTTP request.
    /// </summary>
    /// <param name="accept">The accept header to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithAccept(string accept) {
        Accept = accept;
        return this;
    }

    /// <summary>
    ///     Sets the encoding for the HTTP request.
    /// </summary>
    /// <param name="encoding">The encoding to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithEncoding(string encoding) {
        Encoding = encoding;
        return this;
    }

    /// <summary>
    ///     Sets the user agent for the HTTP request.
    /// </summary>
    /// <param name="agent">The user agent to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithAgent(string agent) {
        Agent = agent;
        return this;
    }

    /// <summary>
    ///     Removes the token type from the authentication header for the HTTP request.
    /// </summary>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig RemoveTokenType() {
        WithoutTokenType = true;
        return this;
    }

    /// <summary>
    ///     Sets the HTTP version for the request.
    /// </summary>
    /// <param name="version">The HTTP version to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithVersion(Version version) {
        Version = version;
        return this;
    }

    /// <summary>
    ///     Sets the proxy to be used for the HTTP request.
    /// </summary>
    /// <param name="proxy">The proxy to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithProxy(WebProxy proxy) {
        Proxy = proxy;
        return this;
    }

    /// <summary>
    ///     Sets the content for the HTTP request.
    /// </summary>
    /// <param name="content">The content to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithContent(HttpContent content) {
        Content = content;
        return this;
    }

    /// <summary>
    ///     Sets the content type for the HTTP request.
    /// </summary>
    /// <param name="type">The content type to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithContentType(HttpContentType type) {
        ContentType = type;
        return this;
    }

    /// <summary>
    ///     Sets the cancellation token for the HTTP request.
    /// </summary>
    /// <param name="token">The cancellation token to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithCancellationToken(CancellationToken token) {
        CancellationToken = token;
        return this;
    }

    /// <summary>
    ///     Sets the HTTP method for the request.
    /// </summary>
    /// <param name="method">The HTTP method to be set.</param>
    /// <returns>The current <see cref="HttpConfig" /> instance.</returns>
    public HttpConfig WithMethod(HttpMethod method) {
        Method = method;
        return this;
    }
    
    /// <summary>
    ///    Sets whether to use decryption for the HTTP request.
    /// </summary>
    public HttpConfig WithDecryption() {
        UseDecryption = true;
        return this;
    }
}
