﻿using Dolo.Core.Http;
using static Dolo.Core.Http.Http;

namespace Dolo.Core;

public static class DiscordWebHooker
{
    private static async Task SendAsync(string token, string? content, bool hasEmbed, string? title = null, string? color = null, string? description = null, string? thumbnail = null)
    {
        if (string.IsNullOrEmpty(token))
            return;

        await TrySendAsync(a => {
            a.Url = token;
            a.Method = HttpMethod.Post;
            a.ContentType = HttpContentType.ApplicationJson;
            a.Content = new StringContent(hasEmbed ?
                                              CreateJsonEmbedContent(title, color?.ToInt().ToString(), description, thumbnail) :
                                              CreateJsonContent(content));
            ;
        });
    }


    public static async Task SendFileAsync(string token, Stream stream, string? content = null)
    {
        if (string.IsNullOrEmpty(token))
            return;

        await TrySendAsync(a => {
            a.Url = token;
            a.Method = HttpMethod.Post;
            a.ContentType = HttpContentType.MultipartFormData;
            a.Content = new MultipartFormDataContent
            {
                { new StreamContent(stream), "file", "file" },
                { new StringContent(content ?? string.Empty), "content" }
            };
        });
    }

    public static async Task SendAsync(string token, string? content) => await SendAsync(token, content, false);

    public static async Task SendAsync(string token, string? title, string? color, string? description, string? thumbnail = null) => await SendAsync(token, null, true, title, color, description, thumbnail);

    private static string CreateJsonContent(string? content) => JsonConvert.SerializeObject(new
    {
        content
    });

    private static string CreateJsonEmbedContent(string? title, string? color, string? description, string? thumbnail) => JsonConvert.SerializeObject(new
    {
        embeds = new[]
        {
            new
            {
                title,
                color,
                description,
                thumbnail = new
                {
                    url = thumbnail
                }
            }
        }
    });
}