﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     Sorted set or multiset of objects.
/// </summary>
/// <remarks>
///     ISet represents a modifiable collection of objects that are sorted in
///     ascending order to given Comparer. Derived classes may or may not
///     permit duplicate (equivalent) objects.
/// </remarks>
internal interface ISet : IModifiableCollection, IReversible
{
    /// <summary>
    ///     Returns comparer object used by the set.
    /// </summary>
    IComparer Comparer { get; }
}