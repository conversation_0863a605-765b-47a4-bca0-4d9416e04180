﻿using Dolo.Core.Discord;
using Dolo.Database;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Maintenance
    {
        [Command("get")]
        [Description("get the maintenance message")]
        public async Task GetAsync(SlashCommandContext ctx)
        {
            if (ctx.User.Id != 440584675740876810) return;

            var settings = await Mongo.PixiSettings.GetFirstAsync();
            await ctx.TryCreateResponseAsync($"Maintenance: `{settings?.Maintenance}`");
        }
    }
}