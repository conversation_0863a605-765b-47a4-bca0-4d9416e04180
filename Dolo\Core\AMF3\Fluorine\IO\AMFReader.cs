﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO.Readers;
using Dolo.Core.AMF3.Fluorine.IO.Readers.AMF0;
using Dolo.Core.AMF3.Fluorine.IO.Readers.AMF3;
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMFReader : BinaryReader
{
    private static readonly IAMFReader[][] AmfTypeTable;

    private List<object> _amf0ObjectReferences;
    private List<ClassDefinition> _classDefinitions;
    private List<object> _objectReferences;
    private List<object> _stringReferences;

    static AMFReader()
    {
        IAMFReader[] amf0Readers = [new AMF0NumberReader(),/*0*/ new AMF0BooleanReader(),/*1*/ new AMF0StringReader(),/*2*/ new AMF0ASObjectReader(),/*3*/ new AMFUnknownTagReader(), new AMF0NullReader(),/*5*/ new AMF0NullReader(),/*6*/ new AMF0ReferenceReader(),/*7*/ new AMF0AssociativeArrayReader(),/*8*/ new AMFUnknownTagReader(), new AMF0ArrayReader(),/*10*/ new AMF0DateTimeReader(),/*11*/ new AMF0LongStringReader(),/*12*/ new AMFUnknownTagReader(), new AMFUnknownTagReader(), new AMF0XmlReader(),/*15*/ new AMF0ObjectReader(),/*16*/ new AMF0AMF3TagReader()/*17*/];

        IAMFReader[] amf3Readers = [new AMF3NullReader(),/*0*/ new AMF3NullReader(),/*1*/ new AMF3BooleanFalseReader(),/*2*/ new AMF3BooleanTrueReader(),/*3*/ new AMF3IntegerReader(),/*4*/ new AMF3NumberReader(),/*5*/ new AMF3StringReader(),/*6*/ new AMF3XmlReader(),/*7*/ new AMF3DateTimeReader(),/*8*/ new AMF3ArrayReader(),/*9*/ new AMF3ObjectReader(),/*10*/ new AMF3XmlReader(),/*11*/ new AMF3ByteArrayReader(),/*12*/ new AMFUnknownTagReader(), new AMFUnknownTagReader(), new AMFUnknownTagReader(), new AMFUnknownTagReader(), new AMFUnknownTagReader()];

        AmfTypeTable = new IAMFReader[4][] { amf0Readers, null, null, amf3Readers };
    }

    /// <summary>
    ///     Initializes a new instance of the AMFReader class based on the supplied stream and using UTF8Encoding.
    /// </summary>
    /// <param name="stream"></param>
    public AMFReader(Stream stream) : base(stream)
    {
        Reset();
    }

    /// <summary>
    ///     Gets or sets whether legacy collection serialization is used for AMF3.
    /// </summary>
    public bool UseLegacyCollection { get; set; } = true;

    /// <summary>
    ///     Indicates whether reflection errors should raise an exception or set the LastError property.
    /// </summary>
    public bool FaultTolerancy { get; set; } = false;

    /// <summary>
    ///     Returns the last exception that ocurred while deserializing an object.
    /// </summary>
    /// <returns></returns>
    public Exception LastError { get; private set; }

    /// <summary>
    ///     Resets object references.
    /// </summary>
    public void Reset()
    {
        _amf0ObjectReferences = new(5);
        _objectReferences = new(15);
        _stringReferences = new(15);
        _classDefinitions = new(2);
        LastError = null;
    }

    /// <summary>
    ///     Deserializes object graphs from Action Message Format (AMF).
    /// </summary>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadData()
    {
        var typeCode = ReadByte();
        return ReadData(typeCode);
    }

    /// <summary>
    ///     Deserializes an object using the specified type marker.
    /// </summary>
    /// <param name="typeMarker">Type marker.</param>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadData(byte typeMarker) => AmfTypeTable[0][typeMarker].ReadData(this);

    /// <summary>
    ///     Reads a reference type.
    /// </summary>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadReference()
    {
        int reference = ReadUInt16();
        //return _amf0ObjectReferences[reference-1];
        return _amf0ObjectReferences[reference];
    }

    /// <summary>
    ///     Reads a 2-byte unsigned integer from the current AMF stream using network byte order encoding and advances the
    ///     position of the stream by two bytes.
    /// </summary>
    /// <returns>The 2-byte unsigned integer.</returns>
    public override ushort ReadUInt16()
    {
        //Read the next 2 bytes, shift and add.
        var bytes = ReadBytes(2);
        return (ushort)((bytes[0] & 0xff) << 8 | bytes[1] & 0xff);
    }

    /// <summary>
    ///     Reads a 2-byte signed integer from the current AMF stream using network byte order encoding and advances the
    ///     position of the stream by two bytes.
    /// </summary>
    /// <returns>The 2-byte signed integer.</returns>
    public override short ReadInt16()
    {
        //Read the next 2 bytes, shift and add.
        var bytes = ReadBytes(2);
        return (short)(bytes[0] << 8 | bytes[1]);
    }

    /// <summary>
    ///     Reads an UTF-8 encoded String from the current AMF stream.
    /// </summary>
    /// <returns>The String value.</returns>
    public override string ReadString()
    {
        //Get the length of the string (first 2 bytes).
        int length = ReadUInt16();
        return ReadUTF(length);
    }    /// <summary>
    ///     Reads a Boolean value from the current AMF stream using network byte order encoding and advances the position of
    ///     the stream by one byte.
    /// </summary>
    /// <returns>The Boolean value.</returns>
    public override bool ReadBoolean() 
    {
        // Check if we can read at least 1 byte
        if (BaseStream.Position >= BaseStream.Length)
            throw new EndOfStreamException("Cannot read beyond the end of the stream");
            
        return base.ReadBoolean();
    }

    /// <summary>
    ///     Reads a 4-byte signed integer from the current AMF stream using network byte order encoding and advances the
    ///     position of the stream by four bytes.
    /// </summary>
    /// <returns>The 4-byte signed integer.</returns>
    public override int ReadInt32()
    {
        // Read the next 4 bytes, shift and add
        var bytes = ReadBytes(4);
        var value = bytes[0] << 24 | bytes[1] << 16 | bytes[2] << 8 | bytes[3];
        return value;
    }

    /// <summary>
    ///     Reads a 3-byte signed integer from the current AMF stream using network byte order encoding and advances the
    ///     position of the stream by three bytes.
    /// </summary>
    /// <returns>The 3-byte signed integer.</returns>
    public int ReadUInt24()
    {
        var bytes = ReadBytes(3);
        var value = bytes[0] << 16 | bytes[1] << 8 | bytes[2];
        return value;
    }

    /// <summary>
    ///     Reads an 8-byte IEEE-754 double precision floating point number from the current AMF stream using network byte
    ///     order encoding and advances the position of the stream by eight bytes.
    /// </summary>
    /// <returns>The 8-byte double precision floating point number.</returns>
    public override double ReadDouble()
    {
        var bytes = ReadBytes(8);
        var reverse = new byte[8];
        //Grab the bytes in reverse order 
        for (int i = 7, j = 0; i >= 0; i--, j++) reverse[j] = bytes[i];
        var value = BitConverter.ToDouble(reverse, 0);
        return value;
    }

    /// <summary>
    ///     Reads a single-precision floating point number from the current AMF stream using network byte order encoding and
    ///     advances the position of the stream by eight bytes.
    /// </summary>
    /// <returns>The single-precision floating point number.</returns>
    public float ReadFloat()
    {
        var bytes = ReadBytes(4);
        var invertedBytes = new byte[4];
        //Grab the bytes in reverse order from the backwards index
        for (int i = 3, j = 0; i >= 0; i--, j++) invertedBytes[j] = bytes[i];
        var value = BitConverter.ToSingle(invertedBytes, 0);
        return value;
    }

    /// <summary>
    ///     Add object reference.
    /// </summary>
    /// <param name="instance">The object instance.</param>
    public void AddReference(object instance)
    {
        _amf0ObjectReferences.Add(instance);
    }

    /// <summary>
    ///     Reads an AMF0 object.
    /// </summary>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadObject()
    {
        var typeIdentifier = ReadString();

        var type = ObjectFactory.Locate(typeIdentifier);
        if (type != null)
        {
            var instance = ObjectFactory.CreateInstance(type);
            AddReference(instance);

            var key = ReadString();
            for (var typeCode = ReadByte(); typeCode != AMF0TypeCode.EndOfObject; typeCode = ReadByte())
            {
                var value = ReadData(typeCode);
                SetMember(instance, key, value);
                key = ReadString();
            }

            return instance;
        }

        ASObject asObject;
        //Reference added in ReadASObject
        asObject = ReadASObject();
        asObject.TypeName = typeIdentifier;
        return asObject;
    }

    /// <summary>
    ///     Reads an anonymous ActionScript object.
    /// </summary>
    /// <returns>The anonymous ActionScript object deserialized from the AMF stream.</returns>
    public ASObject ReadASObject()
    {
        var asObject = new ASObject();
        AddReference(asObject);
        var key = ReadString();
        for (var typeCode = ReadByte(); typeCode != AMF0TypeCode.EndOfObject; typeCode = ReadByte())
        {
            //asObject.Add(key, ReadData(typeCode));
            asObject[key] = ReadData(typeCode);
            key = ReadString();
        }

        return asObject;
    }

    /// <summary>
    ///     Reads an UTF-8 encoded String.
    /// </summary>
    /// <param name="length">Byte-length header.</param>    /// <returns>The String value.</returns>
    public string ReadUTF(int length)
    {
        if (length == 0)
            return string.Empty;
        
        var encodedBytes = ReadBytes(length);
        
        // Try UTF-8 decoding first with fallback for invalid bytes
        try
        {
            var utf8 = new UTF8Encoding(false, false); // Don't throw on invalid bytes
            var decodedString = utf8.GetString(encodedBytes, 0, encodedBytes.Length);
            
            // If the string contains replacement characters, it likely contained binary data
            if (decodedString.Contains('\uFFFD'))
            {
                // Return a hex representation for binary data
                return $"[Binary Data: {System.Convert.ToHexString(encodedBytes)}]";
            }
            
            return decodedString;
        }
        catch (Exception ex)
        {
            // Fallback to hex representation if UTF-8 decoding fails
            return $"[Binary Data: {System.Convert.ToHexString(encodedBytes)} - Error: {ex.Message}]";
        }
    }

    /// <summary>
    ///     Reads an UTF-8 encoded AMF0 Long String type.
    /// </summary>
    /// <returns>The String value.</returns>
    public string ReadLongString()
    {
        var length = ReadInt32();
        return ReadUTF(length);
    }

    /// <summary>
    ///     Reads an An ECMA or associative Array.
    /// </summary>
    /// <returns>The associative Array.</returns>
    internal Dictionary<string, object> ReadAssociativeArray()
    {
        // Get the length property set by flash.
        var length = ReadInt32();
        var result = new Dictionary<string, object>(length);
        AddReference(result);
        var key = ReadString();
        for (var typeCode = ReadByte(); typeCode != AMF0TypeCode.EndOfObject; typeCode = ReadByte())
        {
            var value = ReadData(typeCode);
            result.Add(key, value);
            key = ReadString();
        }

        return result;
    }

    /// <summary>
    ///     Reads an AMF0 strict Array.
    /// </summary>
    /// <returns>The Array.</returns>
    internal IList ReadArray()
    {
        //Get the length of the array.
        var length = ReadInt32();
        var array = new object[length];
        //ArrayList array = new ArrayList(length);
        AddReference(array);
        for (var i = 0; i < length; i++) array[i] = ReadData();
        //array.Add( ReadData() );
        return array;
    }

    /// <summary>
    ///     Reads an ActionScript Date.
    /// </summary>
    /// <returns>The DateTime.</returns>
    public DateTime ReadDateTime()
    {
        var milliseconds = ReadDouble();
        var start = new DateTime(1970, 1, 1);

        var date = start.AddMilliseconds(milliseconds);
        date = DateTime.SpecifyKind(date, DateTimeKind.Utc);
        int tmp = ReadUInt16();
        //Note for the latter than values greater than 720 (12 hours) are 
        //represented as 2^16 - the value.
        //Thus GMT+1 is 60 while GMT-5 is 65236
        if (tmp > 720)
            tmp = 65536 - tmp;
        var tz = tmp / 60;
        switch (AMFConfiguration.Instance.TimezoneCompensation)
        {
            case TimezoneCompensation.None:
                break;
            case TimezoneCompensation.Auto:
                date = date.AddHours(tz);
                date = DateTime.SpecifyKind(date, DateTimeKind.Unspecified);
                break;
        }

        return date;
    }

    /// <summary>
    ///     Reads an XML Document Type.
    ///     The XML document type is always encoded as a long UTF-8 string.
    /// </summary>
    /// <returns>The XmlDocument.</returns>
    public XmlDocument ReadXmlDocument()
    {
        var text = ReadLongString();
        var document = new XmlDocument();
        if (text != null && text != string.Empty)
            document.LoadXml(text);
        return document;
    }

    internal void SetMember(object instance, string memberName, object value)
    {
        if (instance is ASObject)
        {
            ((ASObject)instance)[memberName] = value;
            return;
        }

        var type = instance.GetType();
        //PropertyInfo propertyInfo = type.GetProperty(memberName);
        PropertyInfo propertyInfo = null;
        try
        {
            propertyInfo = type.GetProperty(memberName);
        }
        catch (AmbiguousMatchException)
        {
            //To resolve the ambiguity, include BindingFlags.DeclaredOnly to restrict the search to members that are not inherited.
            propertyInfo = type.GetProperty(memberName,
            BindingFlags.DeclaredOnly | BindingFlags.GetProperty | BindingFlags.Public | BindingFlags.Instance);
        }

        if (propertyInfo != null)
            try
            {
                if (propertyInfo.PropertyType == typeof(TimeSpan) ||
                    propertyInfo.PropertyType == typeof(TimeSpan?) && value != null)
                    value = Util.Convert.ToTimeSpan(value);
                else
                    value = TypeHelper.ChangeType(value, propertyInfo.PropertyType);

                if (propertyInfo.CanWrite)
                {
                    if (propertyInfo.GetIndexParameters() == null || propertyInfo.GetIndexParameters().Length == 0)
                        propertyInfo.SetValue(instance, value, null);
                    else
                    {
                        var msg = __Res.GetString(__Res.Reflection_PropertyIndexFail,
                        string.Format("{0}.{1}", type.FullName, memberName));

                        if (!FaultTolerancy)
                            throw new AMFException(msg);
                        LastError = new AMFException(msg);
                    }
                }
                else
                {
                    var msg = __Res.GetString(__Res.Reflection_PropertyReadOnly,
                    string.Format("{0}.{1}", type.FullName, memberName));
                }
            }
            catch (Exception ex)
            {
                var msg = __Res.GetString(__Res.Reflection_PropertySetFail,
                string.Format("{0}.{1}", type.FullName, memberName), ex.Message);

                if (!FaultTolerancy)
                    throw new AMFException(msg);
                LastError = new AMFException(msg);
            }
        else
        {
            var fi = type.GetField(memberName, BindingFlags.Public | BindingFlags.Instance);
            try
            {
                if (fi != null)
                {
                    if (fi.FieldType == typeof(TimeSpan))
                        value = Util.Convert.ToTimeSpan(value);
                    else
                        value = TypeHelper.ChangeType(value, fi.FieldType);

                    fi.SetValue(instance, value);
                }
                else
                {
                    var msg = __Res.GetString(__Res.Reflection_MemberNotFound,
                    string.Format("{0}.{1}", type.FullName, memberName));
                }
            }
            catch (Exception ex)
            {
                var msg = __Res.GetString(__Res.Reflection_FieldSetFail,
                string.Format("{0}.{1}", type.FullName, memberName), ex.Message);

                if (!FaultTolerancy)
                    throw new AMFException(msg);
                LastError = new AMFException(msg);
            }
        }
    }

    #region AMF3

    /// <summary>
    ///     Deserializes object graphs from Action Message Format (AMF3).
    /// </summary>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadAMF3Data()
    {
        var typeCode = ReadByte();
        return ReadAMF3Data(typeCode);
    }

    /// <summary>
    ///     Deserializes an object using the specified type marker.
    /// </summary>
    /// <param name="typeMarker">Type marker.</param>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadAMF3Data(byte typeMarker) => AmfTypeTable[3][typeMarker].ReadData(this);
    /// <summary>
    ///     Add object reference.
    /// </summary>
    /// <param name="instance">The object instance.</param>
    public void AddAMF3ObjectReference(object instance)
    {
        _objectReferences.Add(instance);
    }

    /// <summary>
    ///     Reads a reference type.
    /// </summary>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadAMF3ObjectReference(int index) => _objectReferences[index];

    /// <summary>
    ///     Handle decoding of the variable-length representation which gives seven bits of value per serialized byte by using
    ///     the high-order bit
    ///     of each byte as a continuation flag.
    /// </summary>
    /// <returns></returns>
    public int ReadAMF3IntegerData()
    {
        int acc = ReadByte();
        int tmp;
        if (acc < 128)
            return acc;
        acc = (acc & 0x7f) << 7;
        tmp = ReadByte();
        if (tmp < 128)
            acc = acc | tmp;
        else
        {
            acc = (acc | tmp & 0x7f) << 7;
            tmp = ReadByte();
            if (tmp < 128)
                acc = acc | tmp;
            else
            {
                acc = (acc | tmp & 0x7f) << 8;
                tmp = ReadByte();
                acc = acc | tmp;
            }
        }

        //To sign extend a value from some number of bits to a greater number of bits just copy the sign bit into all the additional bits in the new format.
        //convert/sign extend the 29bit two's complement number to 32 bit
        var mask = 1 << 28;// mask
        var r = -(acc & mask) | acc;
        return r;

        //The following variation is not portable, but on architectures that employ an 
        //arithmetic right-shift, maintaining the sign, it should be fast. 
        //s = 32 - 29;
        //r = (x << s) >> s;
    }

    /// <summary>
    ///     Reads a 4-byte signed integer from the current AMF stream.
    /// </summary>
    /// <returns>The 4-byte signed integer.</returns>
    public int ReadAMF3Int()
    {
        var intData = ReadAMF3IntegerData();
        return intData;
    }

    /// <summary>
    ///     Reads an ActionScript Date.
    /// </summary>
    /// <returns>The DateTime.</returns>
    public DateTime ReadAMF3Date()
    {
        var handle = ReadAMF3IntegerData();
        var inline = (handle & 1) != 0;
        handle = handle >> 1;
        if (inline)
        {
            var milliseconds = ReadDouble();
            var start = new DateTime(1970, 1, 1, 1, 0, 0);

            var date = start.AddMilliseconds(milliseconds);
            date = DateTime.SpecifyKind(date, DateTimeKind.Utc);
            AddAMF3ObjectReference(date);
            return date;
        }

        return (DateTime)ReadAMF3ObjectReference(handle);
    }

    internal void AddStringReference(string str)
    {
        _stringReferences.Add(str);
    }

    internal string ReadStringReference(int index) => _stringReferences[index] as string;

    /// <summary>
    ///     Reads an UTF-8 encoded String from the current AMF stream.
    /// </summary>
    /// <returns>The String value.</returns>
    public string ReadAMF3String()
    {
        var handle = ReadAMF3IntegerData();
        var inline = (handle & 1) != 0;
        handle = handle >> 1;
        if (inline)
        {
            var length = handle;
            if (length == 0)
                return string.Empty;
            var str = ReadUTF(length);
            AddStringReference(str);
            return str;
        }

        return ReadStringReference(handle);
    }

    /// <summary>
    ///     Reads an XML Document Type.
    ///     The XML document type is always encoded as a long UTF-8 string.
    /// </summary>
    /// <returns>The XmlDocument.</returns>
    public XmlDocument ReadAMF3XmlDocument()
    {
        var handle = ReadAMF3IntegerData();
        var inline = (handle & 1) != 0;
        handle = handle >> 1;
        var xml = string.Empty;
        if (inline)
        {
            if (handle > 0)//length
                xml = ReadUTF(handle);
            AddAMF3ObjectReference(xml);
        }
        else
            xml = ReadAMF3ObjectReference(handle) as string;

        var xmlDocument = new XmlDocument();
        if (xml != null && xml != string.Empty)
            xmlDocument.LoadXml(xml);
        return xmlDocument;
    }

    /// <summary>
    ///     Reads a ByteArray.
    /// </summary>
    /// <returns>The ByteArray instance.</returns>
    /// <remarks>
    ///     <para>
    ///         ActionScript 3.0 introduces a new type to hold an Array of bytes, namely
    ///         ByteArray. AMF 3 serializes this type using a variable length encoding 29-bit
    ///         integer for the byte-length prefix followed by the raw bytes of the ByteArray.
    ///         ByteArray instances can be sent as a reference to a previously occurring ByteArray
    ///         instance by using an index to the implicit object reference table.
    ///     </para>
    ///     <para>
    ///         Note that this encoding imposes some theoretical limits on the use of
    ///         ByteArray. The maximum byte-length of each ByteArray instance is limited to 2^28 -
    ///         1 bytes (approx 256 MB).
    ///     </para>
    /// </remarks>
    public ByteArray ReadAMF3ByteArray()
    {
        var handle = ReadAMF3IntegerData();
        var inline = (handle & 1) != 0;
        handle = handle >> 1;
        if (inline)
        {
            var length = handle;
            var buffer = ReadBytes(length);
            var ba = new ByteArray(buffer);
            AddAMF3ObjectReference(ba);
            return ba;
        }

        return ReadAMF3ObjectReference(handle) as ByteArray;
    }

    /// <summary>
    ///     Reads an AMF3 Array (string or associative).
    /// </summary>
    /// <returns>The Array instance.</returns>
    public object ReadAMF3Array()
    {
        var handle = ReadAMF3IntegerData();
        var inline = (handle & 1) != 0;
        handle = handle >> 1;
        if (inline)
        {
            Dictionary<string, object> hashtable = null;
            var key = ReadAMF3String();
            while (key != null && key != string.Empty)
            {
                if (hashtable == null)
                {
                    hashtable = new();
                    AddAMF3ObjectReference(hashtable);
                }

                var value = ReadAMF3Data();
                hashtable.Add(key, value);
                key = ReadAMF3String();
            }

            //Not an associative array
            if (hashtable == null)
            {
                var array = new object[handle];
                AddAMF3ObjectReference(array);
                for (var i = 0; i < handle; i++)
                {
                    //Grab the type for each element.
                    var typeCode = ReadByte();
                    var value = ReadAMF3Data(typeCode);
                    array[i] = value;
                }

                return array;
            }

            for (var i = 0; i < handle; i++)
            {
                var value = ReadAMF3Data();
                hashtable.Add(i.ToString(), value);
            }

            return hashtable;
        }

        return ReadAMF3ObjectReference(handle);
    }

    internal void AddClassReference(ClassDefinition classDefinition)
    {
        _classDefinitions.Add(classDefinition);
    }

    internal ClassDefinition ReadClassReference(int index) => _classDefinitions[index];

    internal ClassDefinition ReadClassDefinition(int handle)
    {
        ClassDefinition classDefinition = null;
        //an inline object
        var inlineClassDef = (handle & 1) != 0;
        handle = handle >> 1;
        if (inlineClassDef)
        {
            //inline class-def
            var typeIdentifier = ReadAMF3String();
            //flags that identify the way the object is serialized/deserialized
            var externalizable = (handle & 1) != 0;
            handle = handle >> 1;
            var dynamic = (handle & 1) != 0;
            handle = handle >> 1;

            var members = new ClassMember[handle];
            for (var i = 0; i < handle; i++)
            {
                var name = ReadAMF3String();
                var classMember = new ClassMember(name, BindingFlags.Default, MemberTypes.Custom);
                members[i] = classMember;
            }

            classDefinition = new(typeIdentifier, members, externalizable, dynamic);
            AddClassReference(classDefinition);
        }
        else
            //A reference to a previously passed class-def
            classDefinition = ReadClassReference(handle);

        return classDefinition;
    }

    internal object ReadAMF3Object(ClassDefinition classDefinition)
    {
        object instance = null;
        if (classDefinition.ClassName != null && classDefinition.ClassName != string.Empty)
            instance = ObjectFactory.CreateInstance(classDefinition.ClassName);
        else
            instance = new ASObject();
        if (instance == null)
            instance = new ASObject(classDefinition.ClassName);
        AddAMF3ObjectReference(instance);
        if (classDefinition.IsExternalizable)
        {
            if (instance is IExternalizable)
            {
                var externalizable = instance as IExternalizable;
                var dataInput = new DataInput(this);
                externalizable.ReadExternal(dataInput);
            }
            else
            {
                var msg = __Res.GetString(__Res.Externalizable_CastFail, instance.GetType().FullName);
                throw new AMFException(msg);
            }
        }
        else
        {
            for (var i = 0; i < classDefinition.MemberCount; i++)
            {
                var key = classDefinition.Members[i].Name;
                var value = ReadAMF3Data();
                SetMember(instance, key, value);
            }

            if (classDefinition.IsDynamic)
            {
                var key = ReadAMF3String();
                while (key != null && key != string.Empty)
                {
                    var value = ReadAMF3Data();
                    SetMember(instance, key, value);
                    key = ReadAMF3String();
                }
            }
        }

        return instance;
    }

    /// <summary>
    ///     Reads an AMF3 object.
    /// </summary>
    /// <returns>The Object deserialized from the AMF stream.</returns>
    public object ReadAMF3Object()
    {
        var handle = ReadAMF3IntegerData();
        var inline = (handle & 1) != 0;
        handle = handle >> 1;
        if (!inline)
            //An object reference
            return ReadAMF3ObjectReference(handle);
        var classDefinition = ReadClassDefinition(handle);
        var obj = ReadAMF3Object(classDefinition);
        return obj;
    }

    #endregion AMF3
}
