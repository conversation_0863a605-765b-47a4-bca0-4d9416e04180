using Dolo.Core.Http;
using Dolo.Pluto.Shard.Configuration;

namespace Dolo.Pluto.Shard.Services.Api;

public class ToolboxApiService(IAppConfiguration appConfig) : IToolboxApiService
{
    private readonly string _toolboxEndpoint = $"{appConfig.ApiDomain}api/toolbox";

    public async Task<Toolbox.Toolbox?> GetToolboxAsync()
    {
        var response = await Http.TryGetAsync<Toolbox.Toolbox>(_toolboxEndpoint);
        return response.IsSuccess ? response.Result : null;
    }
}
