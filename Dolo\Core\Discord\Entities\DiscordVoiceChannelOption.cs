using System;
using DSharpPlus.Entities;

namespace Dolo.Core.Discord.Entities;

public class DiscordVoiceChannelOption
{
    public string Name { get; set; } = string.Empty;
    public DiscordChannel? Parent { get; set; }
    public int? Bitrate { get; set; }
    public int? UserLimit { get; set; }
    public IEnumerable<DiscordOverwriteBuilder>? Overwrites { get; set; }
    public DiscordVideoQualityMode? QualityMode { get; set; }
    public int? Position { get; set; }
    public string? Reason { get; set; }
}
