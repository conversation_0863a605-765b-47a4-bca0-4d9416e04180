﻿namespace Dolo.Bot.Apple.Hub.Handler;

public static class ChannelCreated
{
    public static async Task InvokeAsync(this ChannelCreatedEventArgs e)
    {
        if (e.Channel.Type      != DiscordChannelType.Voice
            || e.Channel.Parent != HubChannel.VoiceTopic
            || HubChannel.Voice is null)
            return;

        // set the position of the voice channel over the voice create channel
        await e.Channel.TryModifyPositionAsync(HubChannel.Voice.Position);
    }
}