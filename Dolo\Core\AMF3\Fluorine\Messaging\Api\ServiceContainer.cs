﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

internal class ServiceContainer : IServiceContainer
{
    private IServiceProvider _parentProvider;
    private Dictionary<Type, object> _services = new();

    public ServiceContainer() : this(null)
    {}

    public ServiceContainer(IServiceProvider parentProvider)
        => _parentProvider = parentProvider;

    private IServiceContainer Container
    {
        get
        {
            IServiceContainer service = null;
            if (_parentProvider != null)
                service = (IServiceContainer)_parentProvider.GetService(typeof(IServiceContainer));
            return service;
        }
    }

    /// <summary>
    ///     Gets an object that can be used to synchronize access.
    /// </summary>
    public object SyncRoot
        => ((ICollection)_services).SyncRoot;

    #region IServiceProvider Members

    public object GetService(Type serviceType)
    {
        ValidationUtils.ArgumentNotNull(serviceType, "serviceType");
        object service = null;
        lock (SyncRoot)
        {
            if (_services.ContainsKey(serviceType))
                service = _services[serviceType];
            if (service == null && _parentProvider != null) service = _parentProvider.GetService(serviceType);
        }

        return service;
    }

    #endregion

    internal void Shutdown()
    {
        lock (SyncRoot)
        {
            foreach (var serviceInstance in _services.Values)
            {
                var service = serviceInstance as IService;
                service?.Shutdown();
            }

            _services.Clear();
            _services = null;
            _parentProvider = null;
        }
    }

    #region IServiceContainer Members

    public void AddService(Type serviceType, object service)
    {
        AddService(serviceType, service, false);
    }

    public void AddService(Type serviceType, object service, bool promote)
    {
        ValidationUtils.ArgumentNotNull(serviceType, "serviceType");
        ValidationUtils.ArgumentNotNull(service, "service");
        lock (SyncRoot)
        {
            if (promote)
            {
                var container = Container;
                if (container != null)
                {
                    container.AddService(serviceType, service, promote);
                    return;
                }
            }

            if (_services.ContainsKey(serviceType))
                throw new ArgumentException(string.Format("Service {0} already exists", serviceType.FullName));
            _services[serviceType] = service;
        }
    }

    public void RemoveService(Type serviceType)
    {
        RemoveService(serviceType, false);
    }

    public void RemoveService(Type serviceType, bool promote)
    {
        ValidationUtils.ArgumentNotNull(serviceType, "serviceType");
        lock (SyncRoot)
        {
            if (promote)
            {
                var container = Container;
                if (container != null)
                {
                    container.RemoveService(serviceType, promote);
                    return;
                }
            }

            if (_services.ContainsKey(serviceType))
            {
                var service = _services[serviceType] as IService;
                service?.Shutdown();
            }

            _services.Remove(serviceType);
        }
    }

    #endregion
}