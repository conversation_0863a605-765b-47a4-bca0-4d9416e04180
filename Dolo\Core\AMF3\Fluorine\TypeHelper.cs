﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.ComponentModel;
using System.Globalization;
using System.Security;
using System.Xml;
using System.Xml.Linq;
using Dolo.Core.AMF3.Fluorine.Util;
namespace Dolo.Core.AMF3.Fluorine;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal sealed class TypeHelper
{
    private static object _syncLock = new();

    static TypeHelper()
    {
        _defaultSByteNullValue = (sbyte)GetNullValue(typeof(sbyte));
        _defaultInt16NullValue = (short)GetNullValue(typeof(short));
        _defaultInt32NullValue = (int)GetNullValue(typeof(int));
        _defaultInt64NullValue = (long)GetNullValue(typeof(long));
        _defaultByteNullValue = (byte)GetNullValue(typeof(byte));
        _defaultUInt16NullValue = (ushort)GetNullValue(typeof(ushort));
        _defaultUInt32NullValue = (uint)GetNullValue(typeof(uint));
        _defaultUInt64NullValue = (ulong)GetNullValue(typeof(ulong));
        _defaultCharNullValue = (char)GetNullValue(typeof(char));
        _defaultSingleNullValue = (float)GetNullValue(typeof(float));
        _defaultDoubleNullValue = (double)GetNullValue(typeof(double));
        _defaultBooleanNullValue = (bool)GetNullValue(typeof(bool));

        _defaultStringNullValue = (string)GetNullValue(typeof(string));
        _defaultDateTimeNullValue = (DateTime)GetNullValue(typeof(DateTime));
        _defaultDecimalNullValue = (decimal)GetNullValue(typeof(decimal));
        _defaultGuidNullValue = (Guid)GetNullValue(typeof(Guid));
        _defaultXmlReaderNullValue = (XmlReader)GetNullValue(typeof(XmlReader));
        _defaultXmlDocumentNullValue = (XmlDocument)GetNullValue(typeof(XmlDocument));
        _defaultXDocumentNullValue = (XDocument)GetNullValue(typeof(XDocument));
        _defaultXElementNullValue = (XElement)GetNullValue(typeof(XElement));

        _Init();
    }

    internal static void _Init()
    {}

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <returns></returns>
    public static Assembly[] GetAssemblies() => AppDomain.CurrentDomain.GetAssemblies();

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="typeName"></param>
    /// <returns></returns>
    public static Type Locate(string typeName)
    {
        if (typeName == null || typeName == string.Empty)
            #pragma warning disable CS8603// Possible null reference return.
            return null;
        #pragma warning restore CS8603   // Possible null reference return.
        var assemblies = GetAssemblies();// AppDomain.CurrentDomain.GetAssemblies();
        for (var i = 0; i < assemblies.Length; i++)
        {
            var assembly = assemblies[i];
            var type = assembly.GetType(typeName, false);
            if (type != null)
                return type;
        }
        #pragma warning disable CS8603// Possible null reference return.
        return null;
        #pragma warning restore CS8603// Possible null reference return.
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="typeName"></param>
    /// <param name="lac"></param>
    /// <returns></returns>
    public static Type LocateInLac(string typeName, string lac) =>
        //if( lac == null  )
        //	return null;
        //if( typeName == null || typeName == string.Empty )
        //	return null;
        //         foreach (string file in Directory.GetFiles(lac, "*.dll"))
        //{
        //	try
        //	{
        //		Assembly assembly = Assembly.LoadFrom(file);
        //		Type type = assembly.GetType(typeName, false);
        //		if (type != null)
        //			return type;
        //	}
        //	catch (Exception)
        //	{
        //	}
        //}
        //         foreach (string file in Directory.GetFiles(lac, "*.exe"))
        //         {
        //             try
        //             {
        //                 Assembly assembly = Assembly.LoadFrom(file);
        //                 Type type = assembly.GetType(typeName, false);
        //                 if (type != null)
        //                     return type;
        //             }
        //             catch (Exception)
        //             {
        //             }
        //         }
        //         foreach (string dir in Directory.GetDirectories(lac))
        //         {
        //             Type type = LocateInLac(typeName, dir);
        //             if (type != null)
        //                 return type;
        //         }
        null;

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="lac"></param>
    /// <param name="excludedBaseTypes"></param>
    /// <returns></returns>
    public static Type[] SearchAllTypes(string lac, Hashtable excludedBaseTypes)
    {
        var result = new ArrayList();
        foreach (var file in Directory.GetFiles(lac, "*.dll"))
            try
            {
                var assembly = Assembly.LoadFrom(file);
                if (assembly == Assembly.GetExecutingAssembly())
                    continue;
                foreach (var type in assembly.GetTypes())
                {
                    if (excludedBaseTypes != null)
                    {
                        if (excludedBaseTypes.ContainsKey(type))
                            continue;
                        if (type.BaseType != null && excludedBaseTypes.ContainsKey(type.BaseType))
                            continue;
                    }

                    result.Add(type);
                }
            }
            catch (Exception)
            {}

        return (Type[])result.ToArray(typeof(Type));
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="methodInfo"></param>
    /// <returns></returns>
    public static bool SkipMethod(MethodInfo methodInfo)
    {
        if (methodInfo.ReturnType == typeof(IAsyncResult))
            return true;
        foreach (var parameterInfo in methodInfo.GetParameters())
            if (parameterInfo.ParameterType == typeof(IAsyncResult))
                return true;
        return false;
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static string GetDescription(Type type)
    {
        var attribute = ReflectionUtils.GetAttribute(typeof(DescriptionAttribute), type, false);
        return (attribute as DescriptionAttribute)?.Description;
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="methodInfo"></param>
    /// <returns></returns>
    public static string GetDescription(MethodInfo methodInfo)
    {
        var attribute = ReflectionUtils.GetAttribute(typeof(DescriptionAttribute), methodInfo, false);
        return (attribute as DescriptionAttribute)?.Description;
    }

    internal static void NarrowValues(object[] values, ParameterInfo[] parameterInfos)
    {
        //Narrow down convertibe types (double for example)
        for (var i = 0; values != null && i < values.Length; i++)
        {
            var value = values[i];
            values[i] = ChangeType(value, parameterInfos[i].ParameterType);
        }
    }

    internal static object GetNullValue(Type type)
    {
        if (type == null) throw new ArgumentNullException("type");

        if (AMFConfiguration.Instance.NullableValues != null)
            if (AMFConfiguration.Instance.NullableValues.ContainsKey(type))
                return AMFConfiguration.Instance.NullableValues[type];
        if (type.IsValueType)
        {
            /* Not supported
            if (type.IsEnum)
                return GetEnumNullValue(type);
            */
            if (type.IsPrimitive)
            {
                if (type == typeof(int)) return 0;
                if (type == typeof(double)) return (double)0;
                if (type == typeof(short)) return (short)0;
                if (type == typeof(bool)) return false;
                if (type == typeof(sbyte)) return (sbyte)0;
                if (type == typeof(long)) return (long)0;
                if (type == typeof(byte)) return (byte)0;
                if (type == typeof(ushort)) return (ushort)0;
                if (type == typeof(uint)) return (uint)0;
                if (type == typeof(ulong)) return (ulong)0;
                if (type == typeof(float)) return (float)0;
                if (type == typeof(char)) return new char();
            }
            else
            {
                if (type == typeof(DateTime)) return DateTime.MinValue;
                if (type == typeof(decimal)) return 0m;
                if (type == typeof(Guid)) return Guid.Empty;
            }
        }
        else
        {
            if (type == typeof(string)) return null;// string.Empty;
            if (type == typeof(DBNull)) return DBNull.Value;
        }

        return null;
    }

    internal static object CreateInstance(Type type)
    {
        //Is this a generic type definition?
        if (ReflectionUtils.IsGenericType(type))
        {
            var genericTypeDefinition = ReflectionUtils.GetGenericTypeDefinition(type);
            // Get the generic type parameters or type arguments.
            var typeParameters = ReflectionUtils.GetGenericArguments(type);

            // Construct an array of type arguments to substitute for 
            // the type parameters of the generic class.
            // The array must contain the correct number of types, in 
            // the same order that they appear in the type parameter 
            // list.
            // Construct the type Dictionary<String, Example>.
            var constructed = ReflectionUtils.MakeGenericType(genericTypeDefinition, typeParameters);
            return Activator.CreateInstance(constructed);
        }

        return Activator.CreateInstance(type);
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <returns></returns>
    public static string[] GetLacLocations()
    {
        var lacLocations = new ArrayList();

        try
        {
            //This is the AMFCore path
            try
            {
                var location = Path.GetDirectoryName(AppContext.BaseDirectory);
                if (location != null)
                    lacLocations.Add(location);
            }
            catch (SecurityException)
            {}

            try
            {
                if (AppDomain.CurrentDomain.DynamicDirectory != null)
                {
                    //Uri uri = new Uri(AppDomain.CurrentDomain.DynamicDirectory);
                    var dynamicDirectory = Path.GetDirectoryName(AppDomain.CurrentDomain.DynamicDirectory);
                    lacLocations.Add(dynamicDirectory);
                }
            }
            catch (SecurityException)
            {}
        }
        catch (Exception)
        {}

        return lacLocations.ToArray(typeof(string)) as string[];
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static bool GetTypeIsAccessible(Type type)
    {
        if (type == null || type.Assembly == typeof(TypeHelper).Assembly)
            return false;
        return true;
    }

    /// <summary>
    ///     Returns the underlying type argument of the specified type.
    /// </summary>
    /// <param name="type">A <see cref="System.Type" /> instance. </param>
    /// <returns>
    ///     <list>
    ///         <item>
    ///             The type argument of the type parameter,
    ///             if the type parameter is a closed generic nullable type.
    ///         </item>
    ///         <item>The underlying Type of enumType, if the type parameter is an enum type.</item>
    ///         <item>Otherwise, the type itself.</item>
    ///     </list>
    /// </returns>
    public static Type GetUnderlyingType(Type type)
    {
        if (type == null) throw new ArgumentNullException("type");

        if (ReflectionUtils.IsNullable(type))
            type = type.GetGenericArguments()[0];

        if (type.IsEnum)
            type = Enum.GetUnderlyingType(type);

        return type;
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static string GetCSharpName(Type type)
    {
        var dimensions = 0;
        while (type.IsArray)
        {
            type = type.GetElementType();
            dimensions++;
        }

        var sb = new StringBuilder();
        sb.Append(type.Namespace);
        sb.Append(".");

        var parameters = Type.EmptyTypes;
        if (ReflectionUtils.IsGenericType(type))
            if (ReflectionUtils.GetGenericArguments(type) != null)
                parameters = ReflectionUtils.GetGenericArguments(type);
        GetCSharpName(type, parameters, 0, sb);
        for (var i = 0; i < dimensions; i++) sb.Append("[]");
        return sb.ToString();
    }

    private static int GetCSharpName(Type type, Type[] parameters, int index, StringBuilder sb)
    {
        if (type.DeclaringType != null && type.DeclaringType != type)
        {
            index = GetCSharpName(type.DeclaringType, parameters, index, sb);
            sb.Append(".");
        }

        var name = type.Name;
        var length = name.IndexOf('`');
        if (length < 0)
            length = name.IndexOf('!');
        if (length > 0)
        {
            sb.Append(name.Substring(0, length));
            sb.Append("<");
            var paramCount = int.Parse(name.Substring(length + 1), CultureInfo.InvariantCulture) +
                             index;
            while (index < paramCount)
            {
                sb.Append(GetCSharpName(parameters[index]));
                if (index < paramCount - 1) sb.Append(",");
                index++;
            }

            sb.Append(">");
            return index;
        }

        sb.Append(name);
        return index;
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="obj"></param>
    /// <param name="targetType"></param>
    /// <returns></returns>
    public static bool IsAssignable(object obj, Type targetType) => IsAssignable(obj, targetType, ReflectionUtils.IsNullable(targetType));

    private static bool IsAssignable(object obj, Type targetType, bool isNullable)
    {
        if (obj != null && targetType.IsAssignableFrom(obj.GetType()))
            return true;//targetType can be assigned from an instance of the obj's Type
        if (isNullable && obj == null)
            return true;//null is assignable to a nullable type
        if (targetType.IsArray)
        {
            if (null == obj)
                return true;
            var srcType = obj.GetType();

            if (srcType == targetType)
                return true;

            if (srcType.IsArray)
            {
                var srcElementType = srcType.GetElementType();
                var dstElementType = targetType.GetElementType();

                if (srcElementType.IsArray != dstElementType.IsArray
                    || srcElementType.IsArray &&
                    srcElementType.GetArrayRank() != dstElementType.GetArrayRank())
                    return false;

                var srcArray = (Array)obj;
                var rank = srcArray.Rank;
                if (rank == 1 && 0 == srcArray.GetLowerBound(0))
                {
                    var arrayLength = srcArray.Length;
                    // Int32 is assignable from UInt32, SByte from Byte and so on.
                    if (dstElementType.IsAssignableFrom(srcElementType))
                        return true;
                    //This is a costly operation
                    for (var i = 0; i < arrayLength; ++i)
                        if (!IsAssignable(srcArray.GetValue(i), dstElementType))
                            return false;
                }
                else
                {
                    //This is a costly operation
                    var arrayLength = 1;
                    var dimensions = new int[rank];
                    var indices = new int[rank];
                    var lbounds = new int[rank];

                    for (var i = 0; i < rank; ++i)
                    {
                        arrayLength *= dimensions[i] = srcArray.GetLength(i);
                        lbounds[i] = srcArray.GetLowerBound(i);
                    }

                    for (var i = 0; i < arrayLength; ++i)
                    {
                        var index = i;
                        for (var j = rank - 1; j >= 0; --j)
                        {
                            indices[j] = index % dimensions[j] + lbounds[j];
                            index /= dimensions[j];
                        }

                        if (!IsAssignable(srcArray.GetValue(indices), dstElementType))
                            return false;
                    }
                }

                return true;
            }
        }
        else if (targetType.IsEnum)
            try
            {
                Enum.Parse(targetType, obj.ToString(), true);
                return true;
            }
            catch (ArgumentException)
            {
                return false;
            }

        if (obj != null)
        {
            var typeConverter = ReflectionUtils.GetTypeConverter(obj);//TypeDescriptor.GetConverter(obj);
            if (typeConverter != null && typeConverter.CanConvertTo(targetType))
                return true;
            typeConverter = ReflectionUtils.GetTypeConverter(targetType);// TypeDescriptor.GetConverter(targetType);
            if (typeConverter != null && typeConverter.CanConvertFrom(obj.GetType()))
                return true;

            //Collections
            if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.Generic.ICollection`1") &&
                obj is IList)
            {
                //For generic interfaces, the name parameter is the mangled name, ending with a grave accent (`) and the number of type parameters
                var typeParameters = ReflectionUtils.GetGenericArguments(targetType);
                if (typeParameters is { Length: 1 })
                {
                    //For generic interfaces, the name parameter is the mangled name, ending with a grave accent (`) and the number of type parameters
                    var typeGenericICollection =
                        targetType.GetInterface("System.Collections.Generic.ICollection`1", true);
                    return typeGenericICollection != null;
                }

                return false;
            }

            if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.IList") && obj is IList)
                return true;

            if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.Generic.IDictionary`2") &&
                obj is IDictionary)
            {
                var typeParameters = ReflectionUtils.GetGenericArguments(targetType);
                if (typeParameters is { Length: 2 })
                {
                    //For generic interfaces, the name parameter is the mangled name, ending with a grave accent (`) and the number of type parameters
                    var typeGenericIDictionary =
                        targetType.GetInterface("System.Collections.Generic.IDictionary`2", true);
                    return typeGenericIDictionary != null;
                }

                return false;
            }

            if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.IDictionary") &&
                obj is IDictionary) return true;
            //return false;
        }
        else
        {
            if (targetType.IsValueType)
            {
                if (AMFConfiguration.Instance.AcceptNullValueTypes)
                    // Any value-type that is not explicitly initialized with a value will 
                    // contain the default value for that object type.
                    return true;
                return false;
            }

            return true;
        }

        try
        {
            if (isNullable)
                switch (Type.GetTypeCode(GetUnderlyingType(targetType)))
                {
                    case TypeCode.Char: return CanConvertToNullableChar(obj);
                }

            switch (Type.GetTypeCode(targetType))
            {
                case TypeCode.Char: return CanConvertToChar(obj);
            }
        }
        catch (InvalidCastException)
        {}

        if (typeof(XDocument) == targetType && obj is XmlDocument) return true;
        if (typeof(XElement)  == targetType && obj is XmlDocument) return true;

        return false;
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="value"></param>
    /// <param name="targetType"></param>
    /// <returns></returns>
    public static object ChangeType(object value, Type targetType) => ConvertChangeType(value, targetType, ReflectionUtils.IsNullable(targetType));

    private static object ConvertChangeType(object value, Type targetType, bool isNullable)
    {
        if (targetType.IsArray)
        {
            if (null == value)
                return null;

            var srcType = value.GetType();

            if (srcType == targetType)
                return value;

            if (srcType.IsArray)
            {
                var srcElementType = srcType.GetElementType();
                var dstElementType = targetType.GetElementType();

                if (srcElementType.IsArray != dstElementType.IsArray
                    || srcElementType.IsArray &&
                    srcElementType.GetArrayRank() != dstElementType.GetArrayRank())
                    throw new InvalidCastException(string.Format("Can not convert array of type '{0}' to array of '{1}'.", srcType.FullName,
                    targetType.FullName));

                var srcArray = (Array)value;
                Array dstArray;

                var rank = srcArray.Rank;

                if (rank == 1 && 0 == srcArray.GetLowerBound(0))
                {
                    var arrayLength = srcArray.Length;

                    dstArray = Array.CreateInstance(dstElementType, arrayLength);

                    // Int32 is assignable from UInt32, SByte from Byte and so on.
                    //
                    if (dstElementType.IsAssignableFrom(srcElementType))
                        Array.Copy(srcArray, dstArray, arrayLength);
                    else
                        for (var i = 0; i < arrayLength; ++i)
                            dstArray.SetValue(ConvertChangeType(srcArray.GetValue(i), dstElementType, isNullable), i);
                }
                else
                {
                    var arrayLength = 1;
                    var dimensions = new int[rank];
                    var indices = new int[rank];
                    var lbounds = new int[rank];

                    for (var i = 0; i < rank; ++i)
                    {
                        arrayLength *= dimensions[i] = srcArray.GetLength(i);
                        lbounds[i] = srcArray.GetLowerBound(i);
                    }

                    dstArray = Array.CreateInstance(dstElementType, dimensions, lbounds);
                    for (var i = 0; i < arrayLength; ++i)
                    {
                        var index = i;
                        for (var j = rank - 1; j >= 0; --j)
                        {
                            indices[j] = index % dimensions[j] + lbounds[j];
                            index /= dimensions[j];
                        }

                        dstArray.SetValue(ConvertChangeType(srcArray.GetValue(indices), dstElementType, isNullable),
                        indices);
                    }
                }

                return dstArray;
            }
        }
        else if (targetType.IsEnum)
            try
            {
                return Enum.Parse(targetType, value.ToString(), true);
            }
            catch (ArgumentException ex)
            {
                throw new InvalidCastException(__Res.GetString(__Res.TypeHelper_ConversionFail), ex);
            }

        if (isNullable)
        {
            switch (Type.GetTypeCode(GetUnderlyingType(targetType)))
            {
                case TypeCode.Boolean:  return ConvertToNullableBoolean(value);
                case TypeCode.Byte:     return ConvertToNullableByte(value);
                case TypeCode.Char:     return ConvertToNullableChar(value);
                case TypeCode.DateTime: return ConvertToNullableDateTime(value);
                case TypeCode.Decimal:  return ConvertToNullableDecimal(value);
                case TypeCode.Double:   return ConvertToNullableDouble(value);
                case TypeCode.Int16:    return ConvertToNullableInt16(value);
                case TypeCode.Int32:    return ConvertToNullableInt32(value);
                case TypeCode.Int64:    return ConvertToNullableInt64(value);
                case TypeCode.SByte:    return ConvertToNullableSByte(value);
                case TypeCode.Single:   return ConvertToNullableSingle(value);
                case TypeCode.UInt16:   return ConvertToNullableUInt16(value);
                case TypeCode.UInt32:   return ConvertToNullableUInt32(value);
                case TypeCode.UInt64:   return ConvertToNullableUInt64(value);
            }

            if (typeof(Guid) == targetType) return ConvertToNullableGuid(value);
        }

        switch (Type.GetTypeCode(targetType))
        {
            case TypeCode.Boolean:  return ConvertToBoolean(value);
            case TypeCode.Byte:     return ConvertToByte(value);
            case TypeCode.Char:     return ConvertToChar(value);
            case TypeCode.DateTime: return ConvertToDateTime(value);
            case TypeCode.Decimal:  return ConvertToDecimal(value);
            case TypeCode.Double:   return ConvertToDouble(value);
            case TypeCode.Int16:    return ConvertToInt16(value);
            case TypeCode.Int32:    return ConvertToInt32(value);
            case TypeCode.Int64:    return ConvertToInt64(value);
            case TypeCode.SByte:    return ConvertToSByte(value);
            case TypeCode.Single:   return ConvertToSingle(value);
            case TypeCode.String:   return ConvertToString(value);
            case TypeCode.UInt16:   return ConvertToUInt16(value);
            case TypeCode.UInt32:   return ConvertToUInt32(value);
            case TypeCode.UInt64:   return ConvertToUInt64(value);
        }

        if (typeof(Guid)        == targetType) return ConvertToGuid(value);
        if (typeof(XmlDocument) == targetType) return ConvertToXmlDocument(value);
        if (typeof(XDocument)   == targetType) return ConvertToXDocument(value);
        if (typeof(XElement)    == targetType) return ConvertToXElement(value);
        if (typeof(byte[])      == targetType) return ConvertToByteArray(value);
        if (typeof(char[])      == targetType) return ConvertToCharArray(value);

        if (value == null)
            return null;
        //Check whether the target Type can be assigned from the value's Type
        if (targetType.IsAssignableFrom(value.GetType()))
            return value;//Skip further adapting

        //Try to convert using a type converter
        var typeConverter = ReflectionUtils.GetTypeConverter(targetType);// TypeDescriptor.GetConverter(targetType);
        if (typeConverter != null && typeConverter.CanConvertFrom(value.GetType()))
            return typeConverter.ConvertFrom(value);
        //Custom type converters handled here (for example ByteArray)
        typeConverter = ReflectionUtils.GetTypeConverter(value);// TypeDescriptor.GetConverter(value);
        if (typeConverter != null && typeConverter.CanConvertTo(targetType))
            return typeConverter.ConvertTo(value, targetType);

        if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.Generic.ICollection`1") &&
            value is IList)
        {
            var obj = CreateInstance(targetType);
            if (obj != null)
            {
                //For generic interfaces, the name parameter is the mangled name, ending with a grave accent (`) and the number of type parameters
                var typeParameters = ReflectionUtils.GetGenericArguments(targetType);
                if (typeParameters is { Length: 1 })
                {
                    //For generic interfaces, the name parameter is the mangled name, ending with a grave accent (`) and the number of type parameters
                    var typeGenericICollection =
                        targetType.GetInterface("System.Collections.Generic.ICollection`1", true);
                    var miAddCollection = targetType.GetMethod("Add");
                    var source = value as IList;
                    for (var i = 0; i < (value as IList).Count; i++)
                        miAddCollection.Invoke(obj, [ChangeType(source[i], typeParameters[0])]);
                }

                return obj;
            }
        }

        if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.IList") && value is IList)
        {
            var obj = CreateInstance(targetType);
            if (obj != null)
            {
                var source = value as IList;
                var destination = obj as IList;
                for (var i = 0; i < source.Count; i++)
                    destination.Add(source[i]);
                return obj;
            }
        }

        if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.Generic.IDictionary`2") &&
            value is IDictionary)
        {
            var obj = CreateInstance(targetType);
            if (obj != null)
            {
                var source = value as IDictionary;
                var typeParameters = ReflectionUtils.GetGenericArguments(targetType);
                if (typeParameters is { Length: 2 })
                {
                    //For generic interfaces, the name parameter is the mangled name, ending with a grave accent (`) and the number of type parameters
                    var typeGenericIDictionary =
                        targetType.GetInterface("System.Collections.Generic.IDictionary`2", true);
                    var miAddCollection = targetType.GetMethod("Add");
                    var dictionary = value as IDictionary;
                    foreach (DictionaryEntry entry in dictionary)
                        miAddCollection.Invoke(obj, [ChangeType(entry.Key, typeParameters[0]), ChangeType(entry.Value, typeParameters[1])]);
                }

                return obj;
            }
        }

        if (ReflectionUtils.ImplementsInterface(targetType, "System.Collections.IDictionary") && value is IDictionary)
        {
            var obj = CreateInstance(targetType);
            if (obj != null)
            {
                var source = value as IDictionary;
                var destination = obj as IDictionary;
                foreach (DictionaryEntry entry in source)
                    destination.Add(entry.Key, entry.Value);
                return obj;
            }
        }

        return System.Convert.ChangeType(value, targetType, null);
    }

    #region Nullable Types

    public static sbyte? ConvertToNullableSByte(object value)
    {
        if (value is sbyte) return (sbyte?)value;
        if (value == null) return null;
        return Util.Convert.ToNullableSByte(value);
    }

    public static short? ConvertToNullableInt16(object value)
    {
        if (value is short) return (short?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableInt16(value);
    }

    public static int? ConvertToNullableInt32(object value)
    {
        if (value is int) return (int?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableInt32(value);
    }

    public static long? ConvertToNullableInt64(object value)
    {
        if (value is long) return (long?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableInt64(value);
    }

    public static byte? ConvertToNullableByte(object value)
    {
        if (value is byte) return (byte?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableByte(value);
    }


    public static ushort? ConvertToNullableUInt16(object value)
    {
        if (value is ushort) return (ushort?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableUInt16(value);
    }


    public static uint? ConvertToNullableUInt32(object value)
    {
        if (value is uint) return (uint?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableUInt32(value);
    }


    public static ulong? ConvertToNullableUInt64(object value)
    {
        if (value is ulong) return (ulong?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableUInt64(value);
    }

    public static char? ConvertToNullableChar(object value)
    {
        if (value is char) return (char?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableChar(value);
    }

    public static bool CanConvertToNullableChar(object value)
    {
        if (value is char) return true;
        if (value == null) return true;
        return Util.Convert.CanConvertToNullableChar(value);
    }

    public static double? ConvertToNullableDouble(object value)
    {
        if (value is double) return (double?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableDouble(value);
    }

    public static float? ConvertToNullableSingle(object value)
    {
        if (value is float) return (float?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableSingle(value);
    }

    public static bool? ConvertToNullableBoolean(object value)
    {
        if (value is bool) return (bool?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableBoolean(value);
    }

    public static DateTime? ConvertToNullableDateTime(object value)
    {
        if (value is DateTime) return (DateTime?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableDateTime(value);
    }

    public static decimal? ConvertToNullableDecimal(object value)
    {
        if (value is decimal) return (decimal?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableDecimal(value);
    }

    public static Guid? ConvertToNullableGuid(object value)
    {
        if (value is Guid) return (Guid?)value;
        if (value == null) return null;

        return Util.Convert.ToNullableGuid(value);
    }

    #endregion

    #region Primitive Types

    private static readonly sbyte _defaultSByteNullValue;

    public static sbyte ConvertToSByte(object value) => value as sbyte? ??
                                                        (value == null ? _defaultSByteNullValue : Util.Convert.ToSByte(value));

    private static readonly short _defaultInt16NullValue;

    public static short ConvertToInt16(object value) => value as short? ??
                                                        (value == null ? _defaultInt16NullValue : Util.Convert.ToInt16(value));

    private static readonly int _defaultInt32NullValue;

    public static int ConvertToInt32(object value) => value as int? ??
                                                      (value == null ? _defaultInt32NullValue : Util.Convert.ToInt32(value));

    private static readonly long _defaultInt64NullValue;

    public static long ConvertToInt64(object value) => value as long? ??
                                                       (value == null ? _defaultInt64NullValue : Util.Convert.ToInt64(value));

    private static readonly byte _defaultByteNullValue;

    public static byte ConvertToByte(object value) => value as byte? ?? (value == null ? _defaultByteNullValue : Util.Convert.ToByte(value));

    private static readonly ushort _defaultUInt16NullValue;

    public static ushort ConvertToUInt16(object value) => value as ushort? ??
                                                          (value == null ? _defaultUInt16NullValue : Util.Convert.ToUInt16(value));

    private static readonly uint _defaultUInt32NullValue;

    public static uint ConvertToUInt32(object value) => value as uint? ??
                                                        (value == null ? _defaultUInt32NullValue : Util.Convert.ToUInt32(value));

    private static readonly ulong _defaultUInt64NullValue;

    public static ulong ConvertToUInt64(object value) => value as ulong? ??
                                                         (value == null ? _defaultUInt64NullValue : Util.Convert.ToUInt64(value));

    private static readonly char _defaultCharNullValue;

    public static char ConvertToChar(object value) => value as char? ?? (value == null ? _defaultCharNullValue : Util.Convert.ToChar(value));

    public static bool CanConvertToChar(object value) => value is char ? true :
                                                         value == null ? true :
                                                                         Util.Convert.CanConvertToChar(value);

    private static readonly float _defaultSingleNullValue;

    public static float ConvertToSingle(object value) => value as float? ??
                                                         (value == null ? _defaultSingleNullValue : Util.Convert.ToSingle(value));

    private static readonly double _defaultDoubleNullValue;

    public static double ConvertToDouble(object value) => value as double? ??
                                                          (value == null ? _defaultDoubleNullValue : Util.Convert.ToDouble(value));

    private static readonly bool _defaultBooleanNullValue;

    public static bool ConvertToBoolean(object value) => value as bool? ??
                                                         (value == null ? _defaultBooleanNullValue : Util.Convert.ToBoolean(value));

    #endregion

    #region Simple Types

    private static readonly string _defaultStringNullValue;

    public static string ConvertToString(object value) => value is string ? (string)value :
                                                          value == null   ? _defaultStringNullValue :
                                                                            Util.Convert.ToString(value);

    private static readonly DateTime _defaultDateTimeNullValue;

    public static DateTime ConvertToDateTime(object value) => value as DateTime? ?? (value == null
                                                                                         ? _defaultDateTimeNullValue
                                                                                         : Util.Convert.ToDateTime(value));

    private static readonly decimal _defaultDecimalNullValue;

    public static decimal ConvertToDecimal(object value) => value as decimal? ??
                                                            (value == null ? _defaultDecimalNullValue : Util.Convert.ToDecimal(value));

    private static readonly Guid _defaultGuidNullValue;

    public static Guid ConvertToGuid(object value) => value as Guid? ?? (value == null ? _defaultGuidNullValue : Util.Convert.ToGuid(value));

    private static readonly XmlReader _defaultXmlReaderNullValue;

    public static XmlReader ConvertToXmlReader(object value) => value is XmlReader ? (XmlReader)value :
                                                                value == null      ? _defaultXmlReaderNullValue :
                                                                                     Util.Convert.ToXmlReader(value);

    private static readonly XmlDocument _defaultXmlDocumentNullValue;

    public static XmlDocument ConvertToXmlDocument(object value) => value is XmlDocument ? (XmlDocument)value :
                                                                    value == null        ? _defaultXmlDocumentNullValue :
                                                                                           Util.Convert.ToXmlDocument(value);

    private static readonly XDocument _defaultXDocumentNullValue;

    public static XDocument ConvertToXDocument(object value) => value is XDocument ? (XDocument)value :
                                                                value == null      ? _defaultXDocumentNullValue :
                                                                                     Util.Convert.ToXDocument(value);

    private static readonly XElement _defaultXElementNullValue;

    public static XElement ConvertToXElement(object value) => value is XElement ? (XElement)value :
                                                              value == null     ? _defaultXElementNullValue :
                                                                                  Util.Convert.ToXElement(value);

    public static byte[] ConvertToByteArray(object value) => value is byte[] ? (byte[])value :
                                                             value == null   ? null :
                                                                               Util.Convert.ToByteArray(value);

    public static char[] ConvertToCharArray(object value) => value is char[] ? (char[])value :
                                                             value == null   ? null :
                                                                               Util.Convert.ToCharArray(value);

    #endregion
}