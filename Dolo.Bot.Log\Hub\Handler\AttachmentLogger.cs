using Dolo.Core.Discord;

namespace Dolo.Bot.Log.Hub.Handler;

public static class AttachmentLogger
{
    /// <summary>
    /// Log attachments when a message with attachments is created
    /// </summary>
    public static async Task LogAttachmentsCreatedAsync(DiscordMessage message)
    {
        if (HubChannel.Attachments is null || !message.Attachments.Any())
            return;

        // Skip bot messages
        if (message.Author?.IsBot == true)
            return;

        try
        {
            var attachments = message.Attachments.Select(a => new HubCache.CachedAttachment
            {
                FileName = a.FileName,
                OriginalUrl = a.Url,
                ProxyUrl = a.ProxyUrl,
                Size = a.FileSize,
                Width = a.Width,
                Height = a.Height,
                ContentType = a.MediaType ?? "unknown"
            }).ToList();

            var embed = HubEmbed.AttachmentsCreated(message, attachments);
            await HubChannel.Attachments.TrySendMessageAsync(embed);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Log] Error logging attachments created: {ex.Message}");
        }
    }
}
