using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class Header : ComponentBase
{
    [Parameter] public EventCallback OnLiveExchangeToggle { get; set; }
    [Parameter] public bool IsLiveExchangeActive { get; set; } = false;

    private async Task ToggleLiveExchange()
    {
        await OnLiveExchangeToggle.InvokeAsync();
    }

    private string GetLiveExchangeStatusColor()
    {
        return IsLiveExchangeActive ? "bg-success" : "bg-span-muted";
    }
}
