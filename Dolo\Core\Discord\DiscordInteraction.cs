﻿#pragma warning disable CS8604

using DSharpPlus.Commands.Processors.SlashCommands;

namespace Dolo.Core.Discord
{
    /// <summary>
    /// Provides extension methods for Discord interactions to simplify common operations and add error handling.
    /// </summary>
    public static class DiscordInteractionExtension
    {
        /// <summary>
        /// Attempts to edit the response of a slash command.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="msg">The webhook builder containing the new message content.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        /// <returns>The edited message, or null if the operation failed.</returns>
        public static async Task<DiscordMessage?> TryEditResponseAsync(this SlashCommandContext context, DiscordWebhookBuilder msg, Action<Exception>? onError = null)
            => await TryIt.TryValueTaskAsync(() => context.EditResponseAsync(msg), onError);

        /// <summary>
        /// Attempts to send a follow-up message to a slash command.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="msg">The follow-up message builder containing the message content.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        /// <returns>The sent follow-up message, or null if the operation failed.</returns>
        public static async Task<DiscordMessage?> TryFollowUpAsync(this SlashCommandContext context, DiscordFollowupMessageBuilder msg, Action<Exception>? onError = null)
            => await TryIt.TryValueTaskAsync(() => context.FollowupAsync(msg), onError);

        /// <summary>
        /// Attempts to edit the response of a slash command with a simple text content.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="content">The new text content of the message.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        /// <returns>The edited message, or null if the operation failed.</returns>
        public static async Task<DiscordMessage?> TryEditResponseAsync(this SlashCommandContext context, string? content, Action<Exception>? onError = null)
            => await TryIt.TryValueTaskAsync(() => context.EditResponseAsync(new DiscordWebhookBuilder().WithContent(content)), onError);

        /// <summary>
        /// Attempts to edit the response of a slash command with an embed.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="embed">The new embed to be sent.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        /// <returns>The edited message, or null if the operation failed.</returns>
        public static async Task<DiscordMessage?> TryEditResponseAsync(this SlashCommandContext context, DiscordEmbed embed, Action<Exception>? onError = null)
            => await TryIt.TryValueTaskAsync(() => context.EditResponseAsync(new DiscordWebhookBuilder().AddEmbed(embed)), onError);

        /// <summary>
        /// Attempts to create a response to a slash command with an embed.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="embed">The embed to be sent.</param>
        /// <param name="ephemeral">Whether the message should be ephemeral (only visible to the command user).</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateResponseAsync(this SlashCommandContext context, DiscordEmbed embed, bool ephemeral = false, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => context.Interaction.CreateResponseAsync(
               DiscordInteractionResponseType.ChannelMessageWithSource,
               new DiscordInteractionResponseBuilder()
                   .AddEmbed(embed)
                   .AsEphemeral(ephemeral)), onError);

        /// <summary>
        /// Attempts to create a response to a slash command with a custom interaction response builder.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="builder">The custom interaction response builder.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateResponseAsync(this SlashCommandContext context, DiscordInteractionResponseBuilder builder, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => context.Interaction.CreateResponseAsync(
               DiscordInteractionResponseType.ChannelMessageWithSource,
               builder), onError);

        /// <summary>
        /// Attempts to create a response to a slash command with a custom response type and builder.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="type">The type of interaction response.</param>
        /// <param name="builder">The custom interaction response builder.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateResponseAsync(this SlashCommandContext context, DiscordInteractionResponseType type, DiscordInteractionResponseBuilder builder, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => context.Interaction.CreateResponseAsync(type, builder), onError);

        /// <summary>
        /// Attempts to create a response to a slash command with a simple text content.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="content">The text content of the message.</param>
        /// <param name="ephemeral">Whether the message should be ephemeral (only visible to the command user).</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateResponseAsync(this SlashCommandContext context, string content, bool ephemeral = false, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => context.Interaction.CreateResponseAsync(
               DiscordInteractionResponseType.ChannelMessageWithSource,
               new DiscordInteractionResponseBuilder()
                   .WithContent(content)
                   .AsEphemeral(ephemeral)), onError);

        /// <summary>
        /// Attempts to delete the response to a slash command.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryDeleteResponseAsync(this SlashCommandContext context, Action<Exception>? onError = null)
            => await TryIt.TryValueTaskAsync(context.DeleteResponseAsync, onError);

        /// <summary>
        /// Attempts to delete a follow-up message sent after a slash command.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="id">The ID of the follow-up message to delete.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryDeleteFollowupMessageAsync(this DiscordInteraction interaction, ulong id, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.DeleteFollowupMessageAsync(id), onError);

        /// <summary>
        /// Attempts to create a response message for a Discord interaction.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="type">The type of interaction response.</param>
        /// <param name="builder">The custom interaction response builder.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateResponseMessageAsync(this DiscordInteraction interaction,
            DiscordInteractionResponseType type,
            DiscordInteractionResponseBuilder builder,
            Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.CreateResponseAsync(type, builder), onError);

        /// <summary>
        /// Attempts to create a response message for a Discord interaction with only a response type.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="type">The type of interaction response.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateResponseMessageAsync(this DiscordInteraction interaction, DiscordInteractionResponseType type, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.CreateResponseAsync(type), onError);

        /// <summary>
        /// Attempts to create a response message for a Discord interaction with simple text content.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="type">The type of interaction response.</param>
        /// <param name="content">The text content of the message.</param>
        /// <param name="isEphemeral">Whether the message should be ephemeral (only visible to the interaction user).</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateResponseMessageAsync(this DiscordInteraction interaction, DiscordInteractionResponseType type, string content, bool isEphemeral = false, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.CreateResponseAsync(type, new DiscordInteractionResponseBuilder()
                .WithContent(content)
                .AsEphemeral(isEphemeral)), onError);

        /// <summary>
        /// Attempts to create a follow-up message for a Discord interaction.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="builder">The follow-up message builder.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        /// <returns>The sent follow-up message, or null if the operation failed.</returns>
        public static async Task<DiscordMessage?> TryCreateFollowupMessageAsync(this DiscordInteraction interaction,
            DiscordFollowupMessageBuilder builder, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.CreateFollowupMessageAsync(builder), onError);

        /// <summary>
        /// Attempts to delete the original response message of a Discord interaction.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryDeleteOriginalMessageAsync(this DiscordInteraction interaction, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(interaction.DeleteOriginalResponseAsync, onError);

        /// <summary>
        /// Attempts to create a follow-up message for a Discord interaction with simple text content.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="content">The text content of the message.</param>
        /// <param name="isEphemeral">Whether the message should be ephemeral (only visible to the interaction user).</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        /// <returns>The sent follow-up message, or null if the operation failed.</returns>
        public static async Task<DiscordMessage?> TryCreateFollowupMessageAsync(this DiscordInteraction interaction, string content, bool isEphemeral = false, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.CreateFollowupMessageAsync(new DiscordFollowupMessageBuilder()
                .WithContent(content)
                .AsEphemeral(isEphemeral)), onError);

        /// <summary>
        /// Attempts to edit a follow-up message of a Discord interaction with simple text content.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="id">The ID of the follow-up message to edit.</param>
        /// <param name="content">The new text content of the message.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryEditFollowupMessageAsync(this DiscordInteraction interaction, ulong id, string content, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.EditFollowupMessageAsync(id, new DiscordWebhookBuilder()
                   .WithContent(content)), onError);

        /// <summary>
        /// Attempts to edit a follow-up message of a Discord interaction with a custom webhook builder.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="id">The ID of the follow-up message to edit.</param>
        /// <param name="builder">The custom webhook builder containing the new message content.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryEditFollowupMessageAsync(this DiscordInteraction interaction, ulong id, DiscordWebhookBuilder builder, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.EditFollowupMessageAsync(id, builder), onError);

        /// <summary>
        /// Attempts to defer the response to a slash command.
        /// </summary>
        /// <param name="context">The slash command context.</param>
        /// <param name="isEphemeral">Whether the deferred response should be ephemeral (only visible to the command user).</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryDeferAsync(this SlashCommandContext context, bool isEphemeral = false, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(async () => await context.DeferResponseAsync(isEphemeral), onError);

        /// <summary>
        /// Attempts to create a modal response for a Discord interaction.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="modal">The modal to be displayed.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryCreateModalResponseAsync(this DiscordInteraction interaction, DiscordInteractionResponseBuilder modal, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.CreateResponseAsync(DiscordInteractionResponseType.Modal, modal), onError);

        /// <summary>
        /// Attempts to acknowledge a Discord interaction without sending a message.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        public static async Task TryAcknowledgeAsync(this DiscordInteraction interaction, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.CreateResponseAsync(DiscordInteractionResponseType.DeferredMessageUpdate), onError);

        /// <summary>
        /// Attempts to update the original response of a Discord interaction with new content.
        /// </summary>
        /// <param name="interaction">The Discord interaction.</param>
        /// <param name="content">The new content for the message.</param>
        /// <param name="onError">Optional action to handle exceptions.</param>
        /// <returns>The updated message, or null if the operation failed.</returns>
        public static async Task<DiscordMessage?> TryUpdateOriginalResponseAsync(this DiscordInteraction interaction, string content, Action<Exception>? onError = null)
            => await TryIt.ThisAsync(() => interaction.EditOriginalResponseAsync(
                new DiscordWebhookBuilder().WithContent(content)), onError);

                /// <summary>
                /// Attempts to update the original response of a Discord interaction with a custom webhook builder.
                /// </summary>
                /// <param name="interaction">The Discord interaction.</param>
                /// <param name="builder">The webhook builder containing the new message content.</param>
                /// <param name="onError">Optional action to handle exceptions.</param>
                /// <returns>The updated message, or null if the operation failed.</returns>
                public static async Task<DiscordMessage?> TryUpdateOriginalResponseAsync(this DiscordInteraction interaction, DiscordWebhookBuilder builder, Action<Exception>? onError = null)
                    => await TryIt.ThisAsync(() => interaction.EditOriginalResponseAsync(builder), onError);
            
    }
}