using System.Collections.Concurrent;
using System.Collections.Immutable;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class HttpTransactionManager : IDisposable
{
    private readonly RequestResponsePairingEngine _pairingEngine;
    private readonly ConcurrentDictionary<string, CompletedTransaction> _completedTransactions = new();
    private readonly Timer _cleanupTimer;
    private readonly Timer _statisticsTimer;
    private readonly TimeSpan _retentionPeriod;
    private readonly ILogger<HttpTransactionManager> _logger;
    private readonly ResponseDecodingService _responseDecodingService;
    private readonly PairingFileLogger? _fileLogger;
    private long _totalPaired;
    private long _totalTimeouts;
    private bool _disposed;

    public HttpTransactionManager(ILogger<HttpTransactionManager> logger, ResponseDecodingService responseDecodingService, TimeSpan? transactionTimeout = null, bool enableFileLogging = true, ILoggerFactory? loggerFactory = null) {
        _logger = logger;
        _responseDecodingService = responseDecodingService;
        _retentionPeriod = TimeSpan.FromHours(1);

        // Initialize the FIFO pairing engine with proper logger
        var pairingLogger = loggerFactory?.CreateLogger<RequestResponsePairingEngine>() ??
                           Microsoft.Extensions.Logging.LoggerFactory.Create(builder => builder.SetMinimumLevel(LogLevel.None))
                               .CreateLogger<RequestResponsePairingEngine>();
        _pairingEngine = new RequestResponsePairingEngine(pairingLogger, transactionTimeout);

        // Subscribe to pairing events
        _pairingEngine.TransactionPaired += OnTransactionPaired;
        _pairingEngine.TransactionTimeout += OnTransactionTimeout;

        if (enableFileLogging)
        {
            try
            {
                _fileLogger = new PairingFileLogger();
                _logger.LogInformation("FIFO pairing file logging enabled");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not initialize file logging: {Error}", ex.Message);
            }
        }

        _cleanupTimer = new Timer(CleanupOldTransactions, null,
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));

        _statisticsTimer = new Timer(LogStatistics, null,
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        _logger.LogInformation("HttpTransactionManager initialized with FIFO pairing engine");
    }

    public event EventHandler<HttpTransactionCompletedEventArgs>? TransactionCompleted;
    public event EventHandler<HttpTransactionTimedOutEventArgs>? TransactionTimedOut;

    private async void OnTransactionPaired(object? sender, TransactionPairedEventArgs e)
    {
        try
        {
            var transaction = e.Transaction;

            // Process the response
            ProcessedResponse? processedResponse = null;
            try
            {
                if (transaction.Response != null)
                {
                    processedResponse = await _responseDecodingService.ProcessResponseAsync(transaction.Response).ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process response for transaction {TransactionId}", transaction.TransactionId);
            }

            // Create completed transaction
            var completedTransaction = new CompletedTransaction
            {
                Transaction = transaction,
                ProcessedResponse = processedResponse,
                CompletedAt = DateTime.UtcNow,
                Status = TransactionStatus.Completed
            };

            _completedTransactions.TryAdd(transaction.TransactionId, completedTransaction);
            _fileLogger?.LogRequestResponsePair(completedTransaction);

            Interlocked.Increment(ref _totalPaired);

            // Notify subscribers
            TransactionCompleted?.Invoke(this, new HttpTransactionCompletedEventArgs(transaction));

            _logger.LogDebug("✅ Transaction {TransactionId} completed successfully", transaction.TransactionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing paired transaction: {Error}", ex.Message);
        }
    }

    private void OnTransactionTimeout(object? sender, TransactionTimeoutEventArgs e)
    {
        try
        {
            Interlocked.Increment(ref _totalTimeouts);

            _logger.LogWarning("⏰ Transaction {TransactionId} timed out: {Reason}", e.TransactionId, e.Reason);

            // Create a timeout transaction for notification
            var timeoutTransaction = new HttpTransaction
            {
                TransactionId = e.TransactionId,
                StartTime = DateTime.UtcNow.Subtract(TimeSpan.FromSeconds(30)), // Approximate
                EndTime = DateTime.UtcNow
            };

            TransactionTimedOut?.Invoke(this, new HttpTransactionTimedOutEventArgs(timeoutTransaction));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing transaction timeout: {Error}", ex.Message);
        }
    }

    /// <summary>
    /// Creates a new connection context for request-response pairing
    /// </summary>
    public string CreateConnection(string hostname, int port, string protocol = "HTTPS")
    {
        if (_disposed) return string.Empty;

        return _pairingEngine.CreateConnection(hostname, port, protocol);
    }

    /// <summary>
    /// Records a request and returns a transaction ID for correlation
    /// </summary>
    public string StartTransaction(string connectionId, IHttpInterceptedRequestMessage request)
    {
        if (_disposed) return string.Empty;

        return _pairingEngine.RecordRequest(connectionId, request);
    }

    /// <summary>
    /// Legacy method for backward compatibility - creates connection and records request
    /// </summary>
    public string StartTransaction(IHttpInterceptedRequestMessage request, string hostname, int port, string protocol)
    {
        if (_disposed) return string.Empty;

        var connectionId = CreateConnection(hostname, port, protocol);
        if (string.IsNullOrEmpty(connectionId)) return string.Empty;

        return StartTransaction(connectionId, request);
    }

    /// <summary>
    /// Pairs a response with the next request in FIFO order for the connection
    /// </summary>
    public async Task<bool> CompleteTransactionAsync(string connectionId, IHttpInterceptedResponseMessage response)
    {
        if (_disposed) return false;

        return _pairingEngine.PairResponse(connectionId, response);
    }



    /// <summary>
    /// Closes a connection and cleans up pending transactions
    /// </summary>
    public void CloseConnection(string connectionId, string reason = "Connection closed")
    {
        if (_disposed) return;

        _pairingEngine.CloseConnection(connectionId);
        _logger.LogDebug("🔌 Closed connection {ConnectionId}: {Reason}", connectionId, reason);
    }

    /// <summary>
    /// Peeks at the next transaction ID for a connection without removing it from the queue
    /// Used for breakpoint processing before response pairing
    /// </summary>
    public string? PeekNextTransactionId(string connectionId) {
        if (_disposed) return null;
        return _pairingEngine.PeekNextTransactionId(connectionId);
    }

    /// <summary>
    /// Gets the original request message for a transaction ID
    /// Used for breakpoint processing to access request URI for response breakpoints
    /// </summary>
    public IHttpInterceptedRequestMessage? GetOriginalRequest(string transactionId) {
        if (_disposed) return null;
        var pendingTransaction = _pairingEngine.GetPendingTransaction(transactionId);
        return pendingTransaction?.Request;
    }

    /// <summary>
    /// Legacy method - deprecated in favor of FIFO pairing
    /// </summary>
    public async Task CompleteTransactionAsync(IHttpInterceptedResponseMessage response, string hostname, int port, string protocol)
    {
        _logger.LogWarning("Using deprecated CompleteTransactionAsync(response, host, port) - use connection-based pairing instead");
        // Deprecated - no longer supported
    }

    /// <summary>
    /// Legacy method - deprecated in favor of FIFO pairing
    /// </summary>
    public async Task CompleteTransactionByMatchingAsync(IHttpInterceptedResponseMessage response, string hostname, int port, string protocol)
    {
        _logger.LogWarning("Using deprecated CompleteTransactionByMatchingAsync - use connection-based FIFO pairing instead");
        // Deprecated - no longer supported
    }

    /// <summary>
    /// Gets current statistics for monitoring
    /// </summary>
    public (int ActiveConnections, int PendingTransactions, long TotalPaired, long TotalTimeouts) GetStatistics()
    {
        var (activeConnections, pendingTransactions, totalPaired, totalTimeouts, _) = _pairingEngine.GetStatistics();
        return (activeConnections, pendingTransactions, totalPaired, totalTimeouts);
    }

    private void LogStatistics(object? state)
    {
        if (_disposed) return;

        try
        {
            var (activeConnections, pendingTransactions, totalPaired, totalTimeouts) = GetStatistics();

            _logger.LogInformation("HttpTransactionManager Stats: {ActiveConnections} active connections, " +
                                 "{PendingTransactions} pending, {TotalPaired} paired, {TotalTimeouts} timeouts",
                activeConnections, pendingTransactions, totalPaired, totalTimeouts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging statistics: {Error}", ex.Message);
        }
    }

    private void CleanupOldTransactions(object? state)
    {
        if (_disposed) return;

        var cutoffTime = DateTime.UtcNow - _retentionPeriod;
        var expiredTransactions = _completedTransactions.Values
            .Where(t => t.CompletedAt < cutoffTime)
            .ToList();

        foreach (var transaction in expiredTransactions)
        {
            _completedTransactions.TryRemove(transaction.Transaction.TransactionId, out _);
        }

        if (expiredTransactions.Count > 0)
        {
            _logger.LogDebug("🧹 Cleaned up {Count} old transactions", expiredTransactions.Count);
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        _pairingEngine?.Dispose();
        _cleanupTimer?.Dispose();
        _statisticsTimer?.Dispose();
        _fileLogger?.Dispose();
        _completedTransactions.Clear();

        _logger.LogInformation("HttpTransactionManager disposed");
    }

    /// <summary>
    /// Gets completed transactions by host for analysis
    /// </summary>
    public List<CompletedTransaction> GetTransactionsByHost(string hostname, int? port = null)
    {
        return _completedTransactions.Values
            .Where(t => t.Transaction.Hostname.Equals(hostname, StringComparison.OrdinalIgnoreCase) &&
                       (port == null || t.Transaction.Port == port))
            .OrderByDescending(t => t.Transaction.StartTime)
            .ToList();
    }
}
