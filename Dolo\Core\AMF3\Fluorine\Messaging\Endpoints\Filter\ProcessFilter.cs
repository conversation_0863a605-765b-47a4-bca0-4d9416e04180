﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Services;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class ProcessFilter : AbstractFilter
{
    private readonly EndpointBase _endpoint;

    /// <summary>
    ///     Initializes a new instance of the ProcessFilter class.
    /// </summary>
    public ProcessFilter(EndpointBase endpoint) => _endpoint = endpoint;

    #region IFilter Members

    public override Task Invoke(AMFContext context)
    {
        var messageOutput = context.MessageOutput;
        for (var i = 0; i < context.AMFMessage.BodyCount; i++)
        {
            var amfBody = context.AMFMessage.TryGetBodyAt(i);
            ResponseBody responseBody = null;
            //Check for Flex2 messages and skip
            if (amfBody.IsEmptyTarget)
                continue;

            //Check if response exists.
            responseBody = messageOutput.GetResponse(amfBody);
            if (responseBody != null) continue;

            try
            {
                var messageBroker = _endpoint.GetMessageBroker();
                var remotingService = messageBroker.GetService(RemotingService.RemotingServiceId) as RemotingService;
                if (remotingService == null)
                {
                    var serviceNotFound = __Res.GetString(__Res.Service_NotFound, RemotingService.RemotingServiceId);
                    responseBody = new ErrorResponseBody(amfBody, new AMFException(serviceNotFound));
                    messageOutput.AddBody(responseBody);
                    continue;
                }

                Destination destination = null;
                if (destination == null)
                    destination = remotingService.GetDestinationWithSource(amfBody.TypeName);
                if (destination == null)
                    destination = remotingService.DefaultDestination;
                //At this moment we got a destination with the exact source or we have a default destination with the "*" source.
                if (destination == null)
                {
                    var destinationNotFound = __Res.GetString(__Res.Destination_NotFound, amfBody.TypeName);
                    responseBody = new ErrorResponseBody(amfBody, new AMFException(destinationNotFound));
                    messageOutput.AddBody(responseBody);
                    continue;
                }

                //Cache check
                var source = amfBody.TypeName + "." + amfBody.Method;
                var parameterList = amfBody.GetParameterList();

                var factoryInstance = destination.GetFactoryInstance();
                factoryInstance.Source = amfBody.TypeName;
                var instance = factoryInstance.Lookup();

                if (instance != null)
                {
                    var isAccessible = TypeHelper.GetTypeIsAccessible(instance.GetType());
                    if (!isAccessible)
                    {
                        var msg = __Res.GetString(__Res.Type_InitError, amfBody.TypeName);
                        responseBody = new ErrorResponseBody(amfBody, new AMFException(msg));
                        messageOutput.AddBody(responseBody);
                        continue;
                    }

                    MethodInfo mi = null;
                    if (!amfBody.IsRecordsetDelivery)
                        mi = MethodHandler.GetMethod(instance.GetType(), amfBody.Method, amfBody.GetParameterList());
                    else
                        //will receive recordsetid only (ignore)
                        mi = instance.GetType().GetMethod(amfBody.Method);
                    if (mi != null)
                    {
                        #region Invocation handling

                        var parameterInfos = mi.GetParameters();
                        //Try to handle missing/optional parameters.
                        var args = new object[parameterInfos.Length];
                        if (!amfBody.IsRecordsetDelivery)
                        {
                            if (args.Length != parameterList.Count)
                            {
                                var msg = __Res.GetString(__Res.Arg_Mismatch, parameterList.Count, mi.Name,
                                args.Length);
                                responseBody = new ErrorResponseBody(amfBody, new ArgumentException(msg));
                                messageOutput.AddBody(responseBody);
                                continue;
                            }

                            parameterList.CopyTo(args, 0);
                        }
                        else
                        {
                            if (amfBody.Target.EndsWith(".release"))
                            {
                                responseBody = new(amfBody, null);
                                messageOutput.AddBody(responseBody);
                                continue;
                            }

                            var recordsetId = parameterList[0] as string;
                            var recordetDeliveryParameters = amfBody.GetRecordsetArgs();
                            var buffer = System.Convert.FromBase64String(recordetDeliveryParameters);
                            recordetDeliveryParameters = Encoding.UTF8.GetString(buffer);
                            if (recordetDeliveryParameters != null && recordetDeliveryParameters != string.Empty)
                            {
                                var stringParameters = recordetDeliveryParameters.Split([',']);
                                for (var j = 0; j < stringParameters.Length; j++)
                                    if (stringParameters[j] == string.Empty)
                                        args[j] = null;
                                    else
                                        args[j] = stringParameters[j];
                                //TypeHelper.NarrowValues(argsStore, parameterInfos);
                            }
                        }

                        TypeHelper.NarrowValues(args, parameterInfos);

                        try
                        {
                            var invocationHandler = new InvocationHandler(mi);
                            object result = invocationHandler.Invoke(instance, args);

                            responseBody = new(amfBody, result);
                        }
                        catch (UnauthorizedAccessException exception)
                        {
                            responseBody = new ErrorResponseBody(amfBody, exception);
                        }
                        catch (Exception exception)
                        {
                            if (exception is TargetInvocationException && exception.InnerException != null)
                                responseBody = new ErrorResponseBody(amfBody, exception.InnerException);
                            else
                                responseBody = new ErrorResponseBody(amfBody, exception);
                        }

                        #endregion Invocation handling
                    }
                    else
                        responseBody = new ErrorResponseBody(amfBody,
                        new MissingMethodException(amfBody.TypeName, amfBody.Method));
                }
                else
                    responseBody =
                        new ErrorResponseBody(amfBody, new TypeInitializationException(amfBody.TypeName, null));
            }
            catch (Exception exception)
            {
                responseBody = new ErrorResponseBody(amfBody, exception);
            }

            messageOutput.AddBody(responseBody);
        }

        return Task.FromResult<object>(null);
    }

    #endregion
}