using Dolo.Core.Http;
using Dolo.Nebula.Entities;
using Newtonsoft.Json;
namespace Dolo.Nebula.Services;

internal static class GetProfileService
{
    private static string GetQuery() => "query GetProfile($profileId: String!, $gameId:String!) {profile(profileId: $profileId) {name avatar(gameId: $gameId) {id full face} balance(gameId: $gameId) {available {currency count}} memberships {lastTierExpiry}}}";

    public static async Task<object?> GetProfileAsync(NebulaClient client, string profile)
    {
        if (string.IsNullOrEmpty(client.Config.Auth))
            throw new NullReferenceException("auth-key is required on this method");

        var data = await Http.TrySendAsync<object>(a => {
            a.Url = $"{client.Services?.GetEdgeProfile()}/graphql";
            a.Method = HttpMethod.Post;
            a.AuthToken = client.Config.Auth;
            a.ContentType = HttpContentType.ApplicationJson;
            a.Content = new StringContent(JsonConvert.SerializeObject(new GraphQuery
            {
                Query = GetQuery(),
                Variables = JsonConvert.SerializeObject(new Dictionary<string, object>
                {
                    { "profileId", profile },
                    { "gameId", client.Config.Game.GetValueOrDefault().GetNebulaGame() }
                })
            }));
        });

        Console.WriteLine(await data.TryGetStringAsync());

        return data.Body;
    }
}
