﻿@using Dolo.Pluto.Shard.Services
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime
@implements IAsyncDisposable

@* This component automatically loads shared resources from Shard - place once in your app *@

@code {
    private SharedResourceInjectionService? _resourceService;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                _resourceService = new SharedResourceInjectionService(JSRuntime);
                await _resourceService.InitializeSharedResourcesAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize shared resources: {ex.Message}");
            }
        }
    }

    public async ValueTask DisposeAsync()
    {
        await Task.CompletedTask;
    }

}
