﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Services.Remoting;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class RemotingAdapter : ServiceAdapter
{

    public override async Task<object> Invoke(IMessage message)
    {
        Task<object> result = null;
        var remotingMessage = message as RemotingMessage;
        var operation = remotingMessage.operation;
        var className = DestinationSettings.Properties["source"] as string;
        //This property is provided for backwards compatibility. The best practice, however, is to not expose the underlying source of a 
        //RemoteObject destination on the client and only one source to a destination.
        if (remotingMessage.source != null && remotingMessage.source != string.Empty)
        {
            if (className == "*")
                className = remotingMessage.source;
            if (className != remotingMessage.source)
            {
                var msg = __Res.GetString(__Res.Type_MismatchMissingSource, remotingMessage.source, DestinationSettings.Properties["source"] as string);
                throw new MessageException(msg, new TypeLoadException(msg));
            }
        }

        if (className == null)
            throw new TypeInitializationException("null", null);

        //Cache check
        var source = className + "." + operation;
        var parameterList = remotingMessage.body as IList;

        var factoryInstance = Destination.GetFactoryInstance();
        factoryInstance.Source = className;
        var instance = factoryInstance.Lookup();

        if (instance != null)
        {
            var type = instance.GetType();
            var isAccessible = TypeHelper.GetTypeIsAccessible(type);
            if (!isAccessible)
            {
                var msg = __Res.GetString(__Res.Type_InitError, type.FullName);
                throw new MessageException(msg, new TypeLoadException(msg));
            }

            try
            {
                var mi = MethodHandler.GetMethod(type, operation, parameterList);
                if (mi != null)
                {
                    var parameterInfos = mi.GetParameters();
                    var args = new object[parameterInfos.Length];
                    parameterList.CopyTo(args, 0);
                    TypeHelper.NarrowValues(args, parameterInfos);
                    var invocationHandler = new InvocationHandler(mi);
                    result = invocationHandler.Invoke(instance, args);
                    await result;
                }
                else
                    throw new MessageException(new MissingMethodException(className, operation));
            }
            catch (TargetInvocationException exception)
            {
                MessageException messageException = null;
                if (exception.InnerException is MessageException)
                    messageException = exception.InnerException as MessageException;//User code throws MessageException
                else
                    messageException = new(exception.InnerException);
                throw messageException;
            }
            catch (Exception exception)
            {
                var messageException = new MessageException(exception);
                throw messageException;
            }
        }
        else
            throw new MessageException(new TypeInitializationException(className, null));

        return result.Result;
    }
}