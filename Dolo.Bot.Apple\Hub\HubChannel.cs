﻿namespace Dolo.Bot.Apple.Hub;

internal static class HubChannel
{
    public static DiscordChannel? AdminCategory => Hub.Guild?.TryGetChannel(901869538298175529);
    public static DiscordChannel? Hello => Hub.Guild?.TryGetChannel(1207083319955554325);
    public static DiscordChannel? Assets => Hub.Guild?.TryGetChannel(944722654907219988);
    public static DiscordChannel? BanLog => Hub.Guild?.TryGetChannel(977621665263525888);
    public static DiscordChannel? BanChat => Hub.Guild?.TryGetChannel(827497504031244358);
    public static DiscordChannel? PlutoChat => Hub.Guild?.TryGetChannel(991108457828728862);

    // important
    public static DiscordChannel? Reward => Hub.Guild?.TryGetChannel(1207084145684254760);
    public static DiscordChannel? Welcome => Hub.Guild?.TryGetChannel(764489324405325895);
    public static DiscordChannel? Roles => Hub.Guild?.TryGetChannel(819971086145683508);
    public static DiscordChannel? Rules => Hub.Guild?.TryGetChannel(738854492300247240);
    public static DiscordChannel? Dev => Hub.Guild?.TryGetChannel(794650277813682247);
    public static DiscordChannel? DevLog => Hub.Guild?.TryGetChannel(967081320549347428);
    public static DiscordChannel? AppLog => Hub.Guild?.TryGetChannel(996225272133197884);

    // server
    public static DiscordChannel? Changelog => Hub.Guild?.TryGetChannel(919349208417456179);
    public static DiscordChannel? News => Hub.Guild?.TryGetChannel(768581458994135111);
    public static DiscordChannel? Event => Hub.Guild?.TryGetChannel(764094926413365248);

    // info
    public static DiscordChannel? Activator => Hub.Guild?.TryGetChannel(719169186286403616);
    public static DiscordChannel? Support => Hub.Guild?.TryGetChannel(781889898789601302);
    public static DiscordChannel? Suggestion => Hub.Guild?.TryGetChannel(930333254282788874);


    public static DiscordChannel? Bot => Hub.Guild?.TryGetChannel(1091079535237271645);

    // talk
    public static DiscordChannel? TalkCategory => Hub.Guild?.TryGetChannel(749375897173491813);
    public static DiscordChannel? Chat => Hub.Guild?.TryGetChannel(794382151008518154);
    public static DiscordChannel? French => Hub.Guild?.TryGetChannel(795352544725434378);
    public static DiscordChannel? Turkish => Hub.Guild?.TryGetChannel(796797590812229732);
    public static DiscordChannel? Polish => Hub.Guild?.TryGetChannel(907769419139268628);
    public static DiscordChannel? German => Hub.Guild?.TryGetChannel(813380014635745300);

    // other
    public static DiscordChannel? OtherCategory => Hub.Guild?.TryGetChannel(794684516210835467);
    public static DiscordChannel? Art => Hub.Guild?.TryGetChannel(797940481891893248);
    public static DiscordChannel? Stats => Hub.Guild?.TryGetChannel(799861454618361866);
    public static DiscordChannel? Looks => Hub.Guild?.TryGetChannel(794684717949648897);
    public static DiscordChannel? Selfie => Hub.Guild?.TryGetChannel(839557225939992656);
    public static DiscordChannel? Outfit => Hub.Guild?.TryGetChannel(866301592323096607);
    public static DiscordChannel? Travel => Hub.Guild?.TryGetChannel(1087863674770833448);
    public static DiscordChannel? Birthday => Hub.Guild?.TryGetChannel(811907303716159508);
    public static DiscordChannel? Food => Hub.Guild?.TryGetChannel(967372695861878855);
    public static DiscordChannel? Pets => Hub.Guild?.TryGetChannel(984164308302848000);
    public static DiscordChannel? Bots => Hub.Guild?.TryGetChannel(777914228845314080);

    // voice
    public static DiscordChannel? Voice => Hub.Guild?.Channels
        .FirstOrDefault(a => a.Value.Name.ToLower() == "💎 » create voice")
        .Value;
    public static DiscordChannel? VoiceTopic => Hub.Guild?.Channels
        .FirstOrDefault(a => a.Value.Name.ToLower() == "voice" && a.Value.IsCategory)
        .Value;
}