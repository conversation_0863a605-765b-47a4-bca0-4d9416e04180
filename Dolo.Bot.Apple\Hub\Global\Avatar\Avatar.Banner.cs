﻿namespace Dolo.Bot.Apple.Hub.Global.Avatar;

public partial class Avatar
{
    [Command("banner")]
    public async Task BannerAsync(TextCommandContext ctx, DiscordUser? discordUser = null)
    {
        if (Hub.Discord is null)
            return;

        // get the user without cache, because discord does not store the banner of the user
        var user = await Hub.Discord.GetUserAsync(discordUser != null ? discordUser.Id : ctx.User.Id, true);

        // check if the user is null
        if (user is null)
        {
            await ctx.Channel.TrySendMessageAsync($"{HubEmoji.GhostLove} can not find the user..");
            return;
        }

        var member = await Hub.Guild!.GetMemberAsync(user.Id);
        var banner = member is null ? user.BannerUrl : member.BannerUrl;

        // check if the banner is null
        if (banner is null)
        {
            await ctx.Channel.TrySendMessageAsync($"{HubEmoji.GhostLove} {user.Username} does not have a banner");
            return;
        }

        // send the banner to the chat
        await ctx.Channel.TrySendMessageAsync(banner);
    }
}