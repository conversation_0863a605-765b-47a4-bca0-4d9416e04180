﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO.Bytecode;
namespace Dolo.Core.AMF3.Fluorine.IO.Readers.AMF3;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
/// <remarks>
///     This reader is used only in case that a requested type is not found and the gateway choose to represent
///     typed objects with ActionScript typed objects.
/// </remarks>
internal class AMF3TypedASObjectReader : IReflectionOptimizer
{
    private readonly string _typeIdentifier;

    public AMF3TypedASObjectReader(string typeIdentifier) => _typeIdentifier = typeIdentifier;

    #region IReflectionOptimizer Members

    public object CreateInstance()
    {
        #pragma warning disable CS8603
        return default;
        #pragma warning restore CS8603
    }

    public object ReadData(AMFReader reader, ClassDefinition classDefinition)
    {
        var aso = new ASObject(_typeIdentifier);
        reader.AddAMF3ObjectReference(aso);
        var key = reader.ReadAMF3String();
        aso.TypeName = _typeIdentifier;
        while (key != string.Empty)
        {
            var value = reader.ReadAMF3Data();
            aso.Add(key, value);
            key = reader.ReadAMF3String();
        }
        return aso;
    }

    #endregion
}