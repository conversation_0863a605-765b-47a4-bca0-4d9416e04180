using System.Collections.Concurrent;

namespace Dolo.Bot.Apple.Hub.Voice.Music.Entities;

public class MusicQueue
{
    private readonly ConcurrentQueue<Track> _queue = new();
    private readonly object _lockObject = new();
    
    public Track? CurrentTrack { get; private set; }
    public int Count => _queue.Count;
    public bool IsEmpty => _queue.IsEmpty;
    public bool IsLooping { get; set; }
    public bool IsShuffled { get; set; }
    public List<Track> History { get; } = new();

    public void Enqueue(Track track)
    {
        _queue.Enqueue(track);
    }

    public void EnqueueNext(Track track)
    {
        lock (_lockObject)
        {
            var tempQueue = new Queue<Track>();
            tempQueue.Enqueue(track);
            
            while (_queue.TryDequeue(out var existingTrack))
            {
                tempQueue.Enqueue(existingTrack);
            }
            
            foreach (var t in tempQueue)
            {
                _queue.Enqueue(t);
            }
        }
    }

    public Track? Dequeue()
    {
        if (_queue.TryDequeue(out var track))
        {
            if (CurrentTrack != null)
            {
                History.Add(CurrentTrack);
                if (History.Count > 50) // Keep last 50 tracks
                    History.RemoveAt(0);
            }
            
            CurrentTrack = track;
            return track;
        }
        
        return null;
    }

    public Track? Peek()
    {
        return _queue.TryPeek(out var track) ? track : null;
    }

    public void Clear()
    {
        lock (_lockObject)
        {
            while (_queue.TryDequeue(out _)) { }
            CurrentTrack = null;
        }
    }

    public List<Track> GetTracks()
    {
        return _queue.ToList();
    }

    public void Shuffle()
    {
        lock (_lockObject)
        {
            var tracks = _queue.ToList();
            _queue.Clear();
            
            var random = new Random();
            var shuffled = tracks.OrderBy(x => random.Next()).ToList();
            
            foreach (var track in shuffled)
            {
                _queue.Enqueue(track);
            }
            
            IsShuffled = true;
        }
    }

    public bool RemoveTrack(int index)
    {
        lock (_lockObject)
        {
            var tracks = _queue.ToList();
            if (index < 0 || index >= tracks.Count)
                return false;
            
            _queue.Clear();
            for (int i = 0; i < tracks.Count; i++)
            {
                if (i != index)
                    _queue.Enqueue(tracks[i]);
            }
            
            return true;
        }
    }

    public Track? GetNext()
    {
        if (IsLooping && CurrentTrack != null)
            return CurrentTrack;
            
        return Peek();
    }
}
