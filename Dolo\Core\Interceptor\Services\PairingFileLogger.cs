using System.Text;
using System.Text.Json;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class PairingFileLogger : IDisposable
{
    private readonly string _logFilePath;
    private readonly FileStream _fileStream;
    private readonly StreamWriter _writer;
    private readonly Lock _lock = new();
    private readonly ILogger<PairingFileLogger> _logger;
    private bool _disposed;

    public PairingFileLogger(string? logDirectory = null, ILogger<PairingFileLogger>? logger = null)
    {
        _logger = logger ?? Microsoft.Extensions.Logging.LoggerFactory.Create(b => b.SetMinimumLevel(LogLevel.None)).CreateLogger<PairingFileLogger>();
        
        var directory = logDirectory ?? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "DoloInterceptorLogs");
        Directory.CreateDirectory(directory);
        
        var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
        _logFilePath = Path.Combine(directory, $"pairing_log_{timestamp}.txt");
        
        _fileStream = new FileStream(_logFilePath, FileMode.Create, FileAccess.Write, FileShare.Read);
        _writer = new StreamWriter(_fileStream, Encoding.UTF8) { AutoFlush = true };
        
        WriteHeader();
        _logger.LogInformation("📝 Pairing file logger started: {LogFile}", _logFilePath);
    }

    public void LogRequestResponsePair(CompletedTransaction transaction)
    {
        if (_disposed) return;

        try
        {
            lock (_lock)
            {
                var request = transaction.Transaction.Request;
                var response = transaction.Transaction.Response;
                var processedResponse = transaction.ProcessedResponse;

                _writer.WriteLine("=======");
                _writer.WriteLine($"Request: {request?.Uri} (ID: {transaction.Transaction.TransactionId})");
                _writer.WriteLine($"Method: {request?.Method}");
                _writer.WriteLine($"Host: {transaction.Transaction.Hostname}:{transaction.Transaction.Port}");
                _writer.WriteLine($"Protocol: {transaction.Transaction.Protocol}");
                _writer.WriteLine($"Timestamp: {transaction.Transaction.StartTime:yyyy-MM-dd HH:mm:ss.fff}");
                _writer.WriteLine($"Duration: {transaction.Transaction.Duration.TotalMilliseconds:F2}ms");
                _writer.WriteLine($"Status: {transaction.Status}");
                
                if (request?.Headers != null && request.Headers.Count > 0)
                {
                    _writer.WriteLine("Request Headers:");
                    foreach (var header in request.Headers.Take(5))
                    {
                        _writer.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                    }
                }

                if (request?.Content != null && request.Content.Length > 0)
                {
                    _writer.WriteLine($"Request Content ({request.Content.Length} bytes):");
                    if (request.IsAmf && request.Amf?.DecodedContent != null)
                    {
                        _writer.WriteLine("  [AMF Content - Decoded]");
                        try
                        {
                            var amfJson = System.Text.Json.JsonSerializer.Serialize(request.Amf.DecodedContent.Content, new JsonSerializerOptions { WriteIndented = true });
                            _writer.WriteLine($"  {amfJson}");
                        }
                        catch
                        {
                            _writer.WriteLine("  [AMF Content - Could not serialize]");
                        }
                    }
                    else
                    {
                        var contentPreview = GetContentPreview(request.Content);
                        _writer.WriteLine($"  {contentPreview}");
                    }
                }

                _writer.WriteLine($"Response: (ID: {transaction.Transaction.TransactionId})");
                _writer.WriteLine($"Status: {(int)(response?.StatusCode ?? 0)} {response?.ReasonPhrase}");
                _writer.WriteLine($"Content-Type: {processedResponse?.ContentType ?? "unknown"}");
                _writer.WriteLine($"Content-Category: {processedResponse?.ContentCategory ?? "unknown"}");
                
                if (response?.Headers != null && response.Headers.Count > 0)
                {
                    _writer.WriteLine("Response Headers:");
                    foreach (var header in response.Headers.Take(5))
                    {
                        _writer.WriteLine($"  {header.Key}: {string.Join(", ", header.Value)}");
                    }
                }

                if (processedResponse != null)
                {
                    _writer.WriteLine("Content:");
                    if (processedResponse.IsAmf && processedResponse.AmfData?.DecodedContent != null)
                    {
                        _writer.WriteLine("  [AMF Content - Decoded]");
                        try
                        {
                            var amfJson = System.Text.Json.JsonSerializer.Serialize(processedResponse.AmfData.DecodedContent.Content, new JsonSerializerOptions { WriteIndented = true });
                            var truncatedJson = TruncateContent(amfJson, 1000);
                            _writer.WriteLine($"  {truncatedJson}");
                        }
                        catch
                        {
                            _writer.WriteLine("  [AMF Content - Could not serialize]");
                        }
                    }
                    else if (!string.IsNullOrEmpty(processedResponse.DisplayContent))
                    {
                        var truncatedContent = TruncateContent(processedResponse.DisplayContent, 1000);
                        _writer.WriteLine($"  {truncatedContent}");
                    }
                    else if (processedResponse.BinaryContent != null)
                    {
                        _writer.WriteLine($"  [Binary Content - {processedResponse.BinaryContent.Length} bytes]");
                    }
                }

                _writer.WriteLine("=======");
                _writer.WriteLine();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error writing pairing log: {Error}", ex.Message);
        }
    }

    public void LogPairingError(string transactionId, string error, string? requestInfo = null)
    {
        if (_disposed) return;

        try
        {
            lock (_lock)
            {
                _writer.WriteLine("=======");
                _writer.WriteLine($"PAIRING ERROR: Transaction ID: {transactionId}");
                _writer.WriteLine($"Error: {error}");
                _writer.WriteLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                
                if (!string.IsNullOrEmpty(requestInfo))
                {
                    _writer.WriteLine($"Request Info: {requestInfo}");
                }
                
                _writer.WriteLine("=======");
                _writer.WriteLine();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error writing pairing error log: {Error}", ex.Message);
        }
    }

    public void LogStatistics(int activeConnections, int pendingTransactions, int completedTransactions)
    {
        if (_disposed) return;

        try
        {
            lock (_lock)
            {
                _writer.WriteLine($"--- STATISTICS [{DateTime.Now:HH:mm:ss}] ---");
                _writer.WriteLine($"Active Connections: {activeConnections}");
                _writer.WriteLine($"Pending Transactions: {pendingTransactions}");
                _writer.WriteLine($"Completed Transactions: {completedTransactions}");
                _writer.WriteLine();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error writing statistics: {Error}", ex.Message);
        }
    }

    private void WriteHeader()
    {
        _writer.WriteLine("=== DOLO HTTPS INTERCEPTOR - REQUEST/RESPONSE PAIRING LOG ===");
        _writer.WriteLine($"Started: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        _writer.WriteLine($"Log File: {_logFilePath}");
        _writer.WriteLine();
    }

    private static string GetContentPreview(byte[] content)
    {
        if (content.Length == 0) return "[Empty]";
        
        try
        {
            var text = Encoding.UTF8.GetString(content);
            return TruncateContent(text, 200);
        }
        catch
        {
            return $"[Binary Content - {content.Length} bytes]";
        }
    }

    private static string TruncateContent(string content, int maxLength)
    {
        if (string.IsNullOrEmpty(content)) return "[Empty]";
        
        if (content.Length <= maxLength) return content;
        
        return content[..maxLength] + $"... [truncated, total: {content.Length} chars]";
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        try
        {
            lock (_lock)
            {
                _writer?.WriteLine();
                _writer?.WriteLine($"=== LOG ENDED: {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===");
                _writer?.Dispose();
                _fileStream?.Dispose();
            }
            
            _logger.LogInformation("📝 Pairing file logger stopped: {LogFile}", _logFilePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error disposing pairing file logger: {Error}", ex.Message);
        }
    }
}
