using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Apple;
using MongoDB.Driver;

namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("transfer")]
    [Description("transfer ownership of the voice channel to another user")]
    public async Task VoiceTransferAsync(SlashCommandContext ctx, [Description("the user who should become the new owner")] DiscordUser user)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic || HubChannel.VoiceTopic is null)
        {
            await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
            return;
        }

        // check if the user is a member of the server
        if (!Hub.Guild.TryGetMember(user.Id, out var member))
        {
            await ctx.TryEditResponseAsync("The user is not a server member.");
            return;
        }

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // check if the user is the owner
        if (usr.Owner != ctx.User.Id)
        {
            await ctx.TryEditResponseAsync("Only the channel owner can transfer ownership.");
            return;
        }

        // check if trying to transfer to themselves
        if (user.Id == ctx.User.Id)
        {
            await ctx.TryEditResponseAsync("You cannot transfer ownership to yourself.");
            return;
        }

        // check if the target user is already the owner
        if (usr.Owner == user.Id)
        {
            await ctx.TryEditResponseAsync("This user is already the owner.");
            return;
        }

        // get the voice channel
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
        {
            await ctx.TryEditResponseAsync("Voice channel not found.");
            return;
        }

        // check if the new owner is in the voice channel
        var memberVoiceChannel = member.VoiceState != null ? await member.VoiceState.GetChannelAsync() : null;
        if (memberVoiceChannel?.Id != usr.Channel)
        {
            await ctx.TryEditResponseAsync("The new owner must be in the voice channel.");
            return;
        }

        // remove the new owner from moderators list if they are there
        usr.Moderator.Remove(user.Id);

        // update the database with new owner
        await Mongo.Voice.UpdateAsync(
            Builders<VoiceUser>.Filter.Eq(a => a.TextChannel, ctx.Channel.Id),
            Builders<VoiceUser>.Update
                .Set(a => a.Owner, user.Id)
                .Set(a => a.Moderator, usr.Moderator));

        // update the voice channel name
        await channel.ModifyAsync(a => a.Name = $"🌸 » {member.Username}");

        // update the text channel topic
        await ctx.Channel.ModifyAsync(a => a.Topic = $"👑 **Channel-Owner »** <@{user.Id}>");

        // send the message
        await ctx.TryEditResponseAsync($"Channel ownership has been transferred to {user.Mention}.");
    }

    [Command("claim")]
    [Description("claim ownership of an ownerless voice channel (if you're a moderator)")]
    public async Task VoiceClaimAsync(SlashCommandContext ctx)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
            return;
        }

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // check if the current owner still exists in the server
        var currentOwner = Hub.Guild.TryGetMember(usr.Owner, out var ownerMember);
        if (currentOwner && ownerMember != null)
        {
            await ctx.TryEditResponseAsync("This channel already has an active owner.");
            return;
        }

        // check if the user is a moderator of this channel
        if (!usr.Moderator.Contains(ctx.User.Id))
        {
            await ctx.TryEditResponseAsync("Only channel moderators can claim ownership of ownerless channels.");
            return;
        }

        // get the voice channel
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
        {
            await ctx.TryEditResponseAsync("Voice channel not found.");
            return;
        }

        // check if the user is in the voice channel
        if (!Hub.Guild.TryGetMember(ctx.User.Id, out var member))
        {
            await ctx.TryEditResponseAsync("You must be in the voice channel to claim ownership.");
            return;
        }

        var userVoiceChannel = member.VoiceState != null ? await member.VoiceState.GetChannelAsync() : null;
        if (userVoiceChannel?.Id != usr.Channel)
        {
            await ctx.TryEditResponseAsync("You must be in the voice channel to claim ownership.");
            return;
        }

        // remove the new owner from moderators list
        usr.Moderator.Remove(ctx.User.Id);

        // update the database with new owner
        await Mongo.Voice.UpdateAsync(
            Builders<VoiceUser>.Filter.Eq(a => a.TextChannel, ctx.Channel.Id),
            Builders<VoiceUser>.Update
                .Set(a => a.Owner, ctx.User.Id)
                .Set(a => a.Moderator, usr.Moderator));

        // update the voice channel name
        await channel.ModifyAsync(a => a.Name = $"🌸 » {ctx.User.Username}");

        // update the text channel topic
        await ctx.Channel.ModifyAsync(a => a.Topic = $"👑 **Channel-Owner »** <@{ctx.User.Id}>");

        // send the message
        await ctx.TryEditResponseAsync("You have successfully claimed ownership of this channel.");
    }
}
