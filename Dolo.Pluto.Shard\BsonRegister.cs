﻿using Dolo.Pluto.Shard.License.Feature;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
namespace Dolo.Pluto.Shard;

public class BsonRegister
{
    public static void Register()
    {
        BsonSerializer.RegisterSerializer(new ObjectSerializer(type
            => ObjectSerializer.DefaultAllowedTypes(type) || type.FullName!.StartsWith("Dolo.Pluto")));

        BsonClassMap.RegisterClassMap<FeatureUpload>();
        BsonClassMap.RegisterClassMap<FeatureFamePurchased>();
        BsonClassMap.RegisterClassMap<FeatureAutographPurchased>();
    }
}