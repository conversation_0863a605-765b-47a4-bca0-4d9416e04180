﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class ErrorResponseBody : ResponseBody
{
    /// <summary>
    ///     Initializes a new instance of the ErrorResponseBody class.
    /// </summary>
    private ErrorResponseBody()
    {}
    /// <summary>
    ///     Initializes a new instance of the ErrorResponseBody class.
    /// </summary>
    /// <param name="requestBody"></param>
    /// <param name="error"></param>
    public ErrorResponseBody(AMFBody requestBody, string error) : base(requestBody)
    {
        IgnoreResults = requestBody.IgnoreResults;
        Target = requestBody.Response + OnStatus;
        Response = null;
        Content = error;
    }
    /// <summary>
    ///     Initializes a new instance of the ErrorResponseBody class.
    /// </summary>
    /// <param name="requestBody"></param>
    /// <param name="exception"></param>
    public ErrorResponseBody(AMFBody requestBody, Exception exception) : base(requestBody)
    {
        Content = exception;
        if (requestBody.IsEmptyTarget)
        {
            var content = requestBody.Content;
            if (content is IList)
                content = (content as IList)[0];
            var message = content as IMessage;
            //Check for Flex2 messages and handle
            if (message != null)
            {
                var errorMessage = ErrorMessage.GetErrorMessage(message, exception);
                Content = errorMessage;
            }
        }
        IgnoreResults = requestBody.IgnoreResults;
        Target = requestBody.Response + OnStatus;
        Response = null;
    }
    /// <summary>
    ///     Initializes a new instance of the ErrorResponseBody class.
    /// </summary>
    /// <param name="requestBody"></param>
    /// <param name="message"></param>
    /// <param name="exception"></param>
    public ErrorResponseBody(AMFBody requestBody, IMessage message, Exception exception) : base(requestBody)
    {
        var errorMessage = ErrorMessage.GetErrorMessage(message, exception);
        Content = errorMessage;
        Target = requestBody.Response + OnStatus;
        IgnoreResults = requestBody.IgnoreResults;
        Response = "";
    }
    /// <summary>
    ///     Initializes a new instance of the ErrorResponseBody class.
    /// </summary>
    /// <param name="requestBody"></param>
    /// <param name="message"></param>
    /// <param name="errorMessage"></param>
    public ErrorResponseBody(AMFBody requestBody, IMessage message, ErrorMessage errorMessage) : base(requestBody)
    {
        Content = errorMessage;
        Target = requestBody.Response + OnStatus;
        IgnoreResults = requestBody.IgnoreResults;
        Response = "";
    }
}