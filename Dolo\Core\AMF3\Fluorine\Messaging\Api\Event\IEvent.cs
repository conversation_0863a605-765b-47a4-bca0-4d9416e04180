﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Event;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal enum EventType
{
    SYSTEM, STATUS, SERVICE_CALL, SHARED_OBJECT, STREAM_CONTROL, STREAM_DATA, CLIENT, SERVER
}

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal interface IEvent
{
    /// <summary>
    ///     Gets event type.
    /// </summary>
    EventType EventType { get; }
    /// <summary>
    ///     Gets event context object.
    /// </summary>
    object Object { get; }
    /// <summary>
    ///     Gets whether event has source (event listeners).
    /// </summary>
    bool HasSource { get; }
    /// <summary>
    ///     Gets event listener.
    /// </summary>
    IEventListener Source { get; set; }
}