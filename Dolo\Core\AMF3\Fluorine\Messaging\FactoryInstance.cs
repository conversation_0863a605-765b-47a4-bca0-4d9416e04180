﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     This class is used by the IFlexFactory to store the configuration for an instance created by the factory.
///     There is one of these for each destination currently since only destinations create these components.
/// </summary>
internal class FactoryInstance
{
    private readonly IFlexFactory _factory;

    /// <summary>
    ///     Initializes a new instance of the FactoryInstance class.
    /// </summary>
    /// <param name="factory">The IFlexFactory this FactoryInstance is created from.</param>
    /// <param name="id">The Destination's id.</param>
    /// <param name="properties">The configuration properties for this destination.</param>
    public FactoryInstance(IFlexFactory factory, string id, Hashtable properties)
    {
        _factory = factory;
        Id = id;
        Properties = properties;
    }

    public string Id
    {
        get;
    }

    public virtual string Scope
    {
        get;
        set;
    }

    public virtual string Source
    {
        get;
        set;
    }

    public string AttributeId
    {
        get;
        set;
    }


    public Hashtable Properties
    {
        get;
    }

    /// <summary>
    ///     If possible, returns the class for the underlying configuration.
    ///     This method can return null if the class is not known until the lookup method is called.
    ///     The goal is so the factories which know the class at startup time can provide earlier error detection.
    ///     If the class is not known, this method can return null and validation will wait until the first lookup call.
    /// </summary>
    /// <returns></returns>
    public virtual Type GetInstanceClass() => null;

    /// <summary>
    ///     Return an instance as appropriate for this instance of the given factory. This just calls the lookup method on the
    ///     factory
    ///     that this instance was created on. You override this method to return the specific component for this destination.
    /// </summary>
    /// <returns></returns>
    public virtual object Lookup() => _factory.Lookup(this);

    /// <summary>
    ///     When the caller is done with the instance, this method is called. For session scoped components, this gives you the
    ///     opportunity to
    ///     update any state modified in the instance in a remote persistence store.
    /// </summary>
    /// <param name="instance"></param>
    public virtual void OnOperationComplete(object instance)
    {}
}