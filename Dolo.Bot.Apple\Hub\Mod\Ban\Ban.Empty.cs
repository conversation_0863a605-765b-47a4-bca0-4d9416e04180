﻿namespace Dolo.Bot.Apple.Hub.Mod.Ban;

public partial class Ban
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("empty")]
    [Description("empty the role ban by kicking them from server")]
    public async Task EmptyAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(true);
        await ctx.TryEditResponseAsync("Emptying role banned members ...");

        // get all members and check if one has the role
        var members = await ctx.Guild.TryGetAllMemberAsync();
        var role = members?.Where(a => a.Roles.Contains(HubRoles.Banned)).ToList();
        if (role is null || !role.Any())
        {
            await ctx.TryEditResponseAsync("No role banned members found.");
            return;
        }

        // kick all members
        foreach (var member in role)
            await member.TryKickAsync();

        // print messsage
        await ctx.TryEditResponseAsync("Role banned members have been kicked.");
    }
}