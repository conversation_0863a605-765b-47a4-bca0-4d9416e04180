using System.Net.Sockets;
using System.Text;
using System.Text.RegularExpressions;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class HttpRequestHandler
{
    private readonly InterceptorConfig _config;
    private readonly SslConnectionHandler _sslHandler;
    private readonly TunnelingService _tunnelingService;
    private readonly ILogger<HttpRequestHandler> _logger;

    public HttpRequestHandler(
        InterceptorConfig config,
        SslConnectionHandler sslHandler,
        TunnelingService tunnelingService,
        ILogger<HttpRequestHandler> logger)
    {
        _config = config;
        _sslHandler = sslHandler;
        _tunnelingService = tunnelingService;
        _logger = logger;
    }

    public async Task HandleClientAsync(TcpClient client)
    {
        try
        {
            using var clientStream = client.GetStream();
            var buffer = new byte[4096];
            var received = await clientStream.ReadAsync(buffer.AsMemory()).ConfigureAwait(false);

            if (received == 0) return;

            var request = Encoding.UTF8.GetString(buffer, 0, received);
            var lines = request.Split('\n');
            var firstLine = lines[0].Trim();

            if (firstLine.StartsWith("CONNECT"))
            {
                await HandleConnectRequest(client, clientStream, firstLine).ConfigureAwait(false);
            }
            else
            {
                await HandleHttpRequest(client, clientStream, request).ConfigureAwait(false);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling client connection: {Error}", ex.Message);
        }
        finally
        {
            try
            {
                client.Close();
            }
            catch
            {
                // Ignore close errors
            }
        }
    }

    private async Task HandleConnectRequest(TcpClient client, NetworkStream clientStream, string connectLine)
    {
        try
        {
            var parts = connectLine.Split(' ');
            if (parts.Length < 2) return;

            var hostPort = parts[1];
            var hostPortParts = hostPort.Split(':');
            var hostname = hostPortParts[0];
            var port = hostPortParts.Length > 1 ? int.Parse(hostPortParts[1]) : 443;

            var shouldIntercept = _config.IsTargetDomain(hostname);

            _logger.LogInformation("CONNECT {Host}:{Port} - {Action}",
                hostname,
                port,
                shouldIntercept ? "INTERCEPTING" : "TUNNELING");

            if (shouldIntercept)
            {
                await _sslHandler.HandleSslConnectionAsync(client, clientStream, hostname, port).ConfigureAwait(false);
            }
            else
            {
                await _tunnelingService.TunnelTrafficAsync(clientStream, hostname, port).ConfigureAwait(false);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling CONNECT request: {Error}", ex.Message);
        }
    }

    private async Task HandleHttpRequest(TcpClient client, NetworkStream clientStream, string request)
    {
        try
        {
            var hostMatch = Regex.Match(request, @"Host:\s*([^\r\n]+)", RegexOptions.IgnoreCase);
            if (!hostMatch.Success) return;

            var hostHeader = hostMatch.Groups[1].Value.Trim();
            var hostParts = hostHeader.Split(':');
            var hostname = hostParts[0];
            var port = hostParts.Length > 1 ? int.Parse(hostParts[1]) : 80;

            var shouldIntercept = _config.IsTargetDomain(hostname);

            _logger.LogInformation("HTTP {Host}:{Port} - {Action}",
                hostname,
                port,
                shouldIntercept ? "INTERCEPTING" : "TUNNELING");

            if (shouldIntercept)
            {
                await HandleInterceptedHttp(clientStream, hostname, port, request).ConfigureAwait(false);
            }
            else
            {
                await _tunnelingService.TunnelHttpTrafficAsync(clientStream, hostname, port, request).ConfigureAwait(false);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling HTTP request: {Error}", ex.Message);
        }
    }

    private async Task HandleInterceptedHttp(NetworkStream clientStream, string hostname, int port, string request)
    {
        try
        {
            using var serverClient = new TcpClient();
            await serverClient.ConnectAsync(hostname, port).ConfigureAwait(false);
            using var serverStream = serverClient.GetStream();

            var requestBytes = Encoding.UTF8.GetBytes(request);
            await serverStream.WriteAsync(requestBytes.AsMemory()).ConfigureAwait(false);

            var buffer = new byte[4096];
            var received = await serverStream.ReadAsync(buffer.AsMemory()).ConfigureAwait(false);

            if (received > 0)
            {
                await clientStream.WriteAsync(buffer.AsMemory(0, received)).ConfigureAwait(false);
            }

            _logger.LogDebug("HTTP request intercepted and relayed for {Host}:{Port}", hostname, port);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling intercepted HTTP for {Host}:{Port}: {Error}", hostname, port, ex.Message);
        }
    }
}
