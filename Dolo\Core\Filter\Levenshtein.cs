﻿namespace Dolo.Core.Filter;

public static class Levenshtein
{
    /// <summary>
    ///     Levenshtein distance between two strings.
    /// </summary>
    public static int GetDistance(string? s, string? t)
    {
        if (s        == null) return 0;
        if (t        == null) return 0;
        if (s        == t) return 0;
        if (s.Length == 0) return t.Length;
        if (t.Length == 0) return s.Length;

        var matrix = new int[s.Length + 1, t.Length + 1];
        for (var i = 0; i <= s.Length; i++)
            matrix[i, 0] = i;
        for (var j = 0; j <= t.Length; j++)
            matrix[0, j] = j;

        for (var i = 1; i <= s.Length; i++)
        for (var j = 1; j <= t.Length; j++)
        {
            var cost = s[i                                                                         - 1] == t[j - 1] ? 0 : 1;
            matrix[i, j] = Math.Min(Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1), matrix[i - 1, j      - 1] + cost);
        }

        return matrix[s.Length, t.Length];
    }
}