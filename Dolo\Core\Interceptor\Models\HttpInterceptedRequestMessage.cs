using Dolo.Core.Interceptor.Interfaces;
using System.Text;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Represents an intercepted HTTP request message with all request data
/// </summary>
public sealed class HttpInterceptedRequestMessage : IHttpInterceptedRequestMessage
{
    public string Method { get; init; } = string.Empty;
    public Uri? Uri { get; init; }
    public Version Version { get; init; } = new(1, 1);
    public Dictionary<string, string[]> Headers { get; init; } = new();
    public byte[]? Content { get; init; }
    public string? ContentType => Headers.TryGetValue("Content-Type", out var values) ? values.FirstOrDefault() : null;
    public long? ContentLength => Headers.TryGetValue("Content-Length", out var values) && long.TryParse(values.FirstOrDefault(), out var length) ? length : Content?.Length;
    public bool IsAmf { get; init; }
    public IAmfInterceptedData? Amf { get; init; }

    public string GetContentAsString(Encoding? encoding = null) =>
        Content is null ? string.Empty : (encoding ?? Encoding.UTF8).GetString(Content);

    public T? GetContentAsJson<T>() where T : class
    {
        var content = GetContentAsString();
        return string.IsNullOrEmpty(content) ? null : System.Text.Json.JsonSerializer.Deserialize<T>(content);
    }

    public string GetHeaderValue(string name) =>
        Headers.TryGetValue(name, out var values) ? values.FirstOrDefault() ?? string.Empty : string.Empty;

    public string[] GetHeaderValues(string name) =>
        Headers.TryGetValue(name, out var values) ? values : Array.Empty<string>();
}
