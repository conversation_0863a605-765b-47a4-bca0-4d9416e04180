﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class CacheableObject
{

    /// <summary>
    ///     Initializes a new instance of the CacheableObject class.
    /// </summary>
    /// <param name="source"></param>
    /// <param name="cacheKey"></param>
    /// <param name="obj"></param>
    public CacheableObject(string source, string cacheKey, object obj)
    {
        Source = source;
        CacheKey = cacheKey;
        Object = obj;
    }

    public object Object
    {
        get;
    }
    public string? CacheKey
    {
        get;
    }
    public string? Source
    {
        get;
    }
}