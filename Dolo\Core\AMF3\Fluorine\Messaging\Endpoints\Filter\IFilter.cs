﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal interface IFilter
{
    /// <summary>
    ///     Gets or sets the next filter.
    /// </summary>
    IFilter Next { get; set; }
    /// <summary>
    ///     Invokes the filter.
    /// </summary>
    /// <param name="context"></param>
    Task Invoke(AMFContext context);
}