﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO;

namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMFContext
{
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string AMFCoreContextKey = "__@amfcorecontext";

    /// <summary>
    ///     Initializes a new instance of the AMFContext class.
    /// </summary>
    public AMFContext(Stream inputStream, Stream outputStream)
    {
        InputStream = inputStream;
        OutputStream = outputStream;
    }


    public AMFMessage AMFMessage
    {
        get;
        set;
    }

    public MessageOutput MessageOutput
    {
        get;
        set;
    }

    public Stream InputStream
    {
        get;
    }

    public Stream OutputStream
    {
        get;
    }
}