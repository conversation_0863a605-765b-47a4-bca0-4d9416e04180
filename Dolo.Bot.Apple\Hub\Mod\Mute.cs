﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Mod;

public class Mute
{
    [RequirePermissions(DiscordPermission.MuteMembers)]
    [Command("mute")]
    [Description("mute a user from the server for 2 minutes")]
    public async Task MuteAsync(SlashCommandContext ctx, [Description("the user to mute from the server")] DiscordUser user, [Description("explain why the user should be muted")] string reason)
    {
        await ctx.Interaction.DeferAsync(true);

        // check if the user is null
        if (Hub.Guild is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You must specify a user to mute");
            return;
        }

        // check if the reason is null
        if (string.IsNullOrEmpty(reason))
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You must specify a reason for the mute");
            return;
        }

        // check if the server has this member
        if (user is not DiscordMember guildMember)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user you specified is not in this server");
            return;
        }

        // check if the user is the user who is running the command
        if (user.Id == ctx.User.Id)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You cannot mute yourself");
            return;
        }

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);
        if (member is null)
            return;

        // grant the role to the member
        await guildMember.TryTimeoutAsync(TimeSpan.FromMinutes(2), reason);
    }
}