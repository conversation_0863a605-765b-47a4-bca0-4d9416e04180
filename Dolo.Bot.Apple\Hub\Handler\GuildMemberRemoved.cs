﻿using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Apple;
using MongoDB.Driver;

namespace Dolo.Bot.Apple.Hub.Handler;

public static class GuildMemberRemoved
{
    public static async Task InvokeAsync(this GuildMemberRemovedEventArgs e)
    {
        // we remove the member from the cache
        Hub.MemberSearchSystem.RemoveMember(e.Member.Id);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == e.Member.Id);

        // remove the welcome message if it exist
        if (member?.Welcome != null && HubChannel.Chat != null && member.Welcome.HasWelcome)
            await HubChannel.Chat.TryDeleteMessageAsync(member.Welcome.Id);

        // cleanup voice channels owned by the leaving member
        await CleanupMemberVoiceChannelsAsync(e.Member.Id);
    }

    private static async Task CleanupMemberVoiceChannelsAsync(ulong memberId)
    {
        // get all voice channels owned by this member
        var voiceChannels = await Mongo.Voice.GetAsync(a => a.Owner == memberId);

        foreach (var voiceChannel in voiceChannels)
        {
            // try to get the voice channel from the server
            var vc = Hub.Guild?.TryGetChannel(voiceChannel.Channel);
            if (vc != null)
                await vc.TryDeleteAsync();

            // try to get the text channel from the server
            var tc = Hub.Guild?.TryGetChannel(voiceChannel.TextChannel);
            if (tc != null)
                await tc.TryDeleteAsync();

            // remove the database entry
            await Mongo.Voice.DeleteAsync(a => a.Owner == memberId);
        }

        // also remove the member from any moderator, banned, or muted lists
        var allVoiceChannels = await Mongo.Voice.GetAsync(a =>
            a.Moderator.Contains(memberId) ||
            a.Banned.Contains(memberId) ||
            a.Muted.Contains(memberId));

        foreach (var channel in allVoiceChannels)
        {
            var updated = false;

            if (channel.Moderator.Contains(memberId))
            {
                channel.Moderator.Remove(memberId);
                updated = true;
            }

            if (channel.Banned.Contains(memberId))
            {
                channel.Banned.Remove(memberId);
                updated = true;
            }

            if (channel.Muted.Contains(memberId))
            {
                channel.Muted.Remove(memberId);
                updated = true;
            }

            if (updated)
            {
                await Mongo.Voice.UpdateAsync(
                    Builders<VoiceUser>.Filter.Eq(a => a.Channel, channel.Channel),
                    Builders<VoiceUser>.Update
                        .Set(a => a.Moderator, channel.Moderator)
                        .Set(a => a.Banned, channel.Banned)
                        .Set(a => a.Muted, channel.Muted));
            }
        }
    }
}
