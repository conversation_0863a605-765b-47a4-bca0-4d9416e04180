﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Voice.Event;

internal class UserLeftVoiceEmpty
{
    // will be triggered whe a user left a channel and it was empty without any users
    public static async Task InvokeAsync(VoiceArgs e)
    {
        if (e.Guild is null
            || e.Channel is null
            || e.Channel == HubChannel.Voice)
            return;

        // get the database entry
        var db = await Mongo.Voice.GetOneAsync(a => a.Channel == e.Channel.Id);
        if (db is null)
            return;

        // try to get the channel from the server if exist then delete it
        var tc = e.Guild.TryGetChannel(db.TextChannel);
        if (tc != null)
            await tc.TryDeleteAsync();

        // delete the channel
        await e.Channel.TryDeleteAsync();

        // delete the database entry
        await Mongo.Voice.DeleteAsync(a => a.Channel == e.Channel.Id);
    }
}