namespace Dolo.Bot.Apple.Hub.Global;

public class Friday
{

    [Command("friday")]
    [Description("Its friday night, saturday, sunday")]
    public async Task FridayAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync();

        if (HubCache.FridayCommandStream is {})
        {
            HubCache.FridayCommandStream.Seek(0, Seek<PERSON><PERSON>in.Begin);
            await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
                .AddFile("friday.mp4", HubCache.FridayCommandStream));
            return;
        }

        var stream = await Http.TryGetStreamAsync("https://github.com/cydolo/assets/raw/main/videos/friday.mp4");
        if (stream.Result is null) return;

        HubCache.FridayCommandStream = stream.Result;

        await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
            .AddFile("friday.mp4", HubCache.FridayCommandStream));
    }
}