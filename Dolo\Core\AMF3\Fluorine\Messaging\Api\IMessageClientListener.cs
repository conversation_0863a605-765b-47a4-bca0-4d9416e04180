﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     Interface to be notified when a MessageClient is created or destroyed.
/// </summary>
internal interface IMessageClientListener
{
    /// <summary>
    ///     Notification that a MessageClient instance was created.
    /// </summary>
    /// <param name="messageClient">The MessageClient that was created.</param>
    void MessageClientCreated(IMessageClient messageClient);
    /// <summary>
    ///     Notification that a MessageClient is about to be destroyed.
    /// </summary>
    /// <param name="messageClient">The MessageClient that will be destroyed.</param>
    void MessageClientDestroyed(IMessageClient messageClient);
}