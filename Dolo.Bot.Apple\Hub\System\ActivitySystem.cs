﻿using Dolo.Core;
using Dolo.Core.Consola;
using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.System;

public static class ActivitySystem
{
    public static async Task StartAsync()
    {
         Consola.Information("Activity system started");

        _ = Task.Run(async () => {
            var prd = new PeriodicTimer(TimeSpan.FromHours(1));
            while (await prd.WaitForNextTickAsync())
            {
                var settings = await Mongo.ServerSettings.GetFirstAsync();
                if (!settings?.IsFakeNotifyEnabled ?? false) continue;
                foreach (var channel in HubConstant.ActivityChannels)
                    await channel!.TrySendMessageAsync(RandomStringGenerator.GetLorem())
                        .TryDeleteAfterAsync(TimeSpan.FromSeconds(1));
            }
        });
    }
}