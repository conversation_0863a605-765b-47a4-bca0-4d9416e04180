namespace Dolo.Bot.Apple.Hub.Voice.Music.Entities;

public class Track
{
    public string Title { get; set; } = string.Empty;
    public string Artist { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public string Thumbnail { get; set; } = string.Empty;
    public DiscordUser RequestedBy { get; set; } = null!;
    public DateTime RequestedAt { get; set; } = DateTime.UtcNow;
    public TrackSource Source { get; set; }
    public string SourceId { get; set; } = string.Empty;

    public string FormattedDuration => Duration.ToString(@"mm\:ss");
    public string FormattedTitle => string.IsNullOrEmpty(Artist) ? Title : $"{Artist} - {Title}";
    
    public static Track FromYouTube(string title, string url, TimeSpan duration, string thumbnail, DiscordUser requestedBy, string videoId)
    {
        return new Track
        {
            Title = title,
            Url = url,
            Duration = duration,
            Thumbnail = thumbnail,
            RequestedBy = requestedBy,
            Source = TrackSource.YouTube,
            SourceId = videoId
        };
    }

    public static Track FromSpotify(string title, string artist, string url, TimeSpan duration, string thumbnail, DiscordUser requestedBy, string trackId)
    {
        return new Track
        {
            Title = title,
            Artist = artist,
            Url = url,
            Duration = duration,
            Thumbnail = thumbnail,
            RequestedBy = requestedBy,
            Source = TrackSource.Spotify,
            SourceId = trackId
        };
    }

    public static Track FromSoundCloud(string title, string url, TimeSpan duration, string thumbnail, DiscordUser requestedBy, string trackId)
    {
        return new Track
        {
            Title = title,
            Url = url,
            Duration = duration,
            Thumbnail = thumbnail,
            RequestedBy = requestedBy,
            Source = TrackSource.SoundCloud,
            SourceId = trackId
        };
    }
}

public enum TrackSource
{
    YouTube,
    Spotify,
    SoundCloud,
    Direct
}
