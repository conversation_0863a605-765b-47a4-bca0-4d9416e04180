using Dolo.Core.Interceptor.Interfaces;
using System.Net;
using System.Text;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Event arguments for intercepted HTTP/HTTPS traffic with proper request/response message objects
/// </summary>
public sealed class TrafficInterceptedEventArgs(
    string hostname,
    int port,
    string protocol,
    int bytesTransferred = 0,
    TimeSpan duration = default,
    IHttpInterceptedRequestMessage? request = null,
    IHttpInterceptedResponseMessage? response = null) : EventArgs
{
    public string Hostname { get; } = hostname;
    public int Port { get; } = port;
    public string Protocol { get; } = protocol;
    public string Url => $"{Protocol}://{Hostname}:{Port}{Request?.Uri?.PathAndQuery}";
    public int BytesTransferred { get; } = bytesTransferred;
    public TimeSpan Duration { get; } = duration;
    public DateTime Timestamp { get; } = DateTime.UtcNow;
    public IHttpInterceptedRequestMessage? Request { get; } = request;
    public IHttpInterceptedResponseMessage? Response { get; } = response;
}

/// <summary>
/// Event arguments for certificate generation events
/// </summary>
public class CertificateGeneratedEventArgs(
    string hostname,
    string certificateSubject,
    string thumbprint,
    DateTime validFrom,
    DateTime validTo) : EventArgs
{
    public string Hostname { get; } = hostname;
    public string CertificateSubject { get; } = certificateSubject;
    public string Thumbprint { get; } = thumbprint;
    public DateTime ValidFrom { get; } = validFrom;
    public DateTime ValidTo { get; } = validTo;
}

/// <summary>
/// Event arguments for error events
/// </summary>
public class InterceptorErrorEventArgs(string source, string message, Exception? exception = null) : EventArgs
{
    public string Source { get; } = source;
    public string Message { get; } = message;
    public Exception? Exception { get; } = exception;
    public DateTime Timestamp { get; } = DateTime.Now;
}

/// <summary>
/// Event arguments for timed out HTTP transactions
/// </summary>
public sealed class HttpTransactionTimedOutEventArgs(HttpTransaction transaction) : EventArgs
{
    public HttpTransaction Transaction { get; } = transaction;

    // Convenience properties
    public string Hostname => Transaction.Hostname;
    public int Port => Transaction.Port;
    public string Protocol => Transaction.Protocol;
    public string Url => $"{Protocol}://{Hostname}:{Port}{Transaction.Request?.Uri?.PathAndQuery}";
    public TimeSpan TimeoutDuration => Transaction.Duration;
    public DateTime Timestamp => Transaction.StartTime;
    public IHttpInterceptedRequestMessage? Request => Transaction.Request;
}

/// <summary>
/// Enum for breakpoint actions
/// </summary>
public enum BreakpointAction {
    Continue,
    Cancel,
    Execute
}

/// <summary>
/// Enum for breakpoint types
/// </summary>
[Flags]
public enum BreakpointType {
    None = 0,
    Request = 1,
    Response = 2,
    Both = Request | Response
}

/// <summary>
/// Event arguments for breakpoint hit events
/// </summary>
public sealed class BreakpointHitEventArgs : EventArgs {
    public string BreakpointId { get; }
    public BreakpointType Type { get; }
    public string TransactionId { get; }
    public string ConnectionId { get; }
    public string Hostname { get; }
    public int Port { get; }
    public string Protocol { get; }
    public string Url { get; }
    public DateTime Timestamp { get; }
    public IHttpInterceptedRequestMessage? Request { get; set; }
    public IHttpInterceptedResponseMessage? Response { get; set; }
    public TaskCompletionSource<BreakpointAction> ActionSource { get; }

    public BreakpointHitEventArgs(
        string breakpointId,
        BreakpointType type,
        string transactionId,
        string connectionId,
        string hostname,
        int port,
        string protocol,
        IHttpInterceptedRequestMessage? request = null,
        IHttpInterceptedResponseMessage? response = null) {
        BreakpointId = breakpointId;
        Type = type;
        TransactionId = transactionId;
        ConnectionId = connectionId;
        Hostname = hostname;
        Port = port;
        Protocol = protocol;
        Url = $"{Protocol}://{Hostname}:{Port}{Request?.Uri?.PathAndQuery ?? Response?.Uri?.PathAndQuery}";
        Timestamp = DateTime.UtcNow;
        Request = request;
        Response = response;
        ActionSource = new TaskCompletionSource<BreakpointAction>();
    }

    /// <summary>
    /// Resolves the breakpoint with the specified action
    /// </summary>
    public void ResolveBreakpoint(BreakpointAction action) {
        ActionSource.TrySetResult(action);
    }

    /// <summary>
    /// Waits for the breakpoint to be resolved
    /// </summary>
    public Task<BreakpointAction> WaitForResolutionAsync() {
        return ActionSource.Task;
    }

    // FLUENT API FOR RUNTIME MODIFICATION - THREAD-SAFE IMPLEMENTATION
    private readonly object _modificationLock = new();
    private readonly Dictionary<string, string[]> _modifiedHeaders = new();
    private byte[]? _modifiedContent;
    private string? _modifiedMethod;
    private Uri? _modifiedUri;
    private int? _modifiedStatusCode;
    private string? _modifiedReasonPhrase;
    private volatile bool _hasModifications;

    /// <summary>
    /// THREAD-SAFE: Gets the current content (modified or original)
    /// </summary>
    private byte[]? GetCurrentContent() {
        lock (_modificationLock) {
            return _modifiedContent ?? (Type == BreakpointType.Request ? Request?.Content : Response?.Content);
        }
    }

    /// <summary>
    /// THREAD-SAFE: Modifies or adds a header value (fluent API)
    /// </summary>
    public BreakpointHitEventArgs ModifyHeader(string name, string value) {
        lock (_modificationLock) {
            _modifiedHeaders[name] = [value];
            _hasModifications = true;
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies or adds a header with multiple values (fluent API)
    /// </summary>
    public BreakpointHitEventArgs ModifyHeader(string name, params string[] values) {
        lock (_modificationLock) {
            _modifiedHeaders[name] = values;
            _hasModifications = true;
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Adds a header value to existing values (fluent API)
    /// </summary>
    public BreakpointHitEventArgs AddHeader(string name, string value) {
        lock (_modificationLock) {
            if (_modifiedHeaders.TryGetValue(name, out var existing)) {
                _modifiedHeaders[name] = [.. existing, value];
            }
            else {
                var originalValues = Request?.GetHeaderValues(name) ?? Response?.GetHeaderValues(name) ?? [];
                _modifiedHeaders[name] = [.. originalValues, value];
            }
            _hasModifications = true;
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Removes a header (fluent API)
    /// </summary>
    public BreakpointHitEventArgs RemoveHeader(string name) {
        lock (_modificationLock) {
            _modifiedHeaders[name] = [];
            _hasModifications = true;
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the content (fluent API)
    /// </summary>
    public BreakpointHitEventArgs ModifyContent(byte[] content) {
        lock (_modificationLock) {
            _modifiedContent = content;
            _hasModifications = true;
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the content from string (fluent API)
    /// </summary>
    public BreakpointHitEventArgs ModifyContent(string content, Encoding? encoding = null) {
        lock (_modificationLock) {
            _modifiedContent = (encoding ?? Encoding.UTF8).GetBytes(content);
            _hasModifications = true;
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the content from JSON object (fluent API)
    /// </summary>
    public BreakpointHitEventArgs ModifyContentAsJson<T>(T obj) where T : class {
        var json = System.Text.Json.JsonSerializer.Serialize(obj);
        lock (_modificationLock) {
            _modifiedContent = Encoding.UTF8.GetBytes(json);
            _hasModifications = true;
        }
        ModifyHeader("Content-Type", "application/json");
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies AMF content with new data and automatically updates hash (fluent API)
    /// </summary>
    public BreakpointHitEventArgs ModifyAmfContent(object?[] newData, bool isTicketRequired = false) {
        if (GetCurrentContent() == null) return this;

        _ = Task.Run(async () => {
            try {
                var amfService = new Services.AmfModificationService();
                var modifiedAmfBytes = await amfService.ModifyAmfContentAsync(GetCurrentContent()!, newData, isTicketRequired).ConfigureAwait(false);

                lock (_modificationLock) {
                    _modifiedContent = modifiedAmfBytes;
                    _hasModifications = true;
                }
                ModifyHeader("Content-Type", "application/x-amf");
            }
            catch (Exception ex) {
                // Log error but don't throw to avoid breaking the breakpoint flow
                Console.WriteLine($"Failed to modify AMF content: {ex.Message}");
            }
        });

        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies AMF content using a modifier function and automatically updates hash (fluent API)
    /// </summary>
    public BreakpointHitEventArgs ModifyAmfContent(Func<object?[], object?[]> dataModifier, bool isTicketRequired = false) {
        if (GetCurrentContent() == null) return this;

        _ = Task.Run(async () => {
            try {
                var amfService = new Services.AmfModificationService();
                var modifiedAmfBytes = await amfService.ModifyAmfContentAsync(GetCurrentContent()!, dataModifier, isTicketRequired).ConfigureAwait(false);

                lock (_modificationLock) {
                    _modifiedContent = modifiedAmfBytes;
                    _hasModifications = true;
                }
                ModifyHeader("Content-Type", "application/x-amf");
            }
            catch (Exception ex) {
                // Log error but don't throw to avoid breaking the breakpoint flow
                Console.WriteLine($"Failed to modify AMF content: {ex.Message}");
            }
        });

        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the HTTP method (for requests only)
    /// </summary>
    public BreakpointHitEventArgs ModifyMethod(string method) {
        if (Type == BreakpointType.Request) {
            lock (_modificationLock) {
                _modifiedMethod = method;
                _hasModifications = true;
            }
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the URI (for requests only)
    /// </summary>
    public BreakpointHitEventArgs ModifyUri(Uri uri) {
        if (Type == BreakpointType.Request) {
            lock (_modificationLock) {
                _modifiedUri = uri;
                _hasModifications = true;
            }
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the URI from string (for requests only)
    /// </summary>
    public BreakpointHitEventArgs ModifyUri(string uri) {
        if (Type == BreakpointType.Request && Uri.TryCreate(uri, UriKind.RelativeOrAbsolute, out var parsedUri)) {
            lock (_modificationLock) {
                _modifiedUri = parsedUri;
                _hasModifications = true;
            }
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the status code (for responses only)
    /// </summary>
    public BreakpointHitEventArgs ModifyStatusCode(int statusCode, string? reasonPhrase = null) {
        if (Type == BreakpointType.Response) {
            lock (_modificationLock) {
                _modifiedStatusCode = statusCode;
                _modifiedReasonPhrase = reasonPhrase;
                _hasModifications = true;
            }
        }
        return this;
    }

    /// <summary>
    /// THREAD-SAFE: Modifies the status code using HttpStatusCode enum (for responses only)
    /// </summary>
    public BreakpointHitEventArgs ModifyStatusCode(HttpStatusCode statusCode) {
        if (Type == BreakpointType.Response) {
            lock (_modificationLock) {
                _modifiedStatusCode = (int)statusCode;
                _modifiedReasonPhrase = statusCode.ToString();
                _hasModifications = true;
            }
        }
        return this;
    }

    /// <summary>
    /// Checks if any modifications have been made
    /// </summary>
    public bool HasModifications => _hasModifications;

    /// <summary>
    /// THREAD-SAFE: Gets the modified request message with all changes applied
    /// </summary>
    public IHttpInterceptedRequestMessage? GetModifiedRequest() {
        if (Type != BreakpointType.Request || Request == null) return Request;
        if (!_hasModifications) return Request;

        lock (_modificationLock) {
            var headers = new Dictionary<string, string[]>(Request.Headers);

            // Apply header modifications
            foreach (var (name, values) in _modifiedHeaders) {
                if (values.Length == 0) {
                    headers.Remove(name); // Remove header
                } else {
                    headers[name] = values; // Add/modify header
                }
            }

            // Update Content-Length if content was modified
            if (_modifiedContent != null) {
                headers["Content-Length"] = [_modifiedContent.Length.ToString()];
            }

            return new HttpInterceptedRequestMessage {
                Method = _modifiedMethod ?? Request.Method,
                Uri = _modifiedUri ?? Request.Uri,
                Version = Request.Version,
                Headers = headers,
                Content = _modifiedContent ?? Request.Content,
                IsAmf = Request.IsAmf,
                Amf = Request.Amf
            };
        }
    }

    /// <summary>
    /// THREAD-SAFE: Gets the modified response message with all changes applied
    /// </summary>
    public IHttpInterceptedResponseMessage? GetModifiedResponse() {
        if (Type != BreakpointType.Response || Response == null) return Response;
        if (!_hasModifications) return Response;

        lock (_modificationLock) {
            var headers = new Dictionary<string, string[]>(Response.Headers);

            // Apply header modifications
            foreach (var (name, values) in _modifiedHeaders) {
                if (values.Length == 0) {
                    headers.Remove(name); // Remove header
                } else {
                    headers[name] = values; // Add/modify header
                }
            }

            // Update Content-Length if content was modified
            if (_modifiedContent != null) {
                headers["Content-Length"] = [_modifiedContent.Length.ToString()];
            }

            return new HttpInterceptedResponseMessage {
                StatusCode = _modifiedStatusCode.HasValue ? (HttpStatusCode)_modifiedStatusCode.Value : Response.StatusCode,
                ReasonPhrase = _modifiedReasonPhrase ?? Response.ReasonPhrase,
                Version = Response.Version,
                Headers = headers,
                Content = _modifiedContent ?? Response.Content,
                Method = Response.Method,
                Uri = Response.Uri,
                IsAmf = Response.IsAmf,
                Amf = Response.Amf
            };
        }
    }

    /// <summary>
    /// THREAD-SAFE: Applies all modifications to the original request/response objects
    /// This updates the Request/Response properties with modified versions
    /// </summary>
    public void ApplyModifications() {
        if (!_hasModifications) return;

        if (Type == BreakpointType.Request && Request != null) {
            Request = GetModifiedRequest();
        } else if (Type == BreakpointType.Response && Response != null) {
            Response = GetModifiedResponse();
        }
    }

    /// <summary>
    /// THREAD-SAFE: Resets all modifications back to original values
    /// </summary>
    public BreakpointHitEventArgs ResetModifications() {
        lock (_modificationLock) {
            _modifiedHeaders.Clear();
            _modifiedContent = null;
            _modifiedMethod = null;
            _modifiedUri = null;
            _modifiedStatusCode = null;
            _modifiedReasonPhrase = null;
            _hasModifications = false;
        }
        return this;
    }
}
