﻿using Dolo.Core.Discord;
namespace Dolo.Bot.Log.Hub;

public static class HubChannel
{
    public static DiscordChannel? Log
        => Hub.Guild?.TryGetChannel(723942739057573929);
    public static DiscordChannel? Join
        => Hub.Guild?.TryGetChannel(715904889682919497);

    public static DiscordChannel? LogFile
        => Hub.Guild?.TryGetChannel(1208441164051513384);
    public static DiscordChannel? Leave
        => Hub.Guild?.TryGetChannel(1079367101321584791);

public static DiscordChannel? SlashCommands
    => Hub.Guild?.TryGetChannel(723942739057573929);
public static DiscordChannel? Attachments
    => Hub.Guild?.TryGetChannel(1400053611748266114);
    }
