﻿using Dolo.Core.Consola;
using Dolo.Database;
using System.Diagnostics;
namespace Dolo.Bot.Apple.Hub.System;

public static class HistogramSystem
{
    public static Task StartAsync()
    {
        _ = Task.Run(async () => {
            Consola.Information("MspHistogram system started.");
            var periodic = new PeriodicTimer(new(0, 10, 0));

            _ = Task.Run(CheckFor2AmAsync);

            while (await periodic.WaitForNextTickAsync())
                await StartHistogramAsync();
        });
        return Task.CompletedTask;
    }

    private static async Task CheckFor2AmAsync()
    {
        var periodic = new PeriodicTimer(new(0, 0, 0, 1));
        while (await periodic.WaitForNextTickAsync())
        {
            // if it's 2 am exact time, then we can start the histogram
            if (DateTime.Now.Hour != 2 || DateTime.Now.Minute != 0 || DateTime.Now.Second != 0)
                continue;

            await StartHistogramAsync();
        }
    }
    private static async Task StartHistogramAsync()
    {
        await Mongo.Histogram.ClearAsync();

        const string app = "Dolo.Planet.Histogram.exe";
        Process.Start(new ProcessStartInfo(app)
        {
            FileName = app,
            UseShellExecute = false,
            RedirectStandardOutput = true,
            CreateNoWindow = false,
            WorkingDirectory = Path.GetDirectoryName(app)
        });
    }
}