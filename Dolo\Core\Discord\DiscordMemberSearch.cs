﻿namespace Dolo.Core.Discord;

/// <summary>
///     This class contains extension methods for the DiscordGuild class to search for members.
/// </summary>
public static class DiscordMemberSearchExtension
{
    // Base URL for the Discord API
    private const string BaseUrl = "https://ptb.discord.com/api/v9/guilds/";

    /// <summary>
    ///     Executes a search for members in a Discord guild.
    /// </summary>
    /// <param name="guild">The guild to search in.</param>
    /// <param name="token">The authorization token.</param>
    /// <param name="after">The ID of the last member in the previous search, used for pagination.</param>
    /// <param name="orQuery">The OR query parameter.</param>
    /// <param name="andQuery">The AND query parameter.</param>
    /// <param name="limit">The maximum number of members to return.</param>
    /// <returns>A DiscordMemberSearch object containing the search results.</returns>
    private static async Task<DiscordMemberSearch> ExecuteAsync(this DiscordGuild guild, string? token, object? after = default, object? orQuery = default, object? andQuery = default, int limit = 500)
    {
        // Implementation of the members-search API from Discord
        // https://discord.com/developers/docs/resources/guild#search-guild-members

        var http = await Http.Http.TrySendAsync<DiscordMemberSearch>(a => {
            a.Method = HttpMethod.Post;
            a.Url = $"{BaseUrl}{guild.Id}/members-search";
            a.WithoutTokenType = false;
            a.AuthToken = token;
            a.AuthType = "Bot";
            a.Version = HttpVersion.Version30;
            a.Content = new StringContent(JsonConvert.SerializeObject(new
            {
                after,
                or_query = orQuery,
                and_query = andQuery,
                limit
            }), Encoding.UTF8, "application/json");
        });

        return http.Body ?? new DiscordMemberSearch();
    }

    /// <summary>
    ///     Gets the latest member search results for a Discord guild.
    /// </summary>
    /// <param name="guild">The guild to search in.</param>
    /// <param name="token">The authorization token.</param>
    /// <param name="limit"></param>
    /// <returns>A DiscordMemberSearch object containing the search results.</returns>
    public static async Task<DiscordMemberSearch> GetMemberSearchAsync(this DiscordGuild guild, string? token, int limit = 500)
        => await guild.ExecuteAsync(token, limit: limit);

    /// <summary>
    ///     Gets all member search results for a Discord guild.
    /// </summary>
    /// <param name="guild">The guild to search in.</param>
    /// <param name="token">The authorization token.</param>
    /// <returns>A DiscordMemberSearch object containing all search results.</returns>
    public static async Task<DiscordMemberSearch> GetAllMemberSearchAsync(this DiscordGuild guild, string? token)
    {
        var members = new DiscordMemberSearch();
        int total;
        var current = 0;
        var lastUserId = 0ul;
        var guildJoinedAt = 0l;

        do
        {
            var result = await guild.ExecuteAsync(token, lastUserId != 0 ? new
            {
                user_id = lastUserId.ToString(),
                guild_joined_at = guildJoinedAt
            } : default);

            if (result.Members.Count == 0)
                break;

            total = result.TotalCount;
            current += result.PageCount;
            lastUserId = result.Members.LastOrDefault()?.Member?.Id                                    ?? 0;
            guildJoinedAt = result.Members.LastOrDefault()?.Member?.JoinedAt.ToTimestampMilliseconds() ?? 0l;

            members.Members.TryAddRange(result.Members);

            members.GuildId = guild.Id;
            members.PageCount = result.PageCount;
            members.TotalCount = total;
        } while (current < total);

        return members;
    }
}

public class DiscordMemberSearch
{
    [JsonProperty("guild_id")]
    public ulong GuildId { get; set; }
    [JsonProperty("members")]
    public List<DiscordMemberSource> Members { get; set; } = [];
    [JsonProperty("page_result_count")]
    public int PageCount { get; set; }
    [JsonProperty("total_result_count")]
    public int TotalCount { get; set; }
}

public class DiscordMemberSource
{
    [JsonProperty("member")]
    public DiscordNewMember? Member { get; set; }
    [JsonProperty("source_invite_code")]
    public string? InviteCode { get; set; }
    [JsonProperty("join_source_type")]
    public JoinType? JoinType { get; set; }
    [JsonProperty("inviter_id")]
    public ulong? InvitedBy { get; set; }
    public DateTimeOffset? CreatedAt { get; set; }
}

public class DiscordNewUser
{
    [JsonProperty("id")]
    public ulong Id { get; set; }
    [JsonProperty("username")]
    public string Username { get; set; } = "";
    [JsonProperty("discriminator")]
    public string Discriminator { get; set; } = "";
}

public class DiscordNewMember
{
    [JsonProperty("nick")]
    public string? Nickname { get; set; }
    [JsonProperty("roles")]
    public List<ulong> Roles { get; set; } = [];
    [JsonProperty("joined_at")]
    public DateTime JoinedAt { get; set; }
    [JsonProperty("deaf")]
    public bool IsDeaf { get; set; }
    [JsonProperty("mute")]
    public bool IsMute { get; set; }
    [JsonProperty("pending")]
    public bool IsPending { get; set; }
    [JsonProperty("permissions")]
    public string? Permissions { get; set; }
    [JsonProperty("premium_since")]
    public DateTimeOffset? PremiumSince { get; set; }
    [JsonProperty("user")]
    public DiscordNewUser? User { get; set; }

    public string? Username => User?.Username;
    public string? Discriminator => User?.Discriminator;
    public ulong Id => User?.Id ?? 0;
}

public enum JoinType
{
    Unknown = 0,
    Bot = 1,        // invited by a bot
    Integration = 2,// invited by integration
    Discovery = 3,  // invited by server discovery
    Hub = 4,        // invited by a hub
    Invite = 5,     // invited by a user
    Vanity = 6      // invited by vanity url

}