using Dolo.Core.Interceptor.Interfaces;
using System.Net;
using System.Text;

namespace Dolo.Core.Interceptor.Models;

/// <summary>
/// Represents an intercepted HTTP response message with all response data
/// </summary>
public sealed class HttpInterceptedResponseMessage : IHttpInterceptedResponseMessage
{
    public HttpStatusCode StatusCode { get; init; }
    public string ReasonPhrase { get; init; } = string.Empty;
    public Version Version { get; init; } = new(1, 1);
    public Dictionary<string, string[]> Headers { get; init; } = new();
    public byte[]? Content { get; init; }
    public bool IsSuccessStatusCode => (int)StatusCode >= 200 && (int)StatusCode <= 299;
    public string? ContentType => Headers.TryGetValue("Content-Type", out var values) ? values.FirstOrDefault() : null;
    public long? ContentLength => Headers.TryGetValue("Content-Length", out var values) && long.TryParse(values.FirstOrDefault(), out var length) ? length : Content?.Length;
    public bool IsAmf { get; init; }
    public IAmfInterceptedData? Amf { get; init; }
    public string Method { get; init; } = string.Empty;
    public Uri? Uri { get; init; }

    public string GetContentAsString(Encoding? encoding = null) =>
        Content is null ? string.Empty : (encoding ?? Encoding.UTF8).GetString(Content);
    
    public T? GetContentAsJson<T>() where T : class
    {
        var content = GetContentAsString();
        return string.IsNullOrEmpty(content) ? null : System.Text.Json.JsonSerializer.Deserialize<T>(content);
    }
    
    public string GetHeaderValue(string name) =>
        Headers.TryGetValue(name, out var values) ? values.FirstOrDefault() ?? string.Empty : string.Empty;
    
    public string[] GetHeaderValues(string name) =>
        Headers.TryGetValue(name, out var values) ? values : [];
}
