﻿#pragma warning disable CS8604
namespace Dolo.Core.Discord;

public static class DiscordChannelExtension
{
    /// <summary>
    ///     Try to create an invite for a channel
    /// </summary>
    /// <param name="channel"></param>
    /// <param name="maxAge"></param>
    /// <param name="maxUses"></param>
    /// <param name="temporary"></param>
    /// <param name="unique"></param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordInvite?> TryCreateInviteAsync(this DiscordChannel channel, int maxAge = 86400, int maxUses = 0, bool temporary = false, bool unique = false, Action<Exception>? onError = null)
        => await channel.CreateInviteAsync(maxAge, maxUses, temporary, unique).TryAsync(onError ?? (a => Console.WriteLine(a.Message)));

    /// <summary>
    ///     Try to place a member into a channel
    /// </summary>
    /// <param name="channel">The Discord channel.</param>
    /// <param name="member">The Discord member to place in the channel.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryPlaceMemberAsync(this DiscordChannel channel, DiscordMember member, Action<Exception>? onError = null)
        => await channel.PlaceMemberAsync(member).TryAsync(onError);

    /// <summary>
    ///     Try to change the position of a channel
    /// </summary>
    /// <param name="channel">The Discord channel.</param>
    /// <param name="pos">The new position for the channel.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryModifyPositionAsync(this DiscordChannel channel, int pos, Action<Exception>? onError = null)
        => await channel.ModifyPositionAsync(pos).TryAsync(onError);
    
    /// <summary>
    ///     Try to delete a channel
    /// </summary>
    /// <param name="channel">The Discord channel to delete.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryDeleteAsync(this DiscordChannel channel, Action<Exception>? onError = null)
        => await channel.DeleteAsync().TryAsync(onError);

    /// <summary>
    ///     Try to add override permissions to a channel
    /// </summary>
    /// <param name="channel">The Discord channel.</param>
    /// <param name="member">The Discord member to add permission overrides for.</param>
    /// <param name="allow">Permissions to allow.</param>
    /// <param name="deny">Permissions to deny.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryAddOverrideAsync(this DiscordChannel channel, DiscordMember? member, DiscordPermissions allow = default, DiscordPermissions deny = default, Action<Exception>? onError = null)
        => await channel.AddOverwriteAsync(member, allow, deny).TryAsync(onError);

    /// <summary>
    ///     Try to add override permissions to a channel
    /// </summary>
    /// <param name="channel">The Discord channel.</param>
    /// <param name="role">The Discord role to add permission overrides for.</param>
    /// <param name="allow">Permissions to allow.</param>
    /// <param name="deny">Permissions to deny.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryAddOverrideAsync(this DiscordChannel channel, DiscordRole? role, DiscordPermissions allow = default, DiscordPermissions deny = default, Action<Exception>? onError = null)
        => await channel.AddOverwriteAsync(role, allow, deny).TryAsync(onError ?? (a => Console.WriteLine(a.Message)));
}