﻿using Microsoft.Win32;
using System.Runtime.Versioning;
namespace Dolo.Core;

[SupportedOSPlatform("windows")]
public static class Regedit
{
    public static string? Get(string key, string? defaultValue = null) => Registry.GetValue(key, null, defaultValue) as string;
    public static void Set(string key, string value) => Registry.SetValue(key, null, value);
    public static void Delete(string key) => Registry.CurrentUser.DeleteValue(key);
    public static void DeleteKey(string key) => Registry.CurrentUser.DeleteSubKeyTree(key);
    public static void CreateKey(string key) => Registry.CurrentUser.CreateSubKey(key);
    public static bool Exists(string key) => Registry.GetValue(key, null, null) != null;
}