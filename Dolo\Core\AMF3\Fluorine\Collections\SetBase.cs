﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     Sorted set of objects; depending on constructor parameters, implements
///     regular set (equal members are not permitted), or multiset (equal members are OK).
/// </summary>
internal abstract class SetBase : ISet, IComparable
{
    private readonly bool _allowDuplicates;
    private RbTree _tree;

    /// <summary>
    ///     Creates an instance of SetBase.
    /// </summary>
    /// <param name="comparer">Comparer that specifies sort order of the elements.</param>
    /// <param name="allowDuplicates">Whether multiple duplicate (equivalent) elements are allowed.</param>
    public SetBase(IComparer comparer, bool allowDuplicates)
    {
        Comparer = comparer;
        _allowDuplicates = allowDuplicates;
        _tree = new(Comparer);
    }

    #region IComparable Members

    /// <summary>
    ///     Compares this set to another object.
    /// </summary>
    /// <remarks>
    ///     If obj is not an enumerable, returns -1 (this &lt; obj)
    ///     Otherwise performs member-to-member lexicographical comparison of two enumerables using default comparer.
    /// </remarks>
    public int CompareTo(object obj) => CollectionComparer.Default.Compare(this, obj);

    #endregion


    #region IEnumerable Members

    /// <summary>
    ///     Returns an enumerator that can iterate through the set.
    /// </summary>
    public IEnumerator GetEnumerator() => new SetEnumerator(_tree);

    #endregion

    #region IReversible Members

    /// <summary>
    ///     Enumerable whose enumerator traverses the set in reversed order.
    /// </summary>
    public IEnumerable Reversed => new ReversedTree(_tree);

    #endregion

    #region ISet Members

    /// <summary>
    ///     Comparer object that defines sort order for the set.
    /// </summary>
    public IComparer Comparer { get; }

    #endregion

    /// <summary>
    ///     Compares this to parameter.
    /// </summary>
    /// <remarks>
    ///     If obj is not enumerable, returns false.
    ///     If obj is enumerable, compares all members using <c>Comparer.Default</c>.
    /// </remarks>
    public override bool Equals(object obj) => CollectionComparer.Default.Compare(this, obj) == 0;

    /// <summary>
    ///     Exists just to silence compiler warning.
    /// </summary>
    public override int GetHashCode() => base.GetHashCode();

    /// <summary>
    ///     Checks that the other set has compatible comparer.
    /// </summary>
    /// <remarks>
    ///     Tries to check that comparer object of the other set is the same
    ///     as comparer object of this set. Since comparers don't provide adequate
    ///     imlpementation of Equals(), we just check that comparer types are the same.
    ///     Throws exception if there is an incompatibility.
    /// </remarks>
    protected void CheckComparer(ISet other)
    {
        if (Comparer.GetType() != other.Comparer.GetType())
            throw new ArgumentException("Sets have incompatible comparer objects", "other");
    }

    #region ICollection Members

    /// <summary>
    ///     Indicates whether access to the set is synchronized (thread-safe).
    /// </summary>
    public bool IsSynchronized => false;

    /// <summary>
    ///     Returns number of elements in the set.
    /// </summary>
    public int Count { get; private set; }

    /// <summary>
    ///     Copies set to an array.
    /// </summary>
    /// <remarks>
    ///     <para>
    ///         All items in the set must be castable to the type of the array. Otherwise,
    ///         <c>InvalidCastException</c> will be thrown.
    ///     </para>
    ///     <para><b>array</b> must be one-dimensional. Otherwise <c>ArgumentException</c> is thrown.</para>
    ///     <para>
    ///         <b>index</b> must be within valid range for the <b>array</b>. Otherwise <c>ArgumentOutOfRangeException</c> is
    ///         thrown.
    ///     </para>
    ///     <para>
    ///         <b>array</b> must have enough space after <b>index</b> to fit all elements of the set. Otherwise
    ///         <c>ArgumentOutOfRangeException</c> is thrown.
    ///     </para>
    ///     <para>Elements are put into the array in ascending sort order.</para>
    /// </remarks>
    /// <param name="array">Array to copy to.</param>
    /// <param name="index">Index to start from.</param>
    public void CopyTo(Array array, int index)
    {
        if (array      == null) throw new ArgumentNullException("array");
        if (array.Rank != 1) throw new ArgumentException("Cannot copy to multidimensional array", "array");

        if (index < 0) throw new ArgumentOutOfRangeException("index", index, "index cannot be negative");

        if (index >= array.Length)
            throw new ArgumentOutOfRangeException("index",
            index,
            string.Format("Passed array of length {0}, index cannot exceed {1}", array.Length, array.Length - 1));

        var count = Count;
        if (array.Length - index < count)
            throw new ArgumentOutOfRangeException("index",
            index,
            string.Format("Not enough room in the array to copy the collection. Array length {0}, start index {1}, items in collection {2}",
            array.Length, index, count));

        var i = index;
        foreach (var item in this)
        {
            array.SetValue(item, i);
            ++i;
        }
    }

    /// <summary>
    ///     An object that can be used to synchronize access to the set.
    /// </summary>
    /// <remarks>
    ///     <para>Returns <c>this</c> for set objects that store their members themselves.</para>
    ///     <para>Returns underlying object for decorators that are wrappers around other objects.</para>
    /// </remarks>
    public object SyncRoot => this;

    #endregion

    #region IModifiableCollection Members

    /// <summary>
    ///     Indicates whether the set is read-only.
    /// </summary>
    public bool IsReadOnly => false;

    /// <summary>
    ///     Adds object to the set, preserving order.
    /// </summary>
    public bool Add(object key)
    {
        if (key == null) return false;

        var result = _tree.Insert(key, _allowDuplicates, true);

        if (result.NewNode)
            ++Count;
        return result.NewNode;
    }

    /// <summary>
    ///     Adds object to the set only if the set contains no equal object(s).
    /// </summary>
    public bool AddIfNotContains(object key)
    {
        if (key == null) return false;

        var result = _tree.Insert(key, false, false);

        if (result.NewNode) ++Count;

        return result.NewNode;
    }

    /// <summary>
    ///     Removes object(s) from the set.
    /// </summary>
    /// <remarks>
    ///     All objects equal to <b>key</b> are removed.
    /// </remarks>
    public int Remove(object key)
    {
        if (key == null) return 0;
        var result = _tree.Erase(key);
        Count -= result;
        return result;
    }

    /// <summary>
    ///     Removes all objects from the set.
    /// </summary>
    public void Clear()
    {
        _tree = new(Comparer);
        Count = 0;
    }

    /// <summary>
    ///     Finds object in the set.
    /// </summary>
    /// <returns>First object equal to <b>obj</b>, or null if not found.</returns>
    public object Find(object key)
    {
        if (key == null) return null;
        var node = _tree.LowerBound(key);

        if (node.IsNull) return null;

        if (Comparer.Compare(node.Value, key) == 0) return node.Value;
        return null;
    }

    /// <summary>
    ///     Finds object(s) in the set.
    /// </summary>
    /// <returns>Collection of objects equal to <b>obj</b>.</returns>
    /// <remarks>
    ///     If no elements equal to <b>obj</b> are found in the set, returns
    ///     valid reference to an empty collection.
    /// </remarks>
    public ICollection FindAll(object key)
    {
        var result = new ArrayList();
        if (key == null) return result;

        var lower = _tree.LowerBound(key);
        var upper = _tree.UpperBound(key);

        if (lower == upper) return result;

        for (var node = lower; node != upper; node = _tree.Next(node)) result.Add(node.Value);

        return result;
    }

    #endregion
}