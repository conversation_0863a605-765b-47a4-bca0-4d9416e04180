using Dolo.Bot.Apple.Hub.Voice.Music.Entities;
using System.Text;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

public static class MusicEmbeds
{
    private const string EmbedColor = "D6EDFB";

    public static DiscordEmbed NowPlaying(Track track, MusicPlayer player)
    {
        var embed = new DiscordEmbedBuilder()
            .WithTitle($"🎵 Now Playing")
            .WithColor(new DiscordColor(EmbedColor))
            .WithDescription($"**{track.FormattedTitle}**")
            .AddField("Duration", track.FormattedDuration, true)
            .AddField("Requested by", track.RequestedBy.Mention, true)
            .AddField("Volume", $"{player.Volume * 100:F0}%", true)
            .WithTimestamp(DateTimeOffset.UtcNow);

        if (!string.IsNullOrEmpty(track.Thumbnail) && Uri.IsWellFormedUriString(track.Thumbnail, UriKind.Absolute))
            embed.WithThumbnail(track.Thumbnail);

        if (player.Queue.Count > 0)
            embed.AddField("Up Next", $"{player.Queue.Count} track(s) in queue", false);

        return embed.Build();
    }

    public static DiscordEmbed TrackAdded(Track track, int queuePosition)
    {
        var embed = new DiscordEmbedBuilder()
            .WithTitle("✅ Track Added to Queue")
            .WithColor(new DiscordColor("4CAF50"))
            .WithDescription($"**{track.FormattedTitle}**")
            .AddField("Duration", track.FormattedDuration, true)
            .AddField("Position in Queue", queuePosition.ToString(), true)
            .AddField("Requested by", track.RequestedBy.Mention, true)
            .WithTimestamp(DateTimeOffset.UtcNow);

        if (!string.IsNullOrEmpty(track.Thumbnail) && Uri.IsWellFormedUriString(track.Thumbnail, UriKind.Absolute))
            embed.WithThumbnail(track.Thumbnail);

        return embed.Build();
    }

    public static DiscordEmbed Queue(MusicPlayer player, int page = 1, int itemsPerPage = 10)
    {
        var tracks = player.Queue.GetTracks();
        var totalPages = (int)Math.Ceiling(tracks.Count / (double)itemsPerPage);
        page = Math.Clamp(page, 1, Math.Max(1, totalPages));

        var embed = new DiscordEmbedBuilder()
            .WithTitle($"🎵 Music Queue")
            .WithColor(new DiscordColor(EmbedColor))
            .WithTimestamp(DateTimeOffset.UtcNow);

        if (player.CurrentTrack != null)
        {
            embed.AddField("🎵 Currently Playing", 
                $"**{player.CurrentTrack.FormattedTitle}**\n" +
                $"Requested by {player.CurrentTrack.RequestedBy.Mention}", false);
        }

        if (tracks.Count == 0)
        {
            embed.WithDescription("Queue is empty. Add some tracks with `/music play`!");
            return embed.Build();
        }

        var description = new StringBuilder();
        var startIndex = (page - 1) * itemsPerPage;
        var endIndex = Math.Min(startIndex + itemsPerPage, tracks.Count);

        for (int i = startIndex; i < endIndex; i++)
        {
            var track = tracks[i];
            description.AppendLine($"`{i + 1}.` **{track.FormattedTitle}** `[{track.FormattedDuration}]`");
            description.AppendLine($"    Requested by {track.RequestedBy.Mention}");
            description.AppendLine();
        }

        embed.WithDescription(description.ToString());
        
        if (totalPages > 1)
            embed.WithFooter($"Page {page}/{totalPages} • {tracks.Count} total tracks");
        else
            embed.WithFooter($"{tracks.Count} track(s) in queue");

        return embed.Build();
    }

    public static DiscordEmbed SearchResults(List<Track> tracks, string query)
    {
        var embed = new DiscordEmbedBuilder()
            .WithTitle($"🔍 Search Results for: {query}")
            .WithColor(new DiscordColor(EmbedColor))
            .WithTimestamp(DateTimeOffset.UtcNow);

        if (tracks.Count == 0)
        {
            embed.WithDescription("No results found for your search query.");
            return embed.Build();
        }

        var description = new StringBuilder();
        for (int i = 0; i < Math.Min(tracks.Count, 5); i++)
        {
            var track = tracks[i];
            description.AppendLine($"`{i + 1}.` **{track.FormattedTitle}** `[{track.FormattedDuration}]`");
        }

        embed.WithDescription(description.ToString());
        embed.WithFooter($"Found {tracks.Count} result(s) • Use the buttons below to select");

        return embed.Build();
    }

    public static DiscordEmbed PlayerStatus(MusicPlayer player)
    {
        var embed = new DiscordEmbedBuilder()
            .WithTitle("🎵 Player Status")
            .WithColor(new DiscordColor(EmbedColor))
            .WithTimestamp(DateTimeOffset.UtcNow);

        var statusEmoji = player.State switch
        {
            PlayerState.Playing => "▶️",
            PlayerState.Paused => "⏸️",
            PlayerState.Stopped => "⏹️",
            PlayerState.Buffering => "⏳",
            _ => "❓"
        };

        embed.AddField("Status", $"{statusEmoji} {player.State}", true);
        embed.AddField("Volume", $"{player.Volume * 100:F0}%", true);
        embed.AddField("Queue", $"{player.Queue.Count} track(s)", true);

        if (player.CurrentTrack != null)
        {
            embed.AddField("Current Track", 
                $"**{player.CurrentTrack.FormattedTitle}**\n" +
                $"Requested by {player.CurrentTrack.RequestedBy.Mention}", false);
        }

        if (player.NextTrack != null)
        {
            embed.AddField("Up Next", player.NextTrack.FormattedTitle, false);
        }

        var settings = new List<string>();
        if (player.Queue.IsLooping) settings.Add("🔂 Loop");
        if (player.Queue.IsShuffled) settings.Add("🔀 Shuffle");
        
        if (settings.Any())
            embed.AddField("Settings", string.Join(" • ", settings), false);

        return embed.Build();
    }

    public static DiscordEmbed Error(string message)
    {
        return new DiscordEmbedBuilder()
            .WithTitle("❌ Error")
            .WithDescription(message)
            .WithColor(new DiscordColor("FF6B6B"))
            .WithTimestamp(DateTimeOffset.UtcNow)
            .Build();
    }

    public static DiscordEmbed Success(string message)
    {
        return new DiscordEmbedBuilder()
            .WithTitle("✅ Success")
            .WithDescription(message)
            .WithColor(new DiscordColor("4CAF50"))
            .WithTimestamp(DateTimeOffset.UtcNow)
            .Build();
    }

    public static DiscordEmbed PlaylistAdded(List<Track> tracks, DiscordUser requestedBy)
    {
        return new DiscordEmbedBuilder()
            .WithTitle("📋 Playlist Added to Queue")
            .WithDescription($"Added **{tracks.Count}** tracks to the queue")
            .AddField("Requested by", requestedBy.Mention, true)
            .AddField("Total Duration", FormatTotalDuration(tracks), true)
            .WithColor(new DiscordColor("4CAF50"))
            .WithTimestamp(DateTimeOffset.UtcNow)
            .Build();
    }

    private static string FormatTotalDuration(List<Track> tracks)
    {
        var totalSeconds = tracks.Sum(t => t.Duration.TotalSeconds);
        var totalDuration = TimeSpan.FromSeconds(totalSeconds);
        
        if (totalDuration.TotalHours >= 1)
            return totalDuration.ToString(@"h\:mm\:ss");
        else
            return totalDuration.ToString(@"mm\:ss");
    }
}
