using System.IO.Compression;
using System.Text;
using System.Text.Json;
using Dolo.Core.Interceptor.Interfaces;
using Dolo.Core.Interceptor.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class ResponseDecodingService {
    private readonly ILogger<ResponseDecodingService> _logger;
    private readonly AmfService _amfService;

    public ResponseDecodingService(ILogger<ResponseDecodingService> logger, AmfService amfService)
    {
        _logger = logger;
        _amfService = amfService;
    }

    public Task<ProcessedResponse> ProcessResponseAsync(IHttpInterceptedResponseMessage response)
    {
        var processed = new ProcessedResponse
        {
            StatusCode = (int)response.StatusCode,
            ReasonPhrase = response.ReasonPhrase,
            Headers = ConvertHeaders(response.Headers),
            Method = response.Method,
            Uri = response.Uri,
            ContentType = GetContentType(response.Headers),
            ContentEncoding = GetContentEncoding(response.Headers)
        };

        if ((int)response.StatusCode == 304)
        {
            processed.DisplayContent = "Content not modified (cached)";
            processed.ContentCategory = "cached";
            return Task.FromResult(processed);
        }

        if (response.Content == null || response.Content.Length == 0)
        {
            processed.DisplayContent = "(empty)";
            processed.ContentCategory = "empty";
            return Task.FromResult(processed);
        }

        var decompressedContent = TryDecompressContent(response.Content, processed.ContentEncoding);
        var actualContent = decompressedContent ?? response.Content;

        if (response.IsAmf)
        {
            processed.IsAmf = true;
            processed.ContentCategory = "amf";

            if (response.Amf != null)
            {
                processed.AmfData = ConvertAmfData(response.Amf);
                processed.DisplayContent = GetAmfDisplayContent(response.Amf);
            }
            else
            {
                processed.DisplayContent = "AMF decode failed";
            }

            return Task.FromResult(processed);
        }

        // Convert content to string for analysis
        var contentString = Encoding.UTF8.GetString(actualContent);
        processed.RawContent = contentString;

        // Detect content type and process accordingly
        if (IsJsonContent(processed.ContentType, contentString))
        {
            processed.ContentCategory = "json";
            processed.DisplayContent = FormatJsonContent(contentString);
        }
        else if (IsHtmlContent(processed.ContentType, contentString))
        {
            processed.ContentCategory = "html";
            processed.DisplayContent = TruncateContent(contentString, 2000);
        }
        else if (IsTextContent(processed.ContentType, contentString))
        {
            processed.ContentCategory = "text";
            processed.DisplayContent = TruncateContent(contentString, 2000);
        }
        else
        {
            processed.ContentCategory = "binary";
            processed.DisplayContent = $"Binary content ({actualContent.Length} bytes)";
            processed.BinaryContent = actualContent;
        }

        return Task.FromResult(processed);
    }

    private Dictionary<string, string> ConvertHeaders(Dictionary<string, string[]> headers)
    {
        var result = new Dictionary<string, string>();
        foreach (var header in headers)
        {
            result[header.Key] = string.Join(", ", header.Value);
        }
        return result;
    }

    private AmfInterceptedData? ConvertAmfData(IAmfInterceptedData? amfData)
    {
        if (amfData == null) return null;
        
        return new AmfInterceptedData
        {
            DecodedContent = amfData.DecodedContent,
            Error = amfData.Error
        };
    }    private string GetAmfDisplayContent(IAmfInterceptedData amfData)
    {
        if (amfData.DecodedContent?.Content != null)
        {
            try
            {
                // Try to convert AMF content to JSON for better display
                var jsonString = amfData.DecodedContent.Content.ToJson(new JsonSerializerSettings(){ Formatting = Formatting.Indented });
                if (!string.IsNullOrEmpty(jsonString))
                    return jsonString;
                    
                // Fallback to string representation
                return amfData.DecodedContent.Content.ToString() ?? "AMF content (non-serializable)";
            }
            catch (Exception ex)
            {
                return $"AMF content (JSON conversion failed: {ex.Message})";
            }
        }
        
        return amfData.Error ?? "AMF decode failed";
    }

    private string GetContentType(Dictionary<string, string[]> headers)
    {
        if (headers.TryGetValue("Content-Type", out var contentTypeValues) && contentTypeValues.Length > 0)
            return contentTypeValues[0].Split(';')[0].Trim().ToLowerInvariant();
        
        return "unknown";
    }

    private string GetContentEncoding(Dictionary<string, string[]> headers)
    {
        if (headers.TryGetValue("Content-Encoding", out var encodingValues) && encodingValues.Length > 0)
            return encodingValues[0].Trim().ToLowerInvariant();
        
        return "none";
    }    private byte[]? TryDecompressContent(byte[] content, string encoding)
    {
        try
        {
            return encoding switch
            {
                "gzip" => DecompressGzip(content),
                "deflate" => DecompressDeflate(content),
                "br" => DecompressBrotli(content),
                _ => null
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to decompress content with encoding: {Encoding}", encoding);
            return null;
        }
    }

    private byte[] DecompressGzip(byte[] compressedData)
    {
        try
        {
            if (IsValidGzipHeader(compressedData))
            {
                using var compressedStream = new MemoryStream(compressedData);
                using var gzipStream = new GZipStream(compressedStream, CompressionMode.Decompress);
                using var resultStream = new MemoryStream();
                gzipStream.CopyTo(resultStream);
                return resultStream.ToArray();
            }
            // If not a valid gzip header, try raw inflate as fallback
            return DecompressRawDeflate(compressedData);
        }
        catch (InvalidDataException ex)
        {
            _logger.LogWarning(ex, "Gzip decompression failed, attempting raw inflate fallback");
            try
            {
                return DecompressRawDeflate(compressedData);
            }
            catch (Exception fallbackEx)
            {
                throw new InvalidDataException($"Gzip and raw inflate decompression failed: {fallbackEx.Message}", fallbackEx);
            }
        }
    }

    private byte[] DecompressDeflate(byte[] compressedData)
    {
        try
        {
            using var compressedStream = new MemoryStream(compressedData);
            using var deflateStream = new DeflateStream(compressedStream, CompressionMode.Decompress);
            using var resultStream = new MemoryStream();
            
            deflateStream.CopyTo(resultStream);
            return resultStream.ToArray();
        }
        catch (InvalidDataException)
        {
            // Try raw deflate without zlib wrapper
            return DecompressRawDeflate(compressedData);
        }
    }
    
    private byte[] DecompressBrotli(byte[] compressedData)
    {
        try
        {
            using var compressedStream = new MemoryStream(compressedData);
            using var brotliStream = new BrotliStream(compressedStream, CompressionMode.Decompress);
            using var resultStream = new MemoryStream();
            
            brotliStream.CopyTo(resultStream);
            return resultStream.ToArray();
        }
        catch (Exception ex)
        {
            throw new InvalidDataException($"Brotli decompression failed: {ex.Message}", ex);
        }
    }
    
    private byte[] DecompressRawDeflate(byte[] compressedData)
    {
        try
        {
            // Skip potential zlib header (2 bytes) and try raw deflate
            var dataToDecompress = compressedData.Length > 2 && 
                                 compressedData[0] == 0x78 && 
                                 (compressedData[1] == 0x01 || compressedData[1] == 0x9C || compressedData[1] == 0xDA)
                                 ? compressedData.Skip(2).ToArray()
                                 : compressedData;
            
            using var compressedStream = new MemoryStream(dataToDecompress);
            using var deflateStream = new DeflateStream(compressedStream, CompressionMode.Decompress);
            using var resultStream = new MemoryStream();
            
            deflateStream.CopyTo(resultStream);
            return resultStream.ToArray();
        }
        catch (Exception ex)
        {
            throw new InvalidDataException($"Raw deflate decompression failed: {ex.Message}", ex);
        }
    }
    
    private static bool IsValidGzipHeader(byte[] data)
    {
        // Gzip header: magic bytes (0x1f, 0x8b) + compression method (0x08)
        return data.Length >= 3 && 
               data[0] == 0x1f && 
               data[1] == 0x8b && 
               data[2] == 0x08;
    }

    private bool IsJsonContent(string contentType, string content)
    {
        if (contentType.Contains("json"))
            return true;

        // Check if content looks like JSON
        var trimmed = content.Trim();
        return (trimmed.StartsWith('{') && trimmed.EndsWith('}')) ||
               (trimmed.StartsWith('[') && trimmed.EndsWith(']'));
    }

    private bool IsHtmlContent(string contentType, string content)
    {
        if (contentType.Contains("html"))
            return true;

        // Check if content looks like HTML
        var trimmed = content.Trim();
        return trimmed.StartsWith("<!DOCTYPE", StringComparison.OrdinalIgnoreCase) ||
               trimmed.StartsWith("<html", StringComparison.OrdinalIgnoreCase) ||
               trimmed.Contains("<body", StringComparison.OrdinalIgnoreCase);
    }

    private bool IsTextContent(string contentType, string content)
    {
        if (contentType.StartsWith("text/"))
            return true;

        // Check if content is likely text by looking for common text patterns
        var sampleSize = Math.Min(content.Length, 1000);
        var sample = content.AsSpan(0, sampleSize);
        
        var printableChars = 0;
        foreach (var c in sample)
        {
            if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                continue;
            if (c < 32 || c > 126)
                continue;
            printableChars++;
        }

        return printableChars > sampleSize * 0.8; // 80% printable characters
    }    private string FormatJsonContent(string json)
    {
        try
        {
            using var document = JsonDocument.Parse(json);
            return System.Text.Json.JsonSerializer.Serialize(document.RootElement, new JsonSerializerOptions 
            { 
                WriteIndented = true 
            });
        }
        catch
        {
            return TruncateContent(json, 2000);
        }
    }

    private string TruncateContent(string content, int maxLength)
    {
        if (content.Length <= maxLength)
            return content;

        return content.Substring(0, maxLength) + $"... ({content.Length - maxLength} more characters)";
    }
}
