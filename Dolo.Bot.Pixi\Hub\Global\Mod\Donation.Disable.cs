﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Donation
    {
        [Command("disable")]
        [Description("disable the donation")]
        public async Task DisableAsync(SlashCommandContext ctx)
        {
            if (ctx.User.Id != 440584675740876810) return;

            var setting = await Mongo.PixiSettings.GetFirstAsync();
            if (!setting?.IsDonation ?? false)
            {
                await ctx.TryCreateResponseAsync("Donation already disabled");
                return;
            }

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(a => a.IsDonation, false));

            await ctx.TryCreateResponseAsync("Donation disabled");
        }
    }
}