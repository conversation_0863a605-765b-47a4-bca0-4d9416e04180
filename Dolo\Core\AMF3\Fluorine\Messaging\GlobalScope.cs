﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using IServiceProvider=Dolo.Core.AMF3.Fluorine.Messaging.Api.IServiceProvider;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     The global scope that acts as root for all applications in a host.
/// </summary>
internal class GlobalScope : Scope, IGlobalScope
{

    #region IGlobalScope Members

    public IServiceProvider ServiceProvider => _serviceContainer;

    public void Register()
    {
        Init();
    }

    #endregion
}