﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("unlimit")]

[Description("set a limit in the voice channel")]
    public async Task VoiceUnlimitAsync(SlashCommandContext ctx)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
            return;
        }

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // try to get the channel 
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
            return;

        // check if the limit is the same
        if (channel.UserLimit == 0)
        {
            await ctx.TryEditResponseAsync("The channel is already unlimited.");
            return;
        }

        // check if the user is allowed to perform the command
        if (usr.Owner != ctx.User.Id && !usr.Moderator.Contains(ctx.User.Id))
        {
            await ctx.TryEditResponseAsync("You are not a channel moderator.");
            return;
        }

        // modify the channel limit
        await channel.ModifyAsync(a => a.Userlimit = 0);

        // send the message
        await ctx.TryEditResponseAsync("The channel is now unlimited.");
    }
}