﻿namespace Dolo.Pluto.Shard.Services.License;

public class LicenseValidationResult
{
    public bool IsValid { get; private set; }
    public string? ErrorMessage { get; private set; }

    public LicenseValidationResult SetValid(bool isValid)
    {
        IsValid = isValid;
        return this;
    }

    public LicenseValidationResult SetErrorMessage(string? errorMessage)
    {
        ErrorMessage = errorMessage;
        return this;
    }
}