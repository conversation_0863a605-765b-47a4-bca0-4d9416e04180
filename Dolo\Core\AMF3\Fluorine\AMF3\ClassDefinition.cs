﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.AMF3;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal sealed class ClassDefinition
{

    internal static ClassMember[] EmptyClassMembers = new ClassMember[0];

    internal ClassDefinition(string className, ClassMember[] members, bool externalizable, bool dynamic)
    {
        ClassName = className;
        Members = members;
        IsExternalizable = externalizable;
        IsDynamic = dynamic;
    }

    /// <summary>
    ///     Gets the class name.
    /// </summary>
    public string? ClassName
    {
        get;
    }
    /// <summary>
    ///     Gets the class member count.
    /// </summary>
    public int MemberCount
    {
        get
        {
            if (Members == null)
                return 0;
            return Members.Length;
        }
    }
    /// <summary>
    ///     Gets the array of class members.
    /// </summary>
    public ClassMember[] Members
    {
        get;
    }
    /// <summary>
    ///     Indicates whether the class is externalizable.
    /// </summary>
    public bool IsExternalizable
    {
        get;
    }
    /// <summary>
    ///     Indicates whether the class is dynamic.
    /// </summary>
    public bool IsDynamic
    {
        get;
    }
    /// <summary>
    ///     Indicates whether the class is typed (not anonymous).
    /// </summary>
    public bool IsTypedObject => ClassName != null && ClassName != string.Empty;
}

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal sealed class ClassMember
{

    internal ClassMember(string name, BindingFlags bindingFlags, MemberTypes memberType)
    {
        Name = name;
        BindingFlags = bindingFlags;
        MemberType = memberType;
    }
    /// <summary>
    ///     Gets the member name.
    /// </summary>
    public string? Name
    {
        get;
    }
    /// <summary>
    ///     Gets the member binding flags.
    /// </summary>
    public BindingFlags BindingFlags
    {
        get;
    }
    /// <summary>
    ///     Gets the member type.
    /// </summary>
    public MemberTypes MemberType
    {
        get;
    }
}
