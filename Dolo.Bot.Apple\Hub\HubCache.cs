﻿using System.Collections.Concurrent;
namespace Dolo.Bot.Apple.Hub;

public static class <PERSON>b<PERSON>ache
{
    // caching for messages
    public static ConcurrentDictionary<ulong, DiscordMessage> Message { get; set; } = new();
    // caching for invites
    public static ConcurrentDictionary<string, DiscordInvite> Invite { get; set; } = new();
    public static Stream? FridayCommandStream { get; set; }


    /// <summary>
    ///     Check if the message is cached
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static bool HasMessage(ulong id) => Message.ContainsKey(id);

    /// <summary>
    ///     Add a message to the cache
    /// </summary>
    /// <param name="id"></param>
    /// <param name="message"></param>
    public static void TryAddOrUpdateMessage(ulong id, DiscordMessage message) => Message.AddOrUpdate(id, message, (key, val) => val = message);


    /// <summary>
    ///     Try to get a message from the cache
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static bool TryGetMessage(ulong id, out DiscordMessage? message) => Message.TryGetValue(id, out message);

    /// <summary>
    ///     Try to remove a message from the cache
    /// </summary>
    /// <param name="id"></param>
    public static void TryRemoveMessage(ulong id) => Message.TryRemove(id, out _);


    /// <summary>
    ///     Check if the invite is cached
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static bool HasInvite(string code) => Invite.ContainsKey(code);

    /// <summary>
    ///     Add a invite to the cache
    /// </summary>
    /// <param name="id"></param>
    /// <param name="message"></param>
    public static void TryAddInvite(string code, DiscordInvite invite) => Invite.TryAdd(code, invite);

    /// <summary>
    ///     Try to get a invite from the cache
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static bool TryGetInvite(string code, out DiscordInvite? invite) => Invite.TryGetValue(code, out invite);

    /// <summary>
    ///    Try to get a invite from the cache
    /// </summary>
    /// <param name="code"></param>
    /// <returns></returns>
    public static DiscordInvite? TryGetInvite(string code) => Invite.TryGetValue(code, out var invite) ? invite : null;

    /// <summary>
    ///    Get all invites from the member
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public static List<DiscordInvite> GetInvites(ulong id) =>  Invite.Values.Where(x => x.Inviter?.Id == id).ToList();

    /// <summary>
    ///     Try to remove a invite from the cache
    /// </summary>
    /// <param name="id"></param>
    public static void TryRemoveInvite(string code) => Invite.TryRemove(code, out _);
}