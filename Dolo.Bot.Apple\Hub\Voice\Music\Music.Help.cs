using System.Text;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

public partial class Music
{
    [Command("help")]
    [Description("show music commands help")]
    public async Task HelpAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(true);

        var embed = new DiscordEmbedBuilder()
            .WithTitle($"Music Commands {HubEmoji.Astro}")
            .WithColor(new DiscordColor("D6EDFB"))
            .WithThumbnail("https://cdn.cbkdz.eu/img/iMusic.jpeg")
            .WithDescription(new StringBuilder()
                .AppendLine("**🎵 Music Bot Commands**")
                .AppendLine("Use these commands in your voice channel's text chat")
                .AppendLine()
                .AppendLine("**Basic Playback**")
                .AppendLine($"{HubEmoji.Apple} `/music play <query>` - Play a song or add to queue")
                .AppendLine($"{HubEmoji.Apple} `/music join` - Connect bot to voice channel")
                .AppendLine($"{HubEmoji.Apple} `/music pause` - Pause current track")
                .AppendLine($"{HubEmoji.Apple} `/music resume` - Resume paused track")
                .AppendLine($"{HubEmoji.Apple} `/music skip` - Skip current track")
                .AppendLine($"{HubEmoji.Apple} `/music stop` - Stop playback and clear queue")
                .AppendLine($"{HubEmoji.Apple} `/music disconnect` - Disconnect from voice")
                .AppendLine()
                .AppendLine("**Queue Management**")
                .AppendLine($"{HubEmoji.Apple} `/music queue [page]` - Show music queue")
                .AppendLine($"{HubEmoji.Apple} `/music clear` - Clear the queue")
                .AppendLine($"{HubEmoji.Apple} `/music shuffle` - Shuffle the queue")
                .AppendLine($"{HubEmoji.Apple} `/music remove <position>` - Remove track from queue")
                .AppendLine($"{HubEmoji.Apple} `/music playnext <query>` - Add song to play next")
                .AppendLine()
                .AppendLine("**Advanced Features**")
                .AppendLine($"{HubEmoji.Apple} `/music search <query>` - Search and select tracks")
                .AppendLine($"{HubEmoji.Apple} `/music playlist <url>` - Add playlist to queue")
                .AppendLine($"{HubEmoji.Apple} `/music loop` - Toggle loop mode")
                .AppendLine($"{HubEmoji.Apple} `/music volume <0-100>` - Set playback volume")
                .AppendLine($"{HubEmoji.Apple} `/music nowplaying` - Show current track info")
                .AppendLine($"{HubEmoji.Apple} `/music status` - Show player status")
                .AppendLine()
                .AppendLine("**Supported Sources**")
                .AppendLine("🎵 YouTube (videos & playlists)")
                .AppendLine("🎵 Spotify (tracks & playlists)")
                .AppendLine("🎵 SoundCloud")
                .AppendLine("🎵 Direct audio URLs")
                .ToString())
            .WithFooter($"Music system powered by Dolo {HubEmoji.WhiteHeart}")
            .WithTimestamp(DateTimeOffset.UtcNow);

        await ctx.TryEditResponseAsync(embed.Build());
    }
}
