﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMFSerializer : AMFWriter
{
    private readonly MemoryStream Stream;

    /// <summary>
    ///     Initializes a new instance of the AMFSerializer class.
    /// </summary>
    /// <param name="stream"></param>
    public AMFSerializer(AMFMessage message)
    {
        Stream = new();
        SetBinary(Stream);
        WriteMessage(message);
        BinaryWriter.Flush();
        BinaryWriter.Close();
    }

    /// <summary>
    ///     Initializes a new instance of the AMFSerializer class.
    /// </summary>
    /// <param name="writer"></param>
    /// <param name="stream"></param>
    internal AMFSerializer(AMFWriter writer, Stream stream)
        : base(writer, stream)
    {}

    public byte[] GetData() => Stream.ToArray();

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="amfMessage"></param>
    public void WriteMessage(AMFMessage amfMessage)
    {
        WriteShort(amfMessage.Version);
        var headerCount = amfMessage.HeaderCount;
        WriteShort(headerCount);
        for (var i = 0; i < headerCount; i++) WriteHeader(amfMessage.GetHeaderAt(i), ObjectEncoding.AMF0);
        var bodyCount = amfMessage.BodyCount;
        WriteShort(bodyCount);
        for (var i = 0; i < bodyCount; i++)
        {
            var bodyat = amfMessage.TryGetBodyAt(i);
            if (bodyat is null)
                continue;


            var responseBody = bodyat as ResponseBody;
            if (responseBody is { IgnoreResults: false })
            {
                //Try to catch serialization errors
                if (Stream.CanSeek)
                {
                    var position = Stream.Position;

                    try
                    {
                        responseBody.WriteBody(amfMessage.ObjectEncoding, this);
                    }
                    catch (Exception exception)
                    {
                        Stream.Seek(position, SeekOrigin.Begin);
                        //this.BaseStream.Position = position;

                        ErrorResponseBody errorResponseBody;
                        if (responseBody.RequestBody.IsEmptyTarget)
                        {
                            var content = responseBody.RequestBody.Content;
                            if (content is IList)
                                content = (content as IList)[0];
                            var message = content as IMessage;
                            var messageException = new MessageException(exception);
                            messageException.FaultCode = __Res.GetString(__Res.Amf_SerializationFail);
                            errorResponseBody =
                                new(responseBody.RequestBody, message, messageException);
                        }
                        else
                            errorResponseBody = new(responseBody.RequestBody, exception);

                        errorResponseBody.WriteBody(amfMessage.ObjectEncoding, this);
                    }
                }
                else
                    responseBody.WriteBody(amfMessage.ObjectEncoding, this);
            }
            else
            {
                var amfBody = amfMessage.TryGetBodyAt(i);
                ValidationUtils.ObjectNotNull(amfBody, "amfBody");
                amfBody.WriteBody(amfMessage.ObjectEncoding, this);
            }
        }
    }

    private void WriteHeader(AMFHeader header, ObjectEncoding objectEncoding)
    {
        Reset();
        WriteUTF(header.Name);
        WriteBoolean(header.MustUnderstand);
        WriteInt32(-1);
        WriteData(objectEncoding, header.Content);
    }
}