﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class ResponseBody : AMFBody
{

    /// <summary>
    ///     Initializes a new instance of the ResponseBody class.
    /// </summary>
    internal ResponseBody()
    {}
    /// <summary>
    ///     Initializes a new instance of the ResponseBody class.
    /// </summary>
    /// <param name="requestBody"></param>
    public ResponseBody(AMFBody requestBody) => RequestBody = requestBody;
    /// <summary>
    ///     Initializes a new instance of the ResponseBody class.
    /// </summary>
    /// <param name="requestBody"></param>
    /// <param name="content"></param>
    public ResponseBody(AMFBody requestBody, object content)
    {
        RequestBody = requestBody;
        _target = requestBody.Response + OnResult;
        _content = content;
        _response = "null";
    }
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public AMFBody RequestBody
    {
        get;
        set;
    }
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public override IList GetParameterList() => RequestBody?.GetParameterList();
}