﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     Implements a read-only collection.
/// </summary>
internal class ReadOnlyCollection : ICollection, IEnumerable
{
    private readonly ICollection _collection;
    private object _syncRoot;

    /// <summary>
    ///     Creates a ReadOnlyCollection wrapper for a specific collection.
    /// </summary>
    /// <param name="collection">The collection to wrap.</param>
    public ReadOnlyCollection(ICollection collection)
    {
        if (collection == null)
            throw new ArgumentNullException("list");
        _collection = collection;
    }

    #region IEnumerable Members

    /// <summary>
    ///     Returns an enumerator that iterates through an ReadOnlyCollection.
    /// </summary>
    /// <returns>An IEnumerator object that can be used to iterate through the collection.</returns>
    public IEnumerator GetEnumerator() => _collection.GetEnumerator();

    #endregion


    #region ICollection Members

    /// <summary>
    ///     Copies the elements of the ReadOnlyCollection to an Array, starting at a particular Array index.
    /// </summary>
    /// <param name="array">
    ///     The one-dimensional Array that is the destination of the elements copied from ReadOnlyCollection.
    ///     The Array must have zero-based indexing.
    /// </param>
    /// <param name="index">The zero-based index in array at which copying begins.</param>
    public void CopyTo(Array array, int index)
    {
        _collection.CopyTo(array, index);
    }
    /// <summary>
    ///     Gets the number of elements contained in the ReadOnlyCollection.
    /// </summary>
    public int Count => _collection.Count;
    /// <summary>
    ///     Gets a value indicating whether access to the ReadOnlyCollection is synchronized (thread safe).
    /// </summary>
    public bool IsSynchronized => false;
    /// <summary>
    ///     Gets an object that can be used to synchronize access to the ReadOnlyCollection.
    /// </summary>
    public object SyncRoot
    {
        get
        {
            if (_syncRoot == null)
            {
                if (_collection != null)
                    _syncRoot = _collection.SyncRoot;
                else
                    Interlocked.CompareExchange(ref _syncRoot, new(), null);
            }
            return _syncRoot;
        }
    }

    #endregion
}