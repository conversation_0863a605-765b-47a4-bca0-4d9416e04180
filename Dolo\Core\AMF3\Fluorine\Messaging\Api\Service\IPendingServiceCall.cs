﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Service;

/// <summary>
///     IPendingServiceCall is a service call with a list of callbacks.
/// </summary>
internal interface IPendingServiceCall : IServiceCall
{
    /// <summary>
    ///     Gets or sets the service call result.
    /// </summary>
    object Result { get; set; }
    /// <summary>
    ///     Registers callback object that implements IPendingServiceCallback interface.
    /// </summary>
    /// <param name="callback"></param>
    void RegisterCallback(IPendingServiceCallback callback);
    /// <summary>
    ///     Unregisters callback object that implements IPendingServiceCallback interface.
    /// </summary>
    /// <param name="callback"></param>
    void UnregisterCallback(IPendingServiceCallback callback);
    /// <summary>
    ///     Returns list of callback objects that implements IPendingServiceCallback.
    /// </summary>
    /// <returns></returns>
    IPendingServiceCallback[] GetCallbacks();
}