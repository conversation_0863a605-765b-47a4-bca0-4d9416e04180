﻿using Dolo.Core;
using Dolo.Core.Consola;
using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [Command("level")]

[Description("shows the level and points")]
    public async Task LevelAsync(SlashCommandContext ctx, [Description("to show the points of a user (optional)")] DiscordUser? user = null)
    {
        await ctx.Interaction.DeferAsync();

        try
        {
            var memberId = user == null ? ctx.Member.Id : user.Id;
            var level = await Mongo.ServerSettings.GetFirstAsync();
            var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == memberId);

            // print user not found when user is not in the database
            if (member is null)
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.GhostLove} » User not found.");
                return;
            }
            
            await ctx.TryEditResponseAsync($"{HubEmoji.GhostLove} <@{ctx.User.Id}> » {(user == null ? "You are" : $"{member.Member.Username} is level")} `{member.Level.Level}` with `{member.Level.Points:N0}` / `{(member.Level.IsEpic ? "∞" : member.Level.GetPointsNextLevel(level!.Level).ToString("N0"))}` points.");
        }
        catch (Exception ex)
        {
            Consola.Error(ex.ToJson());
        }
    }
}