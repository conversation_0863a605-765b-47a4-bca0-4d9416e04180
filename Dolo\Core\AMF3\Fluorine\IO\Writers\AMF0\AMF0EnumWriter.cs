﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF0;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF0EnumWriter : IAMFWriter
{
    #region IAMFWriter Members

    public bool IsPrimitive
        => true;

    public void WriteData(AMFWriter writer, object data)
    {
        writer.WriteByte(AMF0TypeCode.Number);
        double dbl = Util.Convert.ToInt32(data);
        writer.WriteDouble(dbl);
    }

    #endregion
}