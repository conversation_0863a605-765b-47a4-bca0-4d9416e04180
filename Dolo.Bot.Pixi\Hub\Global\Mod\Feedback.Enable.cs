﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Feedback
    {
        [Command("enable")]

[Description("enable the feedback")]
        public async Task EnableAsync(SlashCommandContext ctx)
        {
            if (ctx.User.Id != 440584675740876810) return;

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(a => a.IsFeedback, true));

            await ctx.TryCreateResponseAsync("Feedback enabled");
        }
    }
}