﻿namespace Dolo.Core.Discord;

public static class DiscordUserExtension
{
    /// <summary>
    ///     Try to unban the user
    /// </summary>
    /// <param name="user">The Discord user to unban.</param>
    /// <param name="guild">The guild to unban from.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    /// <returns></returns>
    public static async Task TryUnbanAsync(this DiscordUser user, DiscordGuild guild, Action<Exception>? onError = null)
        => await user.UnbanAsync(guild).TryAsync(onError);

    /// <summary>
    ///     Try to get the user
    /// </summary>
    /// <param name="user">The Discord client.</param>
    /// <param name="id">The ID of the user to get.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordUser?> TryGetUserAsync(this DiscordClient user, ulong id, Action<Exception>? onError = null)
        => await user.GetUserAsync(id).TryAsync(onError);
}