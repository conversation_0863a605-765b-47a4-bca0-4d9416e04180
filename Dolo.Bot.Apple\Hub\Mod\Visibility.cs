namespace Dolo.Bot.Apple.Hub.Mod;

public class ChannelVisibility
{
    private readonly List<DiscordChannel> _ignoreChannels = [HubChannel.AdminCategory, HubChannel.Hello];

    [RequirePermissions(DiscordPermission.ManageMessages)]
    [Command("Visibility")]
    [Description("Hide or show every channel from all members")]
    public async Task UpdateVisibilityAsync(SlashCommandContext ctx, [Description("true to show, false to hide")] bool visible)
    {
        await ctx.Interaction.DeferAsync(true);

        var channels = ctx.Guild?.Channels;
        if (channels is null || channels.Count == 0)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » This server has no channels");
            return;
        }

        foreach (var channel in channels.Values)
        {
            if (_ignoreChannels.Any(ch => ch.Id == channel.Id || ch.Id == channel.ParentId))
                continue;

            var permissions = channel.PermissionOverwrites.FirstOrDefault(a => a.Id == ctx.Guild!.EveryoneRole.Id);
            var allowed = permissions?.Allowed ?? DiscordPermissions.None;
            var denied = permissions?.Denied ?? DiscordPermissions.None;
               
            if(visible && allowed.HasFlag(DiscordPermission.ViewChannel))
                continue;
            if(!visible && denied.HasFlag(DiscordPermission.ViewChannel))
                continue;

            if (visible)
            {
                allowed.Add(DiscordPermission.ViewChannel);
                denied.Remove(DiscordPermission.ViewChannel);
            }else 
            {
                allowed.Remove(DiscordPermission.ViewChannel);
                denied.Add(DiscordPermission.ViewChannel);
            }
            await channel.TryAddOverrideAsync(ctx.Guild!.EveryoneRole, allowed, denied);
        }

        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Channels are now {(visible ? "visible" : "hidden")}");
    }
}
