using Dolo.Pluto.Shard.Configuration;
using Dolo.Pluto.Shard.Hub;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Shard.Services.Api;
using Dolo.Pluto.Shard.Services.Initialization;
using Dolo.Pluto.Shard.Services.License;
using Dolo.Pluto.Shard.Services.Logging;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Extensions;

public static class MauiAppBuilderExtensions
{
    private static WebApplication? _webApp;
    public static WebApplication? WebApp => _webApp;

    private static void RegisterCoreServices(IServiceCollection services, IAppConfiguration appConfig)
    {
        services.AddSingleton<IAppConfiguration>(appConfig);
        services.AddSingleton<UrlBuilderService>();

        services.AddSingleton<IToolboxApiService, ToolboxApiService>();
        services.AddSingleton<ILicenseApiService, LicenseApiService>();
        services.AddSingleton<ApiService>();

        services.AddSingleton<ILicenseFileService, LicenseFileService>();
        services.AddSingleton<ILicenseValidationService, LicenseValidationService>();
        services.AddSingleton<ILicenseService, LicenseService>();

        services.AddSingleton<IInitializationStateManager, InitializationStateManager>();
        services.AddSingleton<IInitializationProgressTracker, InitializationProgressTracker>();
        services.AddSingleton<IInitializationWorkflow, InitializationWorkflow>();

        services.AddSingleton<AuthenticationService>();
        services.AddSingleton<IReportService, ReportService>();

        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
            builder.AddProvider(new SimpleFileLoggerProvider());
        });

        services.AddSingleton<ILoggerFactory>(provider =>
        {
            var factory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
                builder.AddProvider(new SimpleFileLoggerProvider());
                builder.SetMinimumLevel(LogLevel.Information);
            });

            return factory;
        });
    }
    public static IServiceCollection AddSharedServices(this IServiceCollection collection, IAppConfiguration appConfig)
    {
        RegisterCoreServices(collection, appConfig);
        collection.AddSignalRCore();
        return collection;
    }

    public static IServiceCollection AddAutoServices(this IServiceCollection collection)
    {
        var serviceTypes = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(s => s.GetTypes())
            .Where(p => typeof(IService).IsAssignableFrom(p) && p.IsClass);

        foreach (var serviceType in serviceTypes)
            collection.AddScoped(serviceType);

        return collection;
    }
    public static MauiAppBuilder ConfigureSharedWebHost(this MauiAppBuilder builder,
        IAppConfiguration appConfig,
        Action<WebApplication>? configureApp = null)
    {
        var webHostBuilder = WebApplication.CreateSlimBuilder(new WebApplicationOptions
        {
            Args = [$"--urls=http://localhost:{appConfig.WebHostPort}"]
        });

        RegisterCoreServices(webHostBuilder.Services, appConfig);
        webHostBuilder.Services.AddSignalR();
        webHostBuilder.Services.AddCors();

        _webApp = webHostBuilder.Build();
        _webApp.UseRouting();

        _webApp.UseCors(options =>
            options.SetIsOriginAllowed(origin =>
                origin.StartsWith("http://127.0.0.1") ||
                origin.StartsWith("https://localhost") ||
                origin.StartsWith("http://localhost") ||
                origin.Contains("cbkdz.eu"))
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials());

        _webApp.MapHub<AuthenticationHub>(appConfig.AppHub);
        configureApp?.Invoke(_webApp);

        var hostTask = Task.Run(async () =>
        {
            var loggerFactory = _webApp.Services.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("WebHost");

            try
            {
                await _webApp.StartAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to start web server on port {Port}", appConfig.WebHostPort);

                try
                {
                    var reportService = _webApp?.Services?.GetRequiredService<IReportService>();
                    if (reportService != null)
                    {
                        await reportService.ReportErrorAsync(
                            $"Failed to start Pluto Shard SignalR server on port {appConfig.WebHostPort}",
                            "WebHost Startup",
                            ex);
                    }
                }
                catch
                {
                }
            }
        });

        return builder;
    }
}
