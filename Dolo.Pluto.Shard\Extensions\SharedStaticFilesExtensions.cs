using Dolo.Pluto.Shard.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace Dolo.Pluto.Shard.Extensions;

/// <summary>
/// Extensions for configuring shared static files from Shard (for web applications only)
/// </summary>
public static class SharedStaticFilesExtensions
{
    /// <summary>
    /// Configures the web application to serve shared static files from Shard
    /// </summary>
    public static WebApplication UseSharedStaticFiles(this WebApplication app)
    {
        var staticFileService = new SharedStaticFileService();

        // Map all available shared static files
        foreach (var filePath in staticFileService.GetAvailableFiles())
        {
            var routePath = $"/{filePath}";

            app.MapGet(routePath, async context =>
            {
                await using var stream = staticFileService.GetFileStream(filePath);
                if (stream != null)
                {
                    context.Response.ContentType = staticFileService.GetContentType(filePath);

                    // Add caching headers for better performance
                    context.Response.Headers.CacheControl = "public, max-age=3600"; // 1 hour cache
                    context.Response.Headers.ETag = $"\"{filePath.GetHashCode()}\"";

                    await stream.CopyToAsync(context.Response.Body);
                }
                else
                {
                    context.Response.StatusCode = 404;
                }
            });
        }

        return app;
    }
}
