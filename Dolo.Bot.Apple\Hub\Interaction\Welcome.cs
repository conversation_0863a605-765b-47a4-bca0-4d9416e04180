﻿namespace Dolo.Bot.Apple.Hub.Interaction;

public static class Welcome
{
    public static async Task HandleWelcomeAsync(this ComponentInteractionCreatedEventArgs args)
    {
        await args.Interaction.TryCreateResponseMessageAsync(DiscordInteractionResponseType.DeferredMessageUpdate);

        if (args.Id == "question-pluto-verify")
            await args.QuestionPlutoVerificationAsync();
    }



    private static async Task QuestionPlutoVerificationAsync(this InteractionCreatedEventArgs args)
    {
        var embed = HubEmbed.InteractionQuestionPlutoVerify();
        var builder = new DiscordFollowupMessageBuilder()
            .AddEmbed(embed.Embeds[0])
            .AsEphemeral();

        // Only add components if they exist
        if (embed.Components.Any())
            builder.AddActionRowComponent((DiscordActionRowComponent)embed.Components[0]);

        await args.Interaction.TryCreateFollowupMessageAsync(builder);
    }


}
