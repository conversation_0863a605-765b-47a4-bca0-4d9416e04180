using System.Net;
using System.Net.Sockets;

namespace Dolo.Pluto.Shard.Configuration;

public abstract class AppConfiguration : IAppConfiguration
{
    public abstract string AppId { get; }
    public abstract string AppVersion { get; }
    public abstract string AppHub { get; }
    public bool IsDevMode { get; }
    public string ApiDomain => IsDevMode ? "https://test.cbkdz.eu/" : "https://msp.cbkdz.eu/";
    public int WebHostPort { get; private set; }

    protected AppConfiguration()
    {
#if RELEASE
        IsDevMode = false;
#else
        IsDevMode = true;
#endif
        WebHostPort = GetAvailablePort();
    }

    private static int GetAvailablePort()
    {
        var listener = new TcpListener(IPAddress.Loopback, 0);
        try
        {
            listener.Start();
            return ((IPEndPoint)listener.LocalEndpoint).Port;
        }
        finally
        {
            listener.Stop();
        }
    }
}
