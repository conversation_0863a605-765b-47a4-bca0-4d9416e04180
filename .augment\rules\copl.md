---
type: "always_apply"
---

# languages: C#, JavaScript, TypeScript, HTML, CSS, Razor, Blazor, TailwindCSS
# frameworks: .NE<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>act, 
# Never do:
- Never create any kind of readme files, documentation, or comments in the code
- Never create another class inside an existing class
- Never create any kind of code that is not related to the task at hand
- Never create a readme.md file or any other kind of documentation
- Never create any example files or folders.
- Never create any ps1, batch or any other scripts to move, edit, or delete files

# Core Instructions assistant
- Use .NET 10 previews.
- Use c# 14 previews.
- Use tasks to guide your coding process
- Use emojis for task status updates
- Use explicit requested features and avoid assumptions
- Use AsyncEventHandler for event handlers from Dolo.Core project
- Use less or no else statements aswell as try catch statements
- Use LINQ, async/await, inline methods, ters syntax, Null-conditional assignment, File Scoped Namespaces, and other modern features where applicable
- Use var instead of explicit type
- Use simplified collection initializers [] for all collections and .ToList() extensions
- Use simplified member access
- Use primary constructors for classes
- Use TryGetValue instead of GetValue
- Use switch expressions instead of switch statements
- Use simple using statements
- Use range operators
- Use multi-line formatting for ternary operators when expressions exceed readable length
- Use expression-bodied members for methods and properties
- Use the memory based overloads for streams ReadOnlyMemory<byte>
- Use the provided codebase as a reference for style, structure, and conventions
- Use the code style and conventions of the provided codebase
- Use file separation for different concerns
- Use razor.cs files for cs code in Blazor projects and not in the @code block 
- Use Latest stable versions of languages and frameworks
- Use TailwindCSS for styling in frontend projects
- Use existing code when improving or enhancing methods to avoid breaking code functionality
- Use always the best, improved, enhanced, optimized code to get the best performance and user experience
- Use ConfigureAwait(false) on all async calls
- Use professional, clean designs with subtle interactions and proper contrast
- Ask the user for clarification if the requirements are not clear
- Ask the user for confirmation before making any changes to existing code
- Ask the user for confirmation on any html/design/ui/ux changes 
- Take the prompt of the user and make it grammatically correct and clear before proceeding with the task
- Make a summary of the changes made at the end of the task and ask for confirmation
- Make sure to use existing html ui/ux components and elements to work with the design system
- Make sure each file have only one class, interface or other type for a clean codebase
- Make sure that there is no code behind any brackets or any semi-colons always use new lines
- Make sure to always refactor code based on the instructions
- Do a codebase lookup to understand the UI/UX design system and how the user wants the UI/UX to look like
- Do not use any RenderFragment or RenderFragment<T> in razor.cs files. We will always use .razor components for that
- Do not use RenderFragment (AddElement, OpenElement, CloseElement, etc.) in razor.cs files. We will always use .razor components for that
- Do not add any new html elements or components for a design task
- Do not add any comments on any code, properties, or methods always write it on top of the file via a summary and remove any existing comments
- Do not build or continue testing any application
- Do not use any <style> tags in the razor files
- Do not use AI-generated aesthetics, no excessive glow effects, floating particles, or over-animated elements
- Focus on usability over flashiness