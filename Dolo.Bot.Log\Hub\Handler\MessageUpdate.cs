﻿using Dolo.Core.Discord;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Log.Hub.Handler;

public static class MessageUpdate
{
    public static async Task InvokeAsync(this MessageUpdatedEventArgs e)
    {
        // check for null message
        if (e.Message is null)
            return;

        // this will update the message in the cache
        HubCache.AddMessage(e.Message.Id, e.Message);

        // check if the message is from a bot or user
        if (HubChannel.Log is null || e.Message.Author?.Id == Hub.Discord?.CurrentUser.Id || (e.Message.Author?.IsBot ?? false))
            return;

        // send the message
        await HubChannel.Log.TrySendMessageAsync(HubEmbed.MessageUpdated(e.Message, e.MessageBefore));
    }
}
