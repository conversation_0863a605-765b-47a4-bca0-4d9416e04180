﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     Base interface for all services.
/// </summary>
internal interface IService
{
    /// <summary>
    ///     Start service.
    /// </summary>
    /// <param name="configuration">Application configuration.</param>
    void Start();
    /// <summary>
    ///     Shutdown service.
    /// </summary>
    void Shutdown();
}