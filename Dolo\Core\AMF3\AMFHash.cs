using Dolo.Core.AMF3.Fluorine.AMF3;
namespace Dolo.Core.AMF3;

internal static class AMFHash
{
    private const string GetLogDataSalt = "2zKzokBI4^26#oiP";
    private const string GetEndDataSalt = "XSV7%!5!AX2L8@vn";


    internal static string? HashContentOriginal(object?[]? obj, bool encrypted = false)
    {
        if (obj is null) return default;

        var data = ConvertObjectArray(obj);
        var endData = GetTicketValue(obj);
        var logData = GetLogDataSalt;

        return BitConverter.ToString(SHA1.HashData(Encoding.UTF8.GetBytes($"{data}{logData}{endData}")))
            .Replace("-", "")
            .ToLower();
    }

    /// <summary>
    ///     Hash the objects. required to identify a valid request to MovieStarPlanet
    /// </summary>
    /// <param name="obj"></param>
    /// <param name="encrypted"></param>
    /// <returns></returns>
    internal static string? HashContent(object?[]? obj, bool encrypted = false)
        => obj is null ? default
               : BitConverter.ToString(SHA1.HashData(Encoding.UTF8.GetBytes($"{ConvertObjectArray(obj)}{GetLogDataSalt}{(encrypted ? GetTicketValue(obj) : GetEndDataSalt)}")))
                   .Replace("-", "")
                   .ToLower();
    /// <summary>
    ///     Hash the MovieStarPlanet 2 objects
    /// </summary>
    /// <param name="username"></param>
    /// <param name="password"></param>
    /// <param name="server"></param>
    /// <param name="gameid"></param>
    /// <param name="usefulparameter"></param>
    /// <returns></returns>
    internal static string HashContent(string username, string password, string server, string gameid, string usefulparameter = "false") => BitConverter.ToString(new HMACSHA256("7jA7^kAZSHtjxDAa"u8.ToArray())
            .ComputeHash(Encoding.UTF8.GetBytes($"{gameid.Replace("MSP_", "")}{server}{password}{username}{usefulparameter}")))
        .Replace("-", "")
        .ToLower();

    /// <summary>
    ///     Get the Last part of the Ticket
    /// </summary>
    /// <param name="ob"></param>
    /// <returns></returns>
    private static string GetTicketValue(IEnumerable<object?> ob)
    {
        var securityKey = GetEndDataSalt;

        foreach (var l in ob)
            if (l is AMFTicket to)
                securityKey = $"{to.Ticket?.Split(',')[0]}{to.Ticket?.Split(',')[^1][^5..]}";


        return securityKey;
    }

    /// <summary>
    ///     Creates a SessionId for the Methods that not require a SessionId
    /// </summary>
    /// <returns></returns>
    internal static string HashId()
    {
        var result = string.Empty;
        for (var i = 0; i < 48; i++)
            result += RandomNumberGenerator.GetInt32(0, int.MaxValue) * int.MaxValue;

        return System.Convert.ToBase64String(Encoding.UTF8.GetBytes(result[..46]));
    }

    /// <summary>
    ///     Get the type Object of the properties
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private static string GetTypeObject(object obj)
    {
        return (from property in obj.GetType().GetProperties()
                where property.Name    != "Length"
                      && property.Name != "LongLength"  && property.Name != "Rank"
                      && property.Name != "SyncRoot"    && property.Name != "IsReadOnly"
                      && property.Name != "IsFixedSize" && property.Name != "IsSynchronized"
                select property.GetValue(obj, null)
                into prop
                where prop is {}
                select prop)
            .Aggregate(string.Empty, (current, prop)
                => current + GetObjectValue(prop));
    }

    private static string SortObjectContent(object obj)
    {
        return obj.GetType()
            .GetProperties()
            .Select(c => c.Name)
            .OrderBy(a => a)
            .ToArray()
            .Aggregate(string.Empty, (current, name) => current + GetObjectValue(obj.GetType().GetProperty(name)?.GetValue(obj, null)));
    }


    /// <summary>
    ///     Split the data and converts it to a array
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    internal static string[] ToObjectArray(string data)
    {
        var result = new List<string>();

        result.AddRange(data.Split('\n', '\r'));

        return result.ToArray();
    }

    /// <summary>
    ///     Get the object type and convert it to the correct value
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    private static string? GetObjectValue(object? obj)
    {
        while (true)
        {
            switch (obj)
            {
                case AMFTicket:
                case null:
                    return "";
                case int:
                case string:
                    return obj.ToString();
                case bool:
                    return System.Convert.ToBoolean(obj) ? "True" : "False";
                case DateTime time:
                    return $"{time.Year}{DateTime.UtcNow.AddMonths(-1).Month}{DateTime.UtcNow.Day}";
                case byte[] b:
                    return ConvertObjectArray(b);
                case object[] b:
                    return ConvertObjectArray(b);
                case ArrayCollection b:
                    return ConvertObjectArray(b);
                case Array b:
                    return ConvertObjectArray(b);
            }
            if (!obj.GetType().IsGenericType)
                return SortObjectContent(obj);

            var objectList = (IList)obj;
            Array oArray = new object[objectList.Count];
            objectList.CopyTo(oArray, 0);
            obj = oArray;
        }
    }

    /// <summary>
    ///     Convert all objects to valid MovieStarPlanet formats
    /// </summary>
    /// <param name="o"></param>
    /// <returns></returns>
    private static string ConvertObjectArray(object? o)
    {
        var result = string.Empty;
        return o switch
        {
            object[] x         => x.Aggregate(result, (current, l) => current                  + GetObjectValue(l)),
            Array a            => a.Cast<object?>().Aggregate(result, (current, l) => current  + GetObjectValue(l)),
            ArrayCollection ac => ac.Cast<object?>().Aggregate(result, (current, l) => current + GetObjectValue(l)),
            _                  => result
        };
    }

    /// <summary>
    ///     Converts byte-array to string
    /// </summary>
    /// <param name="o"></param>
    private static string ConvertObjectArray(byte[] o)
    {
        if (o.Length <= 20) return BitConverter.ToString(o.ToArray()).Replace("-", "").ToLower();

        using MemoryStream memory = new(o);
        var byteList = new List<byte>();

        for (var i = 0; i < 20; i++)
        {
            memory.Position = o.Length / 20 * i;
            byteList.Add(new BinaryReader(memory).ReadByte());
        }
        return BitConverter.ToString(byteList.ToArray()).Replace("-", "").ToLower();
    }
}
