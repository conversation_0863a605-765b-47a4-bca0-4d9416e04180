﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO.Bytecode;
namespace Dolo.Core.AMF3.Fluorine.IO.Readers.AMF3;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF3OptimizedObjectReader : IAMFReader
{
    private readonly Hashtable _optimizedReaders;

    public AMF3OptimizedObjectReader() => _optimizedReaders = new();

    #region IAMFReader Members

    public object ReadData(AMFReader reader)
    {
        var handle = reader.ReadAMF3IntegerData();
        var inline = (handle & 1) != 0;
        handle = handle >> 1;
        if (!inline)
            //An object reference
            return reader.ReadAMF3ObjectReference(handle);
        var classDefinition = reader.ReadClassDefinition(handle);
        object instance = null;
        var reflectionOptimizer = _optimizedReaders[classDefinition.ClassName] as IReflectionOptimizer;
        if (reflectionOptimizer == null)
            lock (_optimizedReaders)
                if (classDefinition.IsTypedObject)
                {
                    if (!_optimizedReaders.Contains(classDefinition.ClassName))
                    {
                        //Temporary reader
                        _optimizedReaders[classDefinition.ClassName] = new AMF3TempObjectReader();
                        var type = ObjectFactory.Locate(classDefinition.ClassName);
                        if (type != null)
                        {
                            instance = ObjectFactory.CreateInstance(type);
                            if (classDefinition.IsExternalizable)
                            {
                                reflectionOptimizer = new AMF3ExternalizableReader();
                                _optimizedReaders[classDefinition.ClassName] = reflectionOptimizer;
                                instance = reflectionOptimizer.ReadData(reader, classDefinition);
                            }
                            else
                            {
                                reader.AddAMF3ObjectReference(instance);
                                //Fixup
                                if (reflectionOptimizer != null)
                                    _optimizedReaders[classDefinition.ClassName] = reflectionOptimizer;
                                else
                                    _optimizedReaders[classDefinition.ClassName] = new AMF3TempObjectReader();
                            }
                        }
                        else
                        {
                            reflectionOptimizer = new AMF3TypedASObjectReader(classDefinition.ClassName);
                            _optimizedReaders[classDefinition.ClassName] = reflectionOptimizer;
                            instance = reflectionOptimizer.ReadData(reader, classDefinition);
                        }
                    }
                    else
                    {
                        reflectionOptimizer = _optimizedReaders[classDefinition.ClassName] as IReflectionOptimizer;
                        instance = reflectionOptimizer.ReadData(reader, classDefinition);
                    }
                }
                else
                {
                    reflectionOptimizer = new AMF3TypedASObjectReader(classDefinition.ClassName);
                    _optimizedReaders[classDefinition.ClassName] = reflectionOptimizer;
                    instance = reflectionOptimizer.ReadData(reader, classDefinition);
                }
        else
            instance = reflectionOptimizer.ReadData(reader, classDefinition);

        return instance;
    }

    #endregion
}

internal class AMF3TempObjectReader : IReflectionOptimizer
{
    #region IReflectionOptimizer Members

    public object CreateInstance() => default;

    public object ReadData(AMFReader reader, ClassDefinition classDefinition)
    {
        var obj = reader.ReadAMF3Object(classDefinition);
        return obj;
    }

    #endregion
}