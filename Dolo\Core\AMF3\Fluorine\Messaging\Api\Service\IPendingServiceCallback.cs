﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Service;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal interface IPendingServiceCallback
{
    /// <summary>
    ///     Triggered when results are recieved.
    /// </summary>
    /// <param name="call"></param>
    void ResultReceived(IPendingServiceCall call);
}