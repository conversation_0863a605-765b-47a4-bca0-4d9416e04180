﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Birthday;

public partial class Birthday
{
    [Command("del")]
    [Description("remove your birthday")]
    public async Task DelAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(ctx.Channel == HubChannel.Birthday);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == ctx.User.Id);
        if (member is null)
            return;

        // if the member has not added a birthday tell him to add one
        if (member.Birthday is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » Please add your birthday in <#{HubChannel.Birthday?.Id}>");
            return;
        }

        // send the message in the chat
        await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » Your birthday has been removed");

        // update the user in the mongodb collection with the filter of the user id 
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, member.Member.DiscordId),
        Builders<ServerMember>.Update.Set(a => a.Birthday, null));
    }
}