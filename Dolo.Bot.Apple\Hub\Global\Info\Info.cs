﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Info;

public class Info
{
    [Command("info")]
    public async Task InfoAsync(TextCommandContext ctx, DiscordUser? user = default)
    {
        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == ctx.User.Id);

        // print the embed
        await ctx.Channel.TrySendMessageAsync(HubEmbed.Info(user ?? ctx.User, member));
    }
}
