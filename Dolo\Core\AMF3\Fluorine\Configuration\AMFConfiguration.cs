﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml.Serialization;
namespace Dolo.Core.AMF3.Fluorine.Configuration;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal enum RemotingServiceAttributeConstraint
{
    [XmlEnum(Name = "browse")] Browse = 1,
    [XmlEnum(Name = "access")] Access = 2
}

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal enum TimezoneCompensation
{
    [XmlEnum(Name = "none")] None = 0,
    [XmlEnum(Name = "auto")] Auto = 1
}

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal sealed class AMFConfiguration
{
    private static readonly object _objLock = new();
    private static AMFConfiguration _instance;

    private static AMFSettings _amfSettings;

    private AMFConfiguration()
    {}

    public static AMFConfiguration Instance
    {
        get
        {
            if (_instance == null)
                lock (_objLock)
                    if (_instance == null)
                    {
                        var instance = new AMFConfiguration();
                        _amfSettings = new();

                        Thread.MemoryBarrier();
                        _instance = instance;
                    }

            return _instance;
        }
    }

    public AMFSettings AMFSettings => _amfSettings;

    internal ServiceCollection ServiceMap => _amfSettings?.Services;

    internal ClassMappingCollection ClassMappings => _amfSettings.ClassMappings;

    public NullableTypeCollection NullableValues => _amfSettings?.Nullables;

    public bool AcceptNullValueTypes
    {
        get
        {
            if (_amfSettings != null)
                return _amfSettings.AcceptNullValueTypes;
            return false;
        }
    }

    public RemotingServiceAttributeConstraint RemotingServiceAttributeConstraint
    {
        get
        {
            if (_amfSettings != null)
                return _amfSettings.RemotingServiceAttribute;
            return RemotingServiceAttributeConstraint.Access;
        }
    }

    public TimezoneCompensation TimezoneCompensation
    {
        get
        {
            if (_amfSettings != null)
                return _amfSettings.TimezoneCompensation;
            return TimezoneCompensation.None;
        }
    }

    public HttpCompressSettings HttpCompressSettings
    {
        get
        {
            if (_amfSettings is { HttpCompressSettings: {} })
                return _amfSettings.HttpCompressSettings;
            return HttpCompressSettings.Default;
        }
    }

    internal OptimizerSettings OptimizerSettings => _amfSettings?.Optimizer;

    internal string GetServiceName(string serviceLocation)
    {
        if (ServiceMap != null)
            return ServiceMap.GetServiceName(serviceLocation);
        return serviceLocation;
    }

    internal string GetServiceLocation(string serviceName)
    {
        if (ServiceMap != null)
            return ServiceMap.GetServiceLocation(serviceName);
        return serviceName;
    }

    internal string GetMethodName(string serviceLocation, string method)
    {
        if (ServiceMap != null)
            return ServiceMap.GetMethodName(serviceLocation, method);
        return method;
    }

    internal string GetMappedTypeName(string customClass)
    {
        if (ClassMappings != null)
            return ClassMappings.GetType(customClass);
        return customClass;
    }

    internal string GetCustomClass(string type)
    {
        if (ClassMappings != null)
            return ClassMappings.GetCustomClass(type);
        return type;
    }
}