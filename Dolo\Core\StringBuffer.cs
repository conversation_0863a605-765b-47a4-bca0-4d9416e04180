﻿namespace Dolo.Core;

/// <summary>
///     Thread safe string builder using semaphore slim
///     can be used in async methods or synchronous methods
/// </summary>
public class StringBuffer
{
    private readonly StringBuilder _builder;
    private readonly SemaphoreSlim _semaphore;
    public StringBuffer()
    {
        _builder = new();
        _semaphore = new(1, 1);
    }

    /// <summary>
    ///     Append a new line
    /// </summary>
    /// <returns></returns>
    public StringBuffer AppendLine(string? value)
    {
        _semaphore.Wait();
        _builder.AppendLine(value);
        _semaphore.Release();
        return this;
    }

    /// <summary>
    ///     Append a string
    /// </summary>
    /// <returns></returns>
    public StringBuffer Append(string? value)
    {
        _semaphore.Wait();
        _builder.Append(value);
        _semaphore.Release();
        return this;
    }

    /// <summary>
    ///     Append a string async
    /// </summary>
    /// <returns></returns>
    public async Task<StringBuffer> AppendAsync(string? value)
    {
        await _semaphore.WaitAsync();
        _builder.Append(value);
        _semaphore.Release();
        return this;
    }

    /// <summary>
    ///     Append a new line async
    /// </summary>
    /// <returns></returns>
    public async Task<StringBuffer> AppendLineAsync(string? value)
    {
        await _semaphore.WaitAsync();
        _builder.AppendLine(value);
        _semaphore.Release();
        return this;
    }

    /// <summary>
    ///     Append a formatted string async
    /// </summary>
    /// <returns></returns>
    public async Task<StringBuffer> AppendFormatAsync(string format, params object?[] args)
    {
        await _semaphore.WaitAsync();
        _builder.AppendFormat(format, args);
        _semaphore.Release();
        return this;
    }

    /// <summary>
    ///     Append a formatted string and a new line async
    /// </summary>
    /// <returns></returns>
    public async Task<StringBuffer> AppendLineFormatAsync(string format, params object?[] args)
    {
        await _semaphore.WaitAsync();
        _builder.AppendFormat(format, args);
        _builder.AppendLine();
        _semaphore.Release();
        return this;
    }

    public StringBuilder ToBuilder()
        => _builder;
    public override string ToString()
        => _builder.ToString();
    public static implicit operator string(StringBuffer buffer)
        => buffer.ToString();
}