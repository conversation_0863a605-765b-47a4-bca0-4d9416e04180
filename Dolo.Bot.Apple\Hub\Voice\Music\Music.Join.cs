﻿using Dolo.Bot.Apple.Hub.Voice.Music.Services;
using Dolo.Database;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

public partial class Music
{
    [Command("join")]
    [Description("join the voice channel")]
    public async Task JoinAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var voiceUser = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (voiceUser is null)
        {
            await ctx.TryEditResponseAsync("This text channel is not linked to a voice channel.");
            return;
        }

        var voiceChannel = ctx.Guild.GetChannel(voiceUser.Channel);
        if (voiceChannel is null)
        {
            await ctx.TryEditResponseAsync("Voice channel not found.");
            return;
        }

        try
        {
            var player = await MusicService.GetOrCreatePlayerAsync(ctx.Guild.Id, voiceUser.Channel, ctx.Channel.Id);
            if (player != null && player.IsConnected)
            {
                await ctx.TryEditResponseAsync($"🎵 Connected to **{voiceChannel.Name}** and ready to play music!");
            }
            else
            {
                await ctx.TryEditResponseAsync("Failed to connect to voice channel.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music] Error joining voice channel: {ex.Message}");
            await ctx.TryEditResponseAsync($"Failed to connect: {ex.Message}");
        }
    }
}
