
using System;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Linq;
using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor;
using Dolo.Core.Interceptor.Extensions;
using Dolo.Core.Extension;
using Dolo.Core;
using Dolo.Core.Cryption;
namespace Dolo.Test;

public partial class Program {
    private static readonly object ConsoleLock = new();

    // Native Windows API for MessageBox
    [DllImport("user32.dll", CharSet = CharSet.Unicode)]
    private static extern int MessageBox(IntPtr hWnd, string text, string caption, uint type);

    // MessageBox button types
    private const uint MB_YESNOCANCEL = 0x00000003;
    private const uint MB_ICONQUESTION = 0x00000020;
    private const uint MB_DEFBUTTON1 = 0x00000000;

    // MessageBox return values
    private const int IDYES = 6;
    private const int IDNO = 7;
    private const int IDCANCEL = 2;

    // Random name generator for Profile Identity API
    private static readonly string[] RandomNames = [
        "VVVV", "XSS", "SDSSD", "WWWWRQ", "FFFSAF", "ASCAS", "AFASFA", "AASf"
    ];

    private static readonly Random Random = new();

    private static string GetRandomName() => RandomNames[Random.Next(RandomNames.Length)];


    [STAThread]
    public static async Task Main() {
        await Program.PrintMspAync();
        return;
        string original = "eweqew";
        string encrypted = CustomEncryptor.DoEncrypt(original)!;
        string decrypted = CustomEncryptor.DoDecrypt(encrypted)!;

        Console.WriteLine($"Original : {original}");
        Console.WriteLine($"Encrypted: {encrypted}");
        Console.WriteLine($"Decrypted: {decrypted}");

        return;
        var interceptorSettings = new InterceptorConfig()
          .ForMovieStarPlanet()
          .UsePort(8889)
          .UseOCSP()
          .EnableAllBreakpoints()
          .AddBreakpointRule(".*[Ll]ogin.*", BreakpointType.Both) // More flexible case-insensitive pattern
          .UseSequentialBreakpoints(); // Sequential processing by default

        // Allow user to choose breakpoint processing mode
        Console.WriteLine("🔧 Breakpoint Processing Modes:");
        Console.WriteLine("   [S] Sequential (Default) - Reliable input handling, no conflicts");
        Console.WriteLine("   [C] Concurrent - Faster, potential input conflicts");
        Console.Write("Choose mode (S/C) or press Enter for default: ");

        var modeInput = Console.ReadLine()?.ToUpperInvariant();
        if (modeInput == "C") {
            interceptorSettings.UseConcurrentBreakpoints();
            Console.WriteLine("✅ Concurrent processing enabled");
        }
        else {
            Console.WriteLine("✅ Sequential processing enabled (default)");
        }
        Console.WriteLine();

        Console.WriteLine("Creating interceptor...");
        using var interceptor = new HttpsInterceptor(interceptorSettings);
        Console.WriteLine("Interceptor created successfully");

        Console.WriteLine("Installing root certificate...");
        if (interceptor.InstallRootCertificate()) {
            Console.WriteLine("✅ Root certificate installed successfully");
        }
        else {
            Console.WriteLine("❌ Failed to install root certificate");
        }

        interceptor.BreakpointHit += OnBreakpointHitAsync;

        Console.WriteLine("🚀 Starting HTTPS Interceptor...");
        await interceptor.StartAsync();
        Console.WriteLine("✅ HTTPS Interceptor Started");
        Console.WriteLine($"🌐 Proxy: 127.0.0.1:{interceptorSettings.ProxyPort}");
        Console.WriteLine("🔍 Waiting for traffic... (Press Ctrl+C to stop)");
        Console.WriteLine();
        Console.CancelKeyPress += async (s, e) => {
            e.Cancel = true;
            Console.WriteLine("\n🛑 Shutting down...");
            await interceptor.StopAsync();
            Environment.Exit(0);
        };

        await Task.Delay(-1);
    }

    private static Task OnBreakpointHitAsync(object? sender, BreakpointHitEventArgs e) {
        // The library handles sequential vs concurrent processing internally
        // We just need to handle the user interaction
        _ = Task.Run(() => ProcessBreakpoint(e));
        return Task.CompletedTask;
    }

    private static void ProcessBreakpoint(BreakpointHitEventArgs e) {
        try {
            var breakpointId = e.BreakpointId[..8];

            // Handle AMF content modification
            if (e.Request?.IsAmf == true || e.Response?.IsAmf == true) {
                HandleAmfBreakpoint(e);
                return;
            }

            // Pre-process Profile Identity API responses for modification
            string? modifiedContent = null;
            if (e.Type == BreakpointType.Response && e.Response?.Content?.Length > 0) {
                var originalContent = System.Text.Encoding.UTF8.GetString(e.Response.Content);
                modifiedContent = ReplaceProfileNames(originalContent);
                e.ModifyContent(modifiedContent);
            }

            // Output raw HTTP format to console for debugging
            lock (ConsoleLock) {
                Console.WriteLine("=== RAW HTTP TRAFFIC ===");

                if (e.Request != null) {
                    // Request line
                    Console.WriteLine($"{e.Request.Method} {e.Request.Uri?.PathAndQuery} HTTP/{e.Request.Version}");

                    // Request headers
                    foreach (var header in e.Request.Headers) {
                        foreach (var value in header.Value) {
                            Console.WriteLine($"{header.Key}: {value}");
                        }
                    }
                    Console.WriteLine();

                    // Request body
                    if (e.Request.Content?.Length > 0) {
                        var requestContent = System.Text.Encoding.UTF8.GetString(e.Request.Content);
                        Console.WriteLine(requestContent);
                    }
                    Console.WriteLine();
                }

                if (e.Response != null) {
                    // Response line
                    Console.WriteLine($"HTTP/{e.Response.Version} {(int)e.Response.StatusCode} {e.Response.ReasonPhrase}");

                    // Response headers
                    foreach (var header in e.Response.Headers) {
                        foreach (var value in header.Value) {
                            Console.WriteLine($"{header.Key}: {value}");
                        }
                    }
                    Console.WriteLine();

                    // Response body (show modified if available)
                    if (e.Response.Content?.Length > 0) {
                        var responseContent = modifiedContent ?? System.Text.Encoding.UTF8.GetString(e.Response.Content);
                        Console.WriteLine(responseContent);
                    }
                    Console.WriteLine();
                }

                Console.WriteLine("========================");
                Console.WriteLine();
            }

            // Build full URL for MessageBox
            var fullUrl = e.Url;
            if (e.Request?.Uri != null) {
                fullUrl = e.Request.Uri.ToString();
            }
            else if (e.Response?.Uri != null) {
                fullUrl = e.Response.Uri.ToString();
            }

            // Build message content for MessageBox
            var message = $"🔴 BREAKPOINT HIT - {e.Type}\n";
            message += $"🆔 ID: {breakpointId}\n";
            message += $"🌐 URL: {fullUrl}\n\n";

            // Show Request Content (AMF or HTTP)
            if (e.Request != null) {
                if (e.Request.IsAmf && e.Request.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Request.Amf.DecodedContent.Content.ToJson();
                    message += $"📤 REQUEST AMF:\n{jsonContent ?? "[Not serializable]"}\n\n";
                }
                else if (e.Request.Content?.Length > 0) {
                    var content = System.Text.Encoding.UTF8.GetString(e.Request.Content);
                    message += $"📤 REQUEST HTTP:\n{content}\n\n";
                }
            }

            // Show Response Content (AMF or HTTP)
            if (e.Response != null) {
                if (e.Response.IsAmf && e.Response.Amf?.DecodedContent?.Content != null) {
                    var jsonContent = e.Response.Amf.DecodedContent.Content.ToJson();
                    message += $"📥 RESPONSE AMF:\n{jsonContent ?? "[Not serializable]"}\n\n";
                }
                else if (e.Response.Content?.Length > 0) {
                    var content = System.Text.Encoding.UTF8.GetString(e.Response.Content);

                    // Show modified content if we have it, otherwise show original
                    if (modifiedContent != null) {
                        message += $"📥 RESPONSE HTTP (MODIFIED):\n{modifiedContent}\n\n";
                        message += $"🔧 ORIGINAL:\n{content}\n\n";
                    }
                    else {
                        message += $"📥 RESPONSE HTTP:\n{content}\n\n";
                    }
                }
            }

            message += "Choose action:\n";
            message += "• YES = Continue\n";
            message += "• NO = Cancel\n";
            message += "• CANCEL = Execute";

            // Show native MessageBox
            var result = MessageBox(IntPtr.Zero, message, "🔴 MSP Profile Identity API",
                MB_YESNOCANCEL | MB_ICONQUESTION | MB_DEFBUTTON1);

            var action = result switch {
                IDYES => BreakpointAction.Continue,
                IDNO => BreakpointAction.Cancel,
                IDCANCEL => BreakpointAction.Execute,
                _ => BreakpointAction.Continue // Default to continue
            };



            e.ResolveBreakpoint(action);
        }
        catch {
            // Auto-continue on error
            e.ResolveBreakpoint(BreakpointAction.Continue);
        }
    }

    private static void HandleAmfBreakpoint(BreakpointHitEventArgs e) {
        var breakpointId = e.BreakpointId[..8];

        Console.WriteLine($"=== AMF BREAKPOINT HIT ({breakpointId}) ===");
        Console.WriteLine($"Type: {e.Type}");
        Console.WriteLine($"URI: {e.Request?.Uri?.ToString() ?? e.Response?.Uri?.ToString() ?? "Unknown"}");

        if (e.Request?.IsAmf == true) {
            Console.WriteLine("AMF Request detected");
            Console.WriteLine($"AMF Content: {e.Request.Amf?.DecodedContent?.Content ?? "null"}");
        }

        if (e.Response?.IsAmf == true) {
            Console.WriteLine("AMF Response detected");
            Console.WriteLine($"AMF Content: {e.Response.Amf?.DecodedContent?.Content ?? "null"}");
        }

        Console.WriteLine("Choose action:");
        Console.WriteLine("1. Continue without modification");
        Console.WriteLine("2. Modify AMF content (example)");
        Console.WriteLine("3. Cancel request/response");

        var choice = Console.ReadLine();
        switch (choice) {
            case "2":
                // Example modification
                if (e.Type == BreakpointType.Response) {
                    e.ModifyAmfContent(new object[] { "TestData1", "TestData2", 12345 });
                    Console.WriteLine("AMF content modified with test data");
                }
                break;
            case "3":
                if (e.Type == BreakpointType.Response) {
                    e.ModifyStatusCode(404, "Not Found");
                }
                Console.WriteLine("Request/Response cancelled");
                break;
            default:
                Console.WriteLine("Continuing without modification");
                break;
        }

        e.ResolveBreakpoint(BreakpointAction.Continue);
        Console.WriteLine("========================");
    }

    private static string ReplaceProfileNames(string content) {
        try {
            var profileNames = System.Text.Json.JsonSerializer.Deserialize<string[]>(content);
            if (profileNames?.Length > 0) {
                var randomNames = new[] { "VVVV", "AASf", "FFFSAF" };
                var random = new Random();
                for (int i = 0; i < profileNames.Length; i++) {
                    profileNames[i] = randomNames[random.Next(randomNames.Length)];
                }
                return System.Text.Json.JsonSerializer.Serialize(profileNames);
            }
        } catch {
            // If parsing fails, return original content
        }
        return content;
    }
}
