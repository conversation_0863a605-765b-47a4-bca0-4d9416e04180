using System.Collections;
using System.Diagnostics;
using System.Text;
using Dolo.Bot.Apple.Hub.Voice.Music.Services;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

public partial class Music
{
    [Command("test")]
    [Description("test audio system components")]
    public async Task TestAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(true);

        var results = new StringBuilder();
        results.AppendLine("🔧 **Audio System Test Results:**\n");

        // Test yt-dlp
        results.AppendLine("**Testing yt-dlp:**");
        var ytDlpPaths = new[] { "yt-dlp", "yt-dlp.exe", ".\\yt-dlp.exe" };
        var ytDlpFound = false;

        foreach (var ytDlpPath in ytDlpPaths)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = ytDlpPath,
                        Arguments = "--version",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    results.AppendLine($"✅ Found at: `{ytDlpPath}` - Version: {output.Trim()}");
                    ytDlpFound = true;
                    break;
                }
            }
            catch (Exception ex)
            {
                results.AppendLine($"❌ `{ytDlpPath}`: {ex.Message}");
            }
        }

        if (!ytDlpFound)
        {
            results.AppendLine("❌ **yt-dlp not found!** Download from: https://github.com/yt-dlp/yt-dlp");
        }

        results.AppendLine("\n**Testing ffmpeg:**");
        var ffmpegFound = false;

        // First, let's check what PATH looks like
        results.AppendLine($"**Environment PATH contains:** {(Environment.GetEnvironmentVariable("PATH")?.Contains("ffmpeg") == true ? "ffmpeg reference found" : "no ffmpeg reference")}");

        // Try where command first
        try
        {
            var whereProcess = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "cmd",
                    Arguments = "/C where ffmpeg",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            whereProcess.Start();
            var whereOutput = await whereProcess.StandardOutput.ReadToEndAsync();
            await whereProcess.WaitForExitAsync();

            if (whereProcess.ExitCode == 0 && !string.IsNullOrEmpty(whereOutput))
            {
                results.AppendLine($"**Where command found:** {whereOutput.Trim()}");
            }
            else
            {
                results.AppendLine("**Where command:** ffmpeg not found in PATH");
            }
        }
        catch (Exception ex)
        {
            results.AppendLine($"**Where command failed:** {ex.Message}");
        }

        // First try using cmd to access PATH with inherited environment
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "cmd",
                    Arguments = "/C ffmpeg -version",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            // Copy all environment variables to ensure PATH is properly inherited
            foreach (DictionaryEntry env in Environment.GetEnvironmentVariables())
            {
                if (env.Key != null && env.Value != null)
                {
                    process.StartInfo.EnvironmentVariables[env.Key.ToString()!] = env.Value.ToString()!;
                }
            }

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync();

            results.AppendLine($"**Debug Info:**");
            results.AppendLine($"Exit Code: {process.ExitCode}");
            results.AppendLine($"Output Length: {output?.Length ?? 0}");
            results.AppendLine($"Error Length: {error?.Length ?? 0}");

            if (!string.IsNullOrEmpty(error))
            {
                results.AppendLine($"Error: {error.Substring(0, Math.Min(200, error.Length))}");
            }

            if (process.ExitCode == 0 && !string.IsNullOrEmpty(output))
            {
                var version = output.Split('\n')[0]; // First line contains version
                results.AppendLine($"✅ Found in PATH via cmd - {version}");
                ffmpegFound = true;
            }
            else
            {
                results.AppendLine($"❌ CMD test failed - Exit code: {process.ExitCode}");
            }
        }
        catch (Exception ex)
        {
            results.AppendLine($"❌ PATH test via cmd: {ex.Message}");
        }

        // If cmd failed, try PowerShell
        if (!ffmpegFound)
        {
            try
            {
                var psProcess = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "powershell",
                        Arguments = "-Command \"ffmpeg -version\"",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                psProcess.Start();
                var psOutput = await psProcess.StandardOutput.ReadToEndAsync();
                await psProcess.WaitForExitAsync();

                if (psProcess.ExitCode == 0 && !string.IsNullOrEmpty(psOutput))
                {
                    var version = psOutput.Split('\n')[0];
                    results.AppendLine($"✅ Found in PATH via PowerShell - {version}");
                    ffmpegFound = true;
                }
                else
                {
                    results.AppendLine($"❌ PowerShell test failed - Exit code: {psProcess.ExitCode}");
                }
            }
            catch (Exception ex)
            {
                results.AppendLine($"❌ PowerShell test failed: {ex.Message}");
            }
        }

        // If not found in PATH, try direct paths including winget locations
        if (!ffmpegFound)
        {
            var ffmpegPaths = new[]
            {
                ".\\ffmpeg.exe",
                @"C:\ffmpeg\bin\ffmpeg.exe",
                // Common winget installation paths
                @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links\ffmpeg.exe",
                @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1-full_build\bin\ffmpeg.exe",
                @"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe",
                // Other common locations
                @"C:\ProgramData\chocolatey\bin\ffmpeg.exe",
                @"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
                @"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe"
            };

            results.AppendLine("**Checking direct paths:**");
            foreach (var ffmpegPath in ffmpegPaths)
            {
                try
                {
                    results.AppendLine($"Checking: `{ffmpegPath}` - {(File.Exists(ffmpegPath) ? "EXISTS" : "NOT FOUND")}");

                    if (File.Exists(ffmpegPath))
                    {
                        var process = new Process
                        {
                            StartInfo = new ProcessStartInfo
                            {
                                FileName = ffmpegPath,
                                Arguments = "-version",
                                RedirectStandardOutput = true,
                                RedirectStandardError = true,
                                UseShellExecute = false,
                                CreateNoWindow = true
                            }
                        };

                        process.Start();
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await process.WaitForExitAsync();

                        if (process.ExitCode == 0)
                        {
                            var version = output.Split('\n')[0];
                            results.AppendLine($"✅ Found at: `{ffmpegPath}` - {version}");
                            ffmpegFound = true;
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"❌ `{ffmpegPath}`: {ex.Message}");
                }
            }

            // If still not found, try to search in WinGet packages directory
            if (!ffmpegFound)
            {
                results.AppendLine("**Searching WinGet packages directory:**");
                try
                {
                    var wingetDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                        "Microsoft", "WinGet", "Packages");

                    if (Directory.Exists(wingetDir))
                    {
                        var ffmpegDirs = Directory.GetDirectories(wingetDir, "*ffmpeg*", SearchOption.TopDirectoryOnly);
                        results.AppendLine($"Found {ffmpegDirs.Length} ffmpeg-related directories");

                        foreach (var dir in ffmpegDirs)
                        {
                            results.AppendLine($"Checking directory: {Path.GetFileName(dir)}");
                            var ffmpegExes = Directory.GetFiles(dir, "ffmpeg.exe", SearchOption.AllDirectories);

                            foreach (var exe in ffmpegExes)
                            {
                                results.AppendLine($"Found ffmpeg.exe at: `{exe}`");

                                try
                                {
                                    var process = new Process
                                    {
                                        StartInfo = new ProcessStartInfo
                                        {
                                            FileName = exe,
                                            Arguments = "-version",
                                            RedirectStandardOutput = true,
                                            RedirectStandardError = true,
                                            UseShellExecute = false,
                                            CreateNoWindow = true
                                        }
                                    };

                                    process.Start();
                                    var output = await process.StandardOutput.ReadToEndAsync();
                                    await process.WaitForExitAsync();

                                    if (process.ExitCode == 0)
                                    {
                                        var version = output.Split('\n')[0];
                                        results.AppendLine($"✅ Working ffmpeg found: `{exe}` - {version}");
                                        ffmpegFound = true;
                                        break;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    results.AppendLine($"❌ Error testing `{exe}`: {ex.Message}");
                                }
                            }

                            if (ffmpegFound) break;
                        }
                    }
                    else
                    {
                        results.AppendLine("WinGet packages directory not found");
                    }
                }
                catch (Exception ex)
                {
                    results.AppendLine($"Error searching WinGet directory: {ex.Message}");
                }
            }
        }

        if (!ffmpegFound)
        {
            results.AppendLine("❌ **ffmpeg not found!**");
            results.AppendLine("Try: `winget install ffmpeg` or download from: https://ffmpeg.org/");
        }

        // Test YouTube URL extraction
        results.AppendLine("\n**Testing YouTube URL extraction:**");
        try
        {
            if (ytDlpFound)
            {
                var testUrl = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"; // Rick Roll for testing
                var ytDlpCmd = ".\\yt-dlp.exe"; // Use the working path format

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = ytDlpCmd,
                        Arguments = $"-f bestaudio --get-url \"{testUrl}\"",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0 && !string.IsNullOrEmpty(output.Trim()))
                {
                    results.AppendLine("✅ YouTube URL extraction working");
                    results.AppendLine($"Sample URL: `{output.Trim().Substring(0, Math.Min(50, output.Trim().Length))}...`");
                }
                else
                {
                    results.AppendLine($"❌ YouTube URL extraction failed: {error}");
                }
            }
            else
            {
                results.AppendLine("⏭️ Skipped (yt-dlp not available)");
            }
        }
        catch (Exception ex)
        {
            results.AppendLine($"❌ Error testing YouTube extraction: {ex.Message}");
        }

        // Summary
        results.AppendLine("\n**Summary:**");
        if (ytDlpFound && ffmpegFound)
        {
            results.AppendLine("🎉 **All components ready!** Music system should work.");
        }
        else
        {
            results.AppendLine("⚠️ **Missing components.** Music system will not work until all tools are installed.");
        }

        await ctx.TryEditResponseAsync(results.ToString());
    }
}
