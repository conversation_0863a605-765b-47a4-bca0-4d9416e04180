<!-- Floating Exchange Preview Window -->
<div class="@GetExchangeClasses()">
    <!-- Header (Clickable to hide) -->
    <div class="flex items-center justify-between p-3 cursor-pointer hover:bg-bg-surface-hover transition-all duration-200" @onclick="ToggleVisibility">
        <div class="flex items-center">
            <div class="w-2 h-2 rounded-full bg-success mr-2"></div>
            <span class="text-xs font-medium text-text-main">Live Exchange</span>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-text-secondary hover:text-text-main transition-colors" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
        </svg>
    </div>

    @if (IsVisible)
    {
        <!-- Content -->
        <div class="p-3 pt-0">
            @if (IsExchangeActive)
            {
                <!-- Active Exchange Content -->
                <!-- Current Item & Round Info -->
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-5 h-5 bg-bg-surface border border-border-l1 rounded flex items-center justify-center mr-2">
                            @if (CurrentItem != null && !string.IsNullOrEmpty(CurrentItem.ImagePath))
                            {
                                <img src="@CurrentItem.ImagePath" alt="@CurrentItem.Name" class="w-3 h-3 object-contain" />
                            }
                            else
                            {
                                <span class="text-xs">👕</span>
                            }
                        </div>
                        <span class="text-xs text-text-main font-medium">@(CurrentItem?.DisplayName ?? "Top Girl")</span>
                    </div>
                    <span class="text-xs text-text-secondary">Round @CurrentRound/@TotalRounds</span>
                </div>
            }
            else
            {
                <!-- No Active Exchange - Test Message -->
                <div class="text-center py-4">
                    <div class="w-8 h-8 bg-bg-base rounded-lg flex items-center justify-center mx-auto mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <p class="text-xs text-text-secondary">No active exchange</p>
                    <p class="text-xs text-text-secondary mt-1">Ready to start transferring items</p>
                </div>
            }

            @if (IsExchangeActive)
            {
                <!-- Account Preview Boxes -->
            <div class="relative mb-3">
                <!-- Account A Box -->
                <div class="bg-bg-surface border border-border-l1 rounded-lg p-2 mb-2">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded bg-primary/20 border border-primary/40 flex items-center justify-center mr-1.5">
                                <span class="text-[8px] font-bold text-primary">A</span>
                            </div>
                            <span class="text-xs text-text-main">Primary</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-1.5 h-1.5 rounded-full @GetAccountStatusColor("A") mr-1"></div>
                            <span class="text-[10px] @GetAccountStatusTextColor("A")">@GetAccountStatus("A")</span>
                        </div>
                    </div>
                    <!-- Item Preview -->
                    <div class="flex items-center justify-center border border-dashed @GetAccountBorderColor("A") rounded h-8 bg-bg-base/50">
                        @if (AccountAHasItem)
                        {
                            @if (CurrentItem != null && !string.IsNullOrEmpty(CurrentItem.ImagePath))
                            {
                                <img src="@CurrentItem.ImagePath" alt="@CurrentItem.Name" class="w-4 h-4 object-contain" />
                            }
                            else
                            {
                                <span class="text-xs">👕</span>
                            }
                        }
                        else
                        {
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                            </svg>
                        }
                    </div>
                </div>

                <!-- Transfer Arrow -->
                <div class="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10" style="top: 50%;">
                    <div class="w-6 h-6 bg-bg-surface border border-border-l1 rounded-full flex items-center justify-center shadow-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-warning" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>

                <!-- Account B Box -->
                <div class="bg-bg-surface border border-border-l1 rounded-lg p-2">
                    <div class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded bg-warning/20 border border-warning/40 flex items-center justify-center mr-1.5">
                                <span class="text-[8px] font-bold text-warning">B</span>
                            </div>
                            <span class="text-xs text-text-main">Secondary</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-1.5 h-1.5 rounded-full @GetAccountStatusColor("B") mr-1"></div>
                            <span class="text-[10px] @GetAccountStatusTextColor("B")">@GetAccountStatus("B")</span>
                        </div>
                    </div>
                    <!-- Empty Preview -->
                    <div class="flex items-center justify-center border border-dashed @GetAccountBorderColor("B") rounded h-8 bg-bg-base/50">
                        @if (AccountBHasItem)
                        {
                            @if (CurrentItem != null && !string.IsNullOrEmpty(CurrentItem.ImagePath))
                            {
                                <img src="@CurrentItem.ImagePath" alt="@CurrentItem.Name" class="w-4 h-4 object-contain" />
                            }
                            else
                            {
                                <span class="text-xs">👕</span>
                            }
                        }
                        else
                        {
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-text-secondary" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                            </svg>
                        }
                    </div>
                </div>
            </div>

            <!-- Transfer Stats -->
            <div class="flex items-center justify-between text-xs mb-2">
                <span class="text-text-secondary">Transfer Progress</span>
                <span class="text-text-secondary">@SentCount sent • @ReceivedCount received</span>
            </div>

            <!-- Progress Bar -->
            <div class="w-full bg-bg-surface border border-border-l1 rounded-full h-1.5">
                <div class="bg-warning h-1.5 rounded-full transition-all duration-500" style="width: @(Progress)%"></div>
            </div>
            }
        </div>
    }
</div>
