﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     The global scope that acts as root for all applications in a host.
/// </summary>
internal interface IGlobalScope : IScope
{
    /// <summary>
    ///     Gets the root service provide object.
    /// </summary>
    IServiceProvider ServiceProvider { get; }
    /// <summary>
    ///     Register the global scope in the server and initialize it.
    /// </summary>
    void Register();
}