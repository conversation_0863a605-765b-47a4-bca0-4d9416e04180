﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Messages;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AcknowledgeMessage : AsyncMessage
{
    /// <summary>
    ///     Initializes a new instance of the AcknowledgeMessage class.
    /// </summary>
    public AcknowledgeMessage()
    {
        _messageId = Guid.NewGuid().ToString("D");
        _timestamp = Environment.TickCount;
    }
}