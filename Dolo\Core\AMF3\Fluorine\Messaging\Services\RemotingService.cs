﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
using Dolo.Core.AMF3.Fluorine.Messaging.Services.Remoting;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Services;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class RemotingService : ServiceBase
{
    public const string RemotingServiceId = "remoting-service";

    public RemotingService(MessageBroker broker, ServiceSettings settings) : base(broker, settings)
    {}

    protected override Destination NewDestination(DestinationSettings destinationSettings)
    {
        var remotingDestination = new RemotingDestination(this, destinationSettings);
        return remotingDestination;
    }

    public override async Task<object> ServiceMessage(IMessage message)
    {
        var remotingMessage = message as RemotingMessage;
        var destination = GetDestination(message) as RemotingDestination;
        var adapter = destination.ServiceAdapter;
        var result = adapter.Invoke(message);
        await result;
        return result.Result;
    }
}