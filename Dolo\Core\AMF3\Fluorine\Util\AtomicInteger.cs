﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Util;

internal class AtomicInteger
{
    private int _counter;

    public AtomicInteger()
        : this(0)
    {}

    public AtomicInteger(int initialValue) => _counter = initialValue;

    public int Value
    {
        get =>
            //Reading int32 is atomic already on both 32 and 64 bit systems
            _counter;
        set => Interlocked.Exchange(ref _counter, value);
    }

    /// <summary>
    ///     Atomically increment by one the current value.
    /// </summary>
    /// <returns></returns>
    public int Increment() => Interlocked.Increment(ref _counter);
    /// <summary>
    ///     Atomically decrement by one the current value.
    /// </summary>
    /// <returns></returns>
    public int Decrement() => Interlocked.Decrement(ref _counter);

    public int PostDecrement() => Interlocked.Decrement(ref _counter) + 1;

    public int PostIncrement() => Interlocked.Increment(ref _counter) - 1;

    /// <summary>
    ///     Atomically add the given value to current value.
    /// </summary>
    /// <param name="delta"></param>
    /// <returns></returns>
    public int Increment(int delta) => Interlocked.Add(ref _counter, delta);

    public int Decrement(int delta) => Interlocked.Add(ref _counter, -delta);

    public int PostDecrement(int delta) => Interlocked.Add(ref _counter, -delta) + delta;

    public int PostIncrement(int delta) => Interlocked.Add(ref _counter, delta) - delta;

    public override string ToString() => Value.ToString();
}