﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF3;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF3EnumWriter : IAMFWriter
{
    #region IAMFWriter Members

    public bool IsPrimitive => true;

    public void WriteData(AMFWriter writer, object data)
    {
        var value = Util.Convert.ToInt32(data);
        writer.WriteAMF3Int(value);
    }

    #endregion
}