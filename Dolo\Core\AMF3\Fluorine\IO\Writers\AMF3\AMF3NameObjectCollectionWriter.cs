﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF3;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF3NameObjectCollectionWriter : IAMFWriter
{

    #region IAMFWriter Members

    public bool IsPrimitive
        => false;

    public void WriteData(AMFWriter writer, object data)
    {
        var collection = data as NameObjectCollectionBase;
        var attributes = collection.GetType().GetCustomAttributes(typeof(DefaultMemberAttribute), false);
        if (attributes is { Length: > 0 })
        {
            var defaultMemberAttribute = attributes[0] as DefaultMemberAttribute;
            var pi = collection.GetType().GetProperty(defaultMemberAttribute.MemberName, [
                typeof(string)
            ]);
            if (pi != null)
            {
                var aso = new ASObject();
                for (var i = 0; i < collection.Keys.Count; i++)
                {
                    var key = collection.Keys[i];
                    var value = pi.GetValue(collection, [
                        key
                    ]);
                    aso.Add(key, value);
                }
                writer.WriteByte(AMF3TypeCode.Object);
                writer.WriteAMF3Object(aso);
                return;
            }
        }

        //We could not access an indexer so write out as it is.
        writer.WriteByte(AMF3TypeCode.Object);
        writer.WriteAMF3Object(data);
    }

    #endregion
}