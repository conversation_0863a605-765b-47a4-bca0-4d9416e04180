
namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("say")]
    [Description("write as the bot")]
    public async Task SayAsync(SlashCommandContext ctx,
        [Description("the message to say")] string message)
    {
        await ctx.Interaction.DeferAsync(true);
        await ctx.Channel.TrySendMessageAsync(message);
        await ctx.TryDeleteResponseAsync();
    }
}