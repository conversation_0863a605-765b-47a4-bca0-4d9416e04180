﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class RawBinary
{

    /// <summary>
    ///     Initializes a new instance of the RawBinary class.
    /// </summary>
    /// <param name="result"></param>
    public RawBinary(byte[] buffer) => Buffer = buffer;

    public byte[] Buffer
    {
        get;
    }
}