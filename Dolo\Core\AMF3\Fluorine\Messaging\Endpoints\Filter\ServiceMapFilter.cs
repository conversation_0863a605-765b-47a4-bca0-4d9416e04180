﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class ServiceMapFilter : AbstractFilter
{
    private EndpointBase _endpoint;

    /// <summary>
    ///     Initializes a new instance of the ServiceMapFilter class.
    /// </summary>
    /// <param name="endpoint"></param>
    public ServiceMapFilter(EndpointBase endpoint) => _endpoint = endpoint;

    #region IFilter Members

    public override Task Invoke(AMFContext context)
    {
        for (var i = 0; i < context.AMFMessage.BodyCount; i++)
        {
            var amfBody = context.AMFMessage.TryGetBodyAt(i);

            if (!amfBody.IsEmptyTarget)
                //Flash
                if (AMFConfiguration.Instance.ServiceMap != null)
                {
                    var typeName = amfBody.TypeName;
                    var method = amfBody.Method;
                    if (typeName != null && AMFConfiguration.Instance.ServiceMap.Contains(typeName))
                    {
                        var serviceLocation = AMFConfiguration.Instance.ServiceMap.GetServiceLocation(typeName);
                        method = AMFConfiguration.Instance.ServiceMap.GetMethod(typeName, method);
                        var target = serviceLocation + "." + method;
                        amfBody.Target = target;
                    }
                }
        }

        return Task.FromResult<object>(null);
    }

    #endregion
}