﻿namespace Dolo.Core;

/// <summary>
///     Represents an asynchronous delegate that can process an event with the specified sender and arguments.
/// </summary>
/// <typeparam name="TSender">The type of the sender.</typeparam>
/// <typeparam name="TEventArgs">The type of the event arguments.</typeparam>
/// <param name="sender">The sender of the event.</param>
/// <param name="e">The event arguments.</param>
/// <returns>A Task that represents the asynchronous operation.</returns>
public delegate Task AsyncEventHandler<in TSender, in TEventArgs>(TSender sender, TEventArgs e);

/// <summary>
///     Represents an asynchronous event with the specified sender and arguments.
/// </summary>
/// <typeparam name="TSender">The type of the sender.</typeparam>
/// <typeparam name="TEventArgs">The type of the event arguments.</typeparam>
public class AsyncEvent<TSender, TEventArgs>
{
    private readonly AsyncEventHandler<TSender, TEventArgs> _handler;

    /// <summary>
    ///     Initializes a new instance of the AsyncEvent class.
    /// </summary>
    /// <param name="handler">The AsyncEventHandler delegate to wrap as an AsyncEvent.</param>
    /// <exception cref="ArgumentNullException">Thrown when the provided handler is null.</exception>
    public AsyncEvent(AsyncEventHandler<TSender, TEventArgs> handler) => _handler = handler ?? throw new ArgumentNullException(nameof(handler));
}

/// <summary>
///     A utility class for invoking async events.
/// </summary>
public static class AsyncEventInvoker
{
    /// <summary>
    ///     Invokes an async event handler with the given sender and arguments.
    /// </summary>
    /// <typeparam name="TSender">The type of the event sender.</typeparam>
    /// <typeparam name="TEventArgs">The type of the event arguments.</typeparam>
    /// <param name="eventHandler">The async event handler to invoke.</param>
    /// <param name="sender">The sender of the event.</param>
    /// <param name="e">The event arguments.</param>
    /// <returns>A Task representing the operation.</returns>
    public static Task InvokeAsync<TSender, TEventArgs>(this AsyncEventHandler<TSender, TEventArgs> eventHandler, TSender sender, TEventArgs e)
        => eventHandler.Invoke(sender, e);
}