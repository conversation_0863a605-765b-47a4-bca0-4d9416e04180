﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Contains the properties for configuring service adapters.
///     This is the <b>destination</b> element in the services-config.xml file.
/// </summary>
internal sealed class DestinationSettings : Hashtable
{
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string AMFDestination = "AMF";

    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string AMFServiceBrowserDestination = "Dolo.Core.AMF3.Fluorine.ServiceBrowser.AMFServiceBrowser";

    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string AMFManagementDestination = "Dolo.Core.AMF3.Fluorine.ServiceBrowser.ManagementService";

    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string AMFCodeGeneratorDestination = "Dolo.Core.AMF3.Fluorine.ServiceBrowser.CodeGeneratorService";

    internal DestinationSettings(ServiceSettings serviceSettings, string id, AdapterSettings adapter, string source)
    {
        ServiceSettings = serviceSettings;
        Properties = new();
        Channels = new();
        Id = id;
        Adapter = adapter;
        Properties["source"] = source;
    }

    internal DestinationSettings(ServiceSettings serviceSettings, XmlNode destinationNode)
    {
        ServiceSettings = serviceSettings;
        Properties = new();
        Channels = new();

        Id = destinationNode.Attributes["id"].Value;

        var adapterNode = destinationNode.SelectSingleNode("adapter");
        if (adapterNode != null)
        {
            var adapterRef = adapterNode.Attributes["ref"].Value;
            var adapterSettings = serviceSettings.AdapterSettings[adapterRef];
            Adapter = adapterSettings;
        }

        PropertiesNode = destinationNode.SelectSingleNode("properties");
        if (PropertiesNode != null)
        {
            var sourceNode = PropertiesNode.SelectSingleNode("source");
            if (sourceNode != null)
                Properties["source"] = sourceNode.InnerXml;
            var factoryNode = PropertiesNode.SelectSingleNode("factory");
            if (factoryNode != null)
                Properties["factory"] = factoryNode.InnerXml;
            var attributeIdNode = PropertiesNode.SelectSingleNode("attribute-id");
            if (attributeIdNode != null)
                //If you specify an attribute-id element in the destination, you can control which attribute the component is stored in
                //This lets more than one destination share the same instance.
                Properties["attribute-id"] = attributeIdNode.InnerXml;
            else
                //Stored using the destination name as the attribute
                Properties["attribute-id"] = Id;

            var scopeNode = PropertiesNode.SelectSingleNode("scope");
            if (scopeNode != null) Properties["scope"] = scopeNode.InnerXml;

            var networkNode = PropertiesNode.SelectSingleNode("network");
            if (networkNode != null)
            {
                var networkSettings = new NetworkSettings(networkNode);
                NetworkSettings = networkSettings;
            }

            var metadataNode = PropertiesNode.SelectSingleNode("metadata");
            if (metadataNode != null)
            {
                var metadataSettings = new MetadataSettings(metadataNode);
                MetadataSettings = metadataSettings;
            }

            var serverNode = PropertiesNode.SelectSingleNode("server");
            if (serverNode != null)
            {
                var serverSettings = new ServerSettings(serverNode);
                ServerSettings = serverSettings;
            }

            var msmqNode = PropertiesNode.SelectSingleNode("msmq");
            if (msmqNode != null)
            {
                var msmqSettings = new MsmqSettings(msmqNode);
                MsmqSettings = msmqSettings;
            }
        }

        var channelsNode = destinationNode.SelectSingleNode("channels");
        if (channelsNode != null)
        {
            var channelNodeList = channelsNode.SelectNodes("channel");
            foreach (XmlNode channelNode in channelNodeList)
            {
                var channelRef = channelNode.Attributes["ref"].Value;
                if (channelRef != null)
                {
                    var channelSettings = ServiceSettings.ServiceConfigSettings.ChannelsSettings[channelRef];
                    Channels.Add(channelSettings);
                }
                else
                {
                    var channelSettings = new ChannelSettings(channelNode);
                    Channels.Add(channelSettings);
                }
            }
        }
    }

    /// <summary>
    ///     Gets the properties XmlNode object. Custom adapters can use this object to query additional settings.
    /// </summary>
    public XmlNode PropertiesNode { get; }

    /// <summary>
    ///     Gets the identity of the destination.
    /// </summary>
    public string Id { get; }

    /// <summary>
    ///     Gets the service settings of the destination.
    /// </summary>
    public ServiceSettings ServiceSettings { get; }

    /// <summary>
    ///     Gets the referenced adapter settings of the destination.
    /// </summary>
    public AdapterSettings Adapter { get; }

    /// <summary>
    ///     Gets destination properties.
    /// </summary>
    public Hashtable Properties { get; }

    /// <summary>
    ///     Gets network settings of the destination.
    /// </summary>
    public NetworkSettings NetworkSettings { get; }

    /// <summary>
    ///     Gets MSMQ settings of the destination if applicable.
    /// </summary>
    public MsmqSettings MsmqSettings { get; }

    /// <summary>
    ///     Gets metadat settings of the destination.
    /// </summary>
    public MetadataSettings MetadataSettings { get; }

    /// <summary>
    ///     Gets server settings of the destination.
    /// </summary>
    public ServerSettings ServerSettings { get; }

    /// <summary>
    ///     Gets channel definitions of the destination.
    /// </summary>
    public ChannelSettingsCollection Channels { get; }
}

/// <summary>
///     Strongly typed DestinationSettings collection.
/// </summary>
internal sealed class DestinationSettingsCollection : CollectionBase
{
    private readonly Hashtable _destinationDictionary;

    /// <summary>
    ///     Initializes a new instance of the DestinationSettingsCollection class.
    /// </summary>
    public DestinationSettingsCollection() => _destinationDictionary = new();

    /// <summary>
    ///     Gets or sets the DestinationSettings element at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the element to get or set.</param>
    /// <returns>The element at the specified index.</returns>
    public DestinationSettings this[int index]
    {
        get => List[index] as DestinationSettings;
        set => List[index] = value;
    }

    /// <summary>
    ///     Gets or sets the DestinationSettings element with the specified key.
    /// </summary>
    /// <param name="key">The id of the DestinationSettings element to get or set.</param>
    /// <returns>The element with the specified key.</returns>
    public DestinationSettings this[string key]
    {
        get => _destinationDictionary[key] as DestinationSettings;
        set => _destinationDictionary[key] = value;
    }

    /// <summary>
    ///     Adds a DestinationSettings to the collection.
    /// </summary>
    /// <param name="value">The DestinationSettings to add to the collection.</param>
    /// <returns>The position into which the new element was inserted.</returns>
    public int Add(DestinationSettings value)
    {
        _destinationDictionary[value.Id] = value;
        return List.Add(value);
    }

    /// <summary>
    ///     Determines the index of a specific item in the collection.
    /// </summary>
    /// <param name="value">The DestinationSettings to locate in the collection.</param>
    /// <returns>The index of value if found in the collection; otherwise, -1.</returns>
    public int IndexOf(DestinationSettings value) => List.IndexOf(value);

    /// <summary>
    ///     Inserts a DestinationSettings item to the collection at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index at which value should be inserted.</param>
    /// <param name="value">The DestinationSettings to insert into the collection.</param>
    public void Insert(int index, DestinationSettings value)
    {
        _destinationDictionary[value.Id] = value;
        List.Insert(index, value);
    }

    /// <summary>
    ///     Removes the first occurrence of a specific DestinationSettings from the collection.
    /// </summary>
    /// <param name="value">The DestinationSettings to remove from the collection.</param>
    public void Remove(DestinationSettings value)
    {
        _destinationDictionary.Remove(value.Id);
        List.Remove(value);
    }

    /// <summary>
    ///     Determines whether the collection contains a specific DestinationSettings value.
    /// </summary>
    /// <param name="value">The DestinationSettings to locate in the collection.</param>
    /// <returns>true if the DestinationSettings is found in the collection; otherwise, false.</returns>
    public bool Contains(DestinationSettings value) => List.Contains(value);

    /// <summary>
    ///     Determines whether the collection contains a destination with a specific identity.
    /// </summary>
    /// <param name="key">The destination identity.</param>
    /// <returns>true if the DestinationSettings is found in the collection; otherwise, false.</returns>
    public bool ContainsKey(string key) => _destinationDictionary.ContainsKey(key);
}