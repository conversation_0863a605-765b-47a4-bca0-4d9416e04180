﻿namespace Dolo.Bot.Apple.Hub.Global.Roulette;

[Command("roulette")]
[Description("commands for the roulette")]
public partial class Roulette
{
    [Command("winner")]
    [Description("roulette the winer of")]
    public async Task RouletteAsync(SlashCommandContext ctx, [Description("choose a language for the rolette")] RouletteType type, [Description("roulette message")] string message)
    {
        await ctx.Interaction.DeferAsync();

        // default language format

        // switch each language format
        var language = type switch
        {
            RouletteType.German  => $"{HubEmoji.Astro} der gewinner von **{{0}}** ist **{{1}}**",
            RouletteType.Turkish => $"{HubEmoji.Astro} Kazananı **{{0}}** dır **{{1}}**",
            RouletteType.French  => $"{HubEmoji.Astro} le vainqueur de **{{0}}** est **{{1}}**",
            _                    => $"{HubEmoji.As<PERSON>} the winner of **{{0}}** is **{{1}}**"
        };

        // get first member of a shuffled list
        var member = Hub.Guild?.Members.ToList().Shuffle().FirstOrDefault();
        if (member is null)
            return;

        // send response to server
        await ctx.TryEditResponseAsync(string.Format(language, message, $"{member.Value.Value.Username}"));
    }
}