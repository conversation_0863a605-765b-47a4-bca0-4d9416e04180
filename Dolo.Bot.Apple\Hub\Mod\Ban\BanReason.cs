﻿using DSharpPlus.Commands.Processors.SlashCommands.ArgumentModifiers;
namespace Dolo.Bot.Apple.Hub.Mod.Ban;

public static class BanExtensions
{
    public static string GetBanReason(this BanReason reason) => reason switch
    {
        BanReason.Spam        => "Spamming in chat",
        BanReason.Advertising => "Advertising in chat or in the dm",
        BanReason.Harassment  => "Harassing other users",
        BanReason.Nsfw        => "Posting NSFW content",
        BanReason.Inviting    => "Sending invite links to other servers",
        BanReason.Software    => "Using Third-party software",
        BanReason.Alt         => "Using a alt account",
        BanReason.Insult      => "Insulting other users",
        BanReason.Bot         => "Stolen account sending fake links",
        BanReason.Scam        => "Scamming other users",
        BanReason.Trashtalk   => "Talking shit on the server",
        BanReason.Permanently => "do not unban",
        _                     => "Unknown reason"
    };
}

public enum BanReason
{
    [ChoiceDisplayName("Spamming in chat")]
    Spam,
    [ChoiceDisplayName("Advertising in chat or in the dm")]
    Advertising,
    [ChoiceDisplayName("harassing other users")]
    Harassment,
    [ChoiceDisplayName("Posting NSFW content")]
    Nsfw,
    [ChoiceDisplayName("Sending invite links to other servers")]
    Inviting,
    [ChoiceDisplayName("Using Third-party software")]
    Software,
    [ChoiceDisplayName("Using a alt account")]
    Alt,
    [ChoiceDisplayName("Insulting other users")]
    Insult,
    [ChoiceDisplayName("Bot account sending fake links")]
    Bot,
    [ChoiceDisplayName("Scamming other users")]
    Scam,
    [ChoiceDisplayName("User is shit talking")]
    Trashtalk,
    [ChoiceDisplayName("Ban permanently. Do not unban")]
    Permanently
}