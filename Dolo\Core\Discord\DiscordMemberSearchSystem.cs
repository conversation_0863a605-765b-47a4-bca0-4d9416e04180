﻿namespace Dolo.Core.Discord;

public class DiscordMemberSearchSystem
{


    /// <summary>
    ///     Initializes a new instance of the <see cref="DiscordMemberSearchSystem" /> class.
    /// </summary>
    public DiscordMemberSearchSystem(Action<DiscordMemberSearchSystemConfig>? action) => Config = action?.GetAction() ?? Config;
    /// <summary>
    ///     Retrieves all members in the cache.
    /// </summary>
    public List<DiscordMemberSource> Members { get; } = [];

    /// <summary>
    ///     The authentication token for accessing the Discord API.
    /// </summary>
    private DiscordMemberSearchSystemConfig Config { get; } = new();

    /// <summary>
    ///     Retrieves a member by their unique identifier.
    /// </summary>
    public DiscordMemberSource? GetMember(ulong? id) =>
        Members.FirstOrDefault(a => a.Member?.Id == id);

    /// <summary>
    ///     Retrieves members based on an invite code.
    /// </summary>
    public List<DiscordMemberSource> GetMembers(string inviteCode) =>
        Members.Where(a => a.InviteCode == inviteCode).ToList();

    /// <summary>
    ///     Retrieves members invited by a specific user.
    /// </summary>
    public List<DiscordMemberSource> GetMembers(ulong userId) =>
        Members.Where(a => a.InvitedBy == userId).ToList();


    /// <summary>
    ///     Removes a member from the cache.
    /// </summary>
    public void RemoveMember(ulong id) =>
        Members.RemoveAll(a => a.Member?.Id == id);

    /// <summary>
    ///     Gets the count of members invited by a specific user.
    /// </summary>
    public int GetInviteCount(ulong userId) =>
        Members.Count(a => a.InvitedBy == userId && a.JoinType == JoinType.Invite);

    /// <summary>
    ///     Gets the count of members invited by a specific user within the last 5 minutes.
    /// </summary>
    public int GetInviteCountFiltered(ulong userId) =>
        Members.Count(a => a.InvitedBy == userId &&
                           a.JoinType == JoinType.Invite &&
                           a.CreatedAt <= DateTime.UtcNow.AddYears(-1));
    /// <summary>
    ///     Sets the Discord client.
    /// </summary>
    /// <summary>
    ///     Updates the cache with new member information.
    /// </summary>
    private void UpdateCache(DiscordMemberSearch memberSearch)
    {
        foreach (var member in memberSearch.Members
                     .Where(member => Members.All(a => a.Member?.Id != member.Member?.Id)))
        {
            member.CreatedAt = Config.Guild!.TryGetMember(member.Member!.Id, out var memberDiscord) ? memberDiscord.CreationTimestamp : default;
            Members.Add(member);
        }
    }

    /// <summary>
    ///     Starts the asynchronous member search process.
    /// </summary>
    public async Task StartAsync()
    {
        while (true)
        {
            var allMembers = await Config.Guild!.GetAllMemberSearchAsync(Config.Token);
            if (allMembers.Members.Count == 0)
            {
                await Task.Delay(TimeSpan.FromMinutes(1));
                continue;
            }

            UpdateCache(allMembers);

            var members = await Config.Guild!.GetMemberSearchAsync(Config.Token, 100);
            if (members.Members.Count == 0)
            {
                await Task.Delay(TimeSpan.FromMinutes(1));
                continue;
            }

            UpdateCache(members);

            await Task.Delay(TimeSpan.FromMinutes(1));
        }
    }

    /// <summary>
    ///     Get the join message for the user
    /// </summary>
    public async Task<string?> GetMessageAsync(DiscordMember e)
    {
    repeat:

        await Task.Delay(TimeSpan.FromSeconds(7));

        var member = GetMember(e.Id);
        var inviter = GetMember(member?.InvitedBy.GetValueOrDefault());

        if (member is null)
        {
            if (!e.Guild.TryGetMember(e.Id, out _))
                return default;

            goto repeat;
        }

        var inviterUser = member.InvitedBy.HasValue ? await Config.Discord!.TryGetUserAsync(member.InvitedBy.GetValueOrDefault()) : null;
        var code = member.InviteCode;
        var joinType = member.JoinType;

        var inviterUsername = inviter?.Member?.Username ?? inviterUser?.Username ?? "Unknown";
        var inviterId = inviter?.Member?.Id ?? member.InvitedBy;
        var inviteCount = GetMembers(inviterId.GetValueOrDefault()).Count;

        return joinType switch
        {
            JoinType.Bot => $"Invited by a bot `{inviterUsername}`",
            JoinType.Discovery => "Joined through discovery",
            JoinType.Integration => "Joined through integration",
            JoinType.Unknown => "Joined from an unknown source",
            JoinType.Vanity => $"Joined through `discord.gg/{code}`",
            JoinType.Invite => inviter is null ?
                                   $"Invited by {inviterUsername} • but left server • {code}" :
                                   $"Invited by {inviterUsername} • {inviteCount:N0} {"invite".Pluralize(inviteCount)} • {code}",
            JoinType.Hub => "Unknown source",
            _ => "Unknown source"
        };
    }
}