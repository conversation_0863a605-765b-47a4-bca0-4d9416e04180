﻿using System.Text;
namespace Dolo.Bot.Apple.Hub;

public static class HubEmbed
{
    private const string EmbedColor = "D6EDFB";
    public static DiscordMessageBuilder Verify => new DiscordMessageBuilder()
        .AddActionRowComponent(new DiscordLinkButtonComponent("https://msp.cbkdz.eu/", "Pluto", false, new(HubEmoji.Pluto)))
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Title = "Verification",
            Description = new StringBuilder()
                .Append("**`1.` » Open Pluto.exe**\n")
                .Append("**`2.` » Login with Discord**\n")
                .Append("**`3.` » You will be mentioned here on success.**\n")
                .ToString(),
            Thumbnail = new()
            {
                Url = "https://cdn.discordapp.com/attachments/770744248952684575/961266987126886490/Screenshot_2022-04-06_at_16-09-48_Tailwind_Play_1.png"
            }
        });
    public static DiscordMessageBuilder Birthday => new DiscordMessageBuilder()
        .AddActionRowComponent(new DiscordButtonComponent(DiscordButtonStyle.Secondary, Guid.NewGuid().ToString(), "", true, new(HubEmoji.WhiteHeart)))
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Title = "Birthday",
            Description = new StringBuilder()
                .Append($"**{HubEmoji.Cake?.ToString()} »** `/bday add` **» add a birthday**\n")
                .Append($"**{HubEmoji.Cake?.ToString()} »** `/bday del` **» remove a birthday**\n")
                .Append($"**{HubEmoji.Cake?.ToString()} »** `/bday me` **» show your birthday**\n")
                .Append($"**{HubEmoji.Cake?.ToString()} »** `/bday user` **» show someones birthday**\n")
                .Append($"**{HubEmoji.Cake?.ToString()} »** `/bday list` **» list all birthdays**\n").ToString(),
            Thumbnail = new()
            {
                Url = "https://cdn.discordapp.com/attachments/944722654907219988/963137175682162708/heart-with-ribbon_1f49d.png"
            }
        });

    public static DiscordMessageBuilder Welcome => new DiscordMessageBuilder()
        .AddActionRowComponent(new DiscordActionRowComponent(new List<DiscordComponent>
        {
            new DiscordLinkButtonComponent("https://msp.cbkdz.eu/", "Pluto App", false, new(HubEmoji.Pluto)),
            new DiscordButtonComponent(DiscordButtonStyle.Primary, "question-pluto-verify", "Verification", false, new(HubEmoji.Key))
        }))
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Title = $"Welcome to Pluto  {HubEmoji.MspLoading}",
            Description = new StringBuilder()
                .Append("A [MovieStarPlanet](https://moviestarplanet.com) Community and Chill' Server.\n")
                .Append("We are Reverse-Engineer and Developer of Pluto\n\n")
                .Append($"**Pluto {HubEmoji.Pluto?.ToString()}**\n")
                .Append("Is an extension for moviestarplanet.\npluto makes playing msp easier.\n\n")
                .Append($"**» **<#{HubChannel.Changelog?.Id}>\n")
                .Append($"**» **<#{HubChannel.Rules?.Id}>\n")
                .Append($"**» **<#{HubChannel.News?.Id}>\n\n").ToString(),
            ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1400076042944778270/image.png?ex=688b51ec&is=688a006c&hm=fc625789298ed7591e79b132734af2f93fe85150749614df6c3d722ee78c6839&"
        });

    public static DiscordMessageBuilder Help => new DiscordMessageBuilder()
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Title = $"Usable commands {HubEmoji.WhiteHeart}",
            Description = new StringBuilder()
                .Append("Interact with other users. to do this, use one of the commands\n")
                .Append("all these commands can be used by everyone\n\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/apple level` » to show a level\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/apple vote` » to vote for something\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/apple board` » to show the top 10 users\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/apple invites` » to see your invites\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/bday me` » to show your birthday\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/bday list` » to show a list of all birthdays\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/bday add` » to add a birthday\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/bday del` » to remove a birthday\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/bday user` » to see someones birthday\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/roulette winner` » to play winner roulette\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/roulette love` » to play winner roulette\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/roulette 8ball` » to play 8ball roulette\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `/welcome` » welcome a new member\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `av` » to show a avatar or from someone else\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `av nitro` » to show the guild avatar\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `av banner` » to show a banner or from someone else\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `av server` » to show the server banner and icon\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `info` » to show information from you or a user\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `meow` » to show a cat\n")
                .Append($"{HubEmoji.GhostLove?.ToString()} » `meow (number)` » to show http cat\n")
                .ToString()
        });

    public static DiscordMessageBuilder Rewards => new DiscordMessageBuilder()
        .AddActionRowComponent([
            new
                DiscordButtonComponent(DiscordButtonStyle.Primary, "reward-my-invites", "My Invites", false, new(HubEmoji.MspHeart!)),
            new DiscordButtonComponent(DiscordButtonStyle.Success, "reward-create-invite", "Create Invite", false, new(HubEmoji.MspHeart!))
        ]).AddEmbed(new DiscordEmbedBuilder
        {
            ImageUrl = "https://cdn.discordapp.com/attachments/944722654907219988/1281523731654512641/file-brdGCWxGHbzQP2HQFseA4SSb.png?ex=66dc077b&is=66dab5fb&hm=d1663f1bb366c4957131ff985a029be47c60352a4cd11cbed24a77dda635c941&",
            Color = new DiscordColor("#2B2D31"),
            Footer = new()
            {
                Text = "invite people to get rewards.",
                IconUrl = "https://cdn.discordapp.com/emojis/1205846416677929060.gif?size=44&quality=lossless"
            },
            Description = $"""
                 ## Rewards {HubEmoji.Tada}
                - *A new reward system has been added to the server.*
                - *Invite people to get many rewards.*
                ### Rewards
                - {HubEmoji.GhostLove}  **350 Invites**  - *1 Year StarVip*
                - {HubEmoji.GhostLove}  **250 Invites.**  - *20€ Paysafecard*
                - {HubEmoji.GhostLove}  **150 Invites**  - *10€ Spotify Card*
                - {HubEmoji.GhostLove}  **100 Invites**  - *10€ Amazon Card*
                - {HubEmoji.GhostLove}  **50 Invites**  - *1 Month StarVip*
                - {HubEmoji.GhostLove}  **25 Invites**  - *1 Week StarVip*
                """
        });

    public static DiscordMessageBuilder NewUser(DiscordMember member, string? message) => new DiscordMessageBuilder()
        .AddActionRowComponent(new DiscordLinkButtonComponent("https://msp.cbkdz.eu/", "Pluto", false, new(HubEmoji.Pluto)))
        .WithContent(member.Mention)
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Title = $"Welcome! {member.Username} {HubEmoji.WhiteFlyingHeart}",
            Description = new StringBuilder()
                .AppendLine()
                .AppendLine($"`{Hub.Guild?.MemberCount:N0} Member`")
                .AppendLine($"**»** <#{HubChannel.Welcome?.Id}>")
                .AppendLine($"**»** <#{HubChannel.Rules?.Id}>")
                .AppendLine($"**»** <#{HubChannel.Roles?.Id}>")
                .ToString(),
            Thumbnail = new()
            {
                Url = member.AvatarUrl
            },
            Footer = new()
            {
                Text = message,
                IconUrl = member.GuildAvatarUrl
            }
        });


    public static DiscordMessageBuilder InteractionQuestionPluto() => new DiscordMessageBuilder()
        .AddEmbed(new DiscordEmbedBuilder
        {
            Thumbnail = new()
            {
                Url = "https://cdn.discordapp.com/attachments/944722654907219988/996455499069411468/1.png"
            },
            Color = new DiscordColor(EmbedColor),
            Title = "FAQ",
            Description = new StringBuilder()
                .AppendLine($"{HubEmoji.PinkDot} **What is pluto?**")
                .AppendLine($"{HubEmoji.ApplePlace} *pluto is a extension for the game moviestarplanet.*")
                .AppendLine($"{HubEmoji.ApplePlace} *with pluto you can simplify the usage of the game.*")
                .AppendLine()
                .AppendLine($"{HubEmoji.PinkDot} **Is Pluto free to use?**")
                .AppendLine($"{HubEmoji.ApplePlace} *currently pluto is free to use.*")
                .AppendLine($"{HubEmoji.ApplePlace} *we are adding premium features in future.*")
                .AppendLine()
                .AppendLine($"{HubEmoji.PinkDot} **Can I be banned for using it?**")
                .AppendLine($"{HubEmoji.ApplePlace} *Maybe.. we do not know that.*")
                .AppendLine($"{HubEmoji.ApplePlace} *We are not the developer team of the game.*")
                .AppendLine($"{HubEmoji.ApplePlace} *Pluto is not meant to harm the game, it is only meant to make it easier to use.*")
                .AppendLine()
                .ToString()
        });

    public static DiscordMessageBuilder InteractionQuestionPlutoVerify() => new DiscordMessageBuilder()
        .AddEmbed(new DiscordEmbedBuilder
        {
            Thumbnail = new()
            {
                Url = "https://cdn.discordapp.com/attachments/944722654907219988/1061765508438966302/1.png"
            },
            Color = new DiscordColor(EmbedColor),
            Description = new StringBuilder()
                .AppendLine("**Discord Verification**")
                .AppendLine("*To use Pluto you need to verify with Discord.*")
                .AppendLine()
                .AppendLine($"{HubEmoji.BluetDot} **Go to [our website](https://msp.cbkdz.eu)**")
                .AppendLine($"{HubEmoji.BluetDot} **Download the toolbox**")
                .AppendLine($"{HubEmoji.BluetDot} **Authenticate for the downloaded tool**")
                .AppendLine()
                .AppendLine($"{HubEmoji.Apple} **Need help?** Write in <#794382151008518154>")
                .ToString()
        });



    public static DiscordMessageBuilder Voice(DiscordUser user) => new DiscordMessageBuilder()
        .WithContent(user.Mention)
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Title = $"Voice Channel Commands {HubEmoji.Astro}",
            ImageUrl = "https://cdn.cbkdz.eu/img/iMusic.jpeg",
            Description = new StringBuilder()
                .Append("these commands can only be executed by the\n")
                .Append("moderators or channel owner of this voice channel\n\n")
                .Append("**Basic Commands**\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice help**](http://a) » get the command list\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice info**](http://a) » detailed channel information\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice status**](http://a) » your permission status\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice stats**](http://a) » channel usage statistics\n")
                .Append("\n**Channel Management**\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice name**](http://a) » set the voice channel name\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice lock**](http://a) » lock the voice channel\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice unlock**](http://a) » unlock the voice channel\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice limit**](http://a) » limit the voice channel\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice unlimit**](http://a) » remove voice channel limit\n")
                .Append("\n**User Management**\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice ban**](http://a) » ban a user from the voice channel\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice unban**](http://a) » unban a user from the voice channel\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice kick**](http://a) » kick a user from the voice channel\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice mute**](http://a) » mute a user in the text channel\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice unmute**](http://a) » unmute a user in the text channel\n")
                .Append("\n**Moderation**\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice mod**](http://a) » give a user moderator rights\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice unmod**](http://a) » remove moderator rights from a user\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice transfer**](http://a) » transfer channel ownership\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice claim**](http://a) » claim ownerless channel (moderators)\n")
                .Append($"{HubEmoji.Apple?.ToString()} **»** [**/voice get**](http://a) » view moderators, banned, muted users\n")
                .ToString()
        });

    public static DiscordMessageBuilder Info(DiscordUser user, ServerMember? member) => new DiscordMessageBuilder()
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Description = new StringBuilder()
                .Append($"**• User {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"{user.Username}{user.Discriminator} (`{user.Id}`)\n\n")
                .Append($"**• Account Created {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"{user.CreationTimestamp} (`{user.CreationTimestamp.GetTimeString()}`)\n\n")
                .Append(Hub.Guild?.Members.Any(a => a.Key == user.Id) ?? false ?
                            $"**• Server Joined {HubEmoji.WhiteFlyingHeart}**\n" +
                            $"{Hub.Guild.Members[user.Id].JoinedAt} (`{Hub.Guild.Members[user.Id].JoinedAt.GetTimeString()}`)\n\n" : "")
                .Append(member is { State.IsBanned: true } ? $"**• Banned {HubEmoji.WhiteFlyingHeart}**\n" : "")
                .Append(member != null && !string.IsNullOrEmpty(member.State.BanReason) && member.State.IsBanned ? $"```{member.State.BanReason}```" : "")
                .ToString(),
            Thumbnail = new()
            {
                Url = user.AvatarUrl
            }
        });

    public static DiscordMessageBuilder DevLog(DiscordUser user, int action, ServerMember member, MessageArgs msg) => new DiscordMessageBuilder()
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Description = new StringBuilder()
                .Append($"**• User {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"{user.Username}{user.Discriminator} (`{user.Id}`)\n\n")
                .Append($"**• Channel {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"<#{msg.Channel.Id}> (`{msg.Channel.Id}`)\n\n")
                .Append($"**• Action {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"{action switch
                {
                    0 => "Has been banned for sending a invite",
                    1 => "Has been banned for sending a blacklisted content",
                    2 => "Has been warned for sending a blacklisted message",
                    3 => "Has been muted for sending a blacklisted message",
                    _ => "Has been warned for sending a blacklisted message"
                }}\n```{msg.Message.Content}```\n")
                .ToString(),
            Thumbnail = new()
            {
                Url = user.AvatarUrl
            }
        });

    public static DiscordMessageBuilder Pluto(DiscordMember? member, ServerMember user) => new DiscordMessageBuilder()
        .AddEmbed(new DiscordEmbedBuilder
        {
            Color = new DiscordColor(EmbedColor),
            Description = new StringBuilder()
                .Append($"**• User {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"{member?.Username}{member?.Discriminator} (`{member?.Id}`)\n\n")
                .Append($"**• Invites {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"Uses: {user.Invites.Uses}\n")
                .Append($"Members: {user.Invites.Members.Count}\n\n")
                .Append($"**• Account {HubEmoji.WhiteFlyingHeart?.ToString()}**\n")
                .Append($"IsBoost: {(user.State.IsBooster ? HubEmoji.Yes?.ToString() : HubEmoji.No?.ToString())}\n")
                .Append($"IsOG: {(user.State.IsOG ? HubEmoji.Yes?.ToString() : HubEmoji.No?.ToString())}\n")
                .Append($"IsLeft: {(user.State.HasLeft ? HubEmoji.Yes?.ToString() : HubEmoji.No?.ToString())}\n")
                .Append($"IsPlutoPlus: {(user.License!.HasPermission(LicensePermission.Plus) ? HubEmoji.Yes?.ToString() : HubEmoji.No?.ToString())}\n")
                .Append($"IsBanned: {(user.State.IsBanned ? HubEmoji.Yes?.ToString() : HubEmoji.No?.ToString())}\n")
                .Append($"IsBlocked: {(user.License?.IsBlocked     ?? false ? HubEmoji.Yes?.ToString() : HubEmoji.No?.ToString())}\n")
                .Append($"Features: {(user.License?.Features.Any() ?? false ? string.Join(",", user.License.Features) : HubEmoji.No?.ToString())}\n")
                .Append($"{(user.State.IsBanned ? $"```{user.State.BanReason}```" : "")}")
                .Append("")
                .ToString(),
            Thumbnail = new()
            {
                Url = member?.AvatarUrl
            }
        });
}
