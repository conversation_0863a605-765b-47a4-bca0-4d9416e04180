﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class ObjectFactory
{
    private static readonly Dictionary<string, Type> _typeCache = new();
    private static readonly string[] _lacLocations;

    static ObjectFactory() => _lacLocations = TypeHelper.GetLacLocations();

    public static Type Locate(string typeName)
    {
        if (typeName == null || typeName == string.Empty)
            return null;

        var mappedTypeName = typeName;
        mappedTypeName = AMFConfiguration.Instance.GetMappedTypeName(typeName);

        //Lookup first in our cache.
        lock (typeof(Type))
        {
            Type type = null;
            if (_typeCache.ContainsKey(mappedTypeName))
                type = _typeCache[mappedTypeName];
            if (type == null)
            {
                type = TypeHelper.Locate(mappedTypeName);
                if (type != null)
                {
                    _typeCache[mappedTypeName] = type;
                    return type;
                }

                //Locate in LAC
                type = LocateInLac(mappedTypeName);
            }

            return type;
        }
    }

    public static Type LocateInLac(string typeName)
    {
        //Locate in LAC
        if (typeName == null || typeName == string.Empty)
            return null;

        var mappedTypeName = typeName;
        mappedTypeName = AMFConfiguration.Instance.GetMappedTypeName(typeName);

        //Lookup first in our cache.
        lock (typeof(Type))
        {
            Type type = null;
            if (_typeCache.ContainsKey(mappedTypeName))
                type = _typeCache[mappedTypeName];
            if (type == null)
                //Locate in LAC
                for (var i = 0; i < _lacLocations.Length; i++)
                {
                    type = TypeHelper.LocateInLac(mappedTypeName, _lacLocations[i]);
                    if (type != null)
                    {
                        _typeCache[mappedTypeName] = type;
                        return type;
                    }
                }

            return type;
        }
    }

    internal static void AddTypeToCache(Type type)
    {
        if (type != null)
            lock (typeof(Type))
                _typeCache[type.FullName] = type;
    }

    public static bool ContainsType(string typeName)
    {
        if (typeName != null)
            lock (typeof(Type))
                return _typeCache.ContainsKey(typeName);

        return false;
    }

    public static object CreateInstance(Type type) => CreateInstance(type, null);

    public static object CreateInstance(Type type, object[] args)
    {
        if (type != null)
            lock (typeof(Type))
                if (type.IsAbstract && type.IsSealed)
                    return type;
                else
                {
                    if (args == null)
                        return Activator.CreateInstance(type,
                        BindingFlags.CreateInstance | BindingFlags.Public | BindingFlags.Instance |
                        BindingFlags.Static, null, [], null);
                    return Activator.CreateInstance(type,
                    BindingFlags.CreateInstance | BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static,
                    null, args, null);
                }

        return null;
    }

    public static object CreateInstance(string typeName) => CreateInstance(typeName, null);

    public static object CreateInstance(string typeName, object[] args)
    {
        var type = Locate(typeName);
        return CreateInstance(type, args);
    }
}