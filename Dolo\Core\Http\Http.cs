﻿using System.Net.Http.Json;
using <PERSON>;
using Polly.Contrib.WaitAndRetry;
namespace Dolo.Core.Http;

public static partial class Http {
    private static readonly IAsyncPolicy<HttpResponseMessage> RetryPolicy = Policy<HttpResponseMessage>
        .Handle<HttpRequestException>()
        .OrResult(r => r.StatusCode is >= HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
        .WaitAndRetryAsync(Backoff.DecorrelatedJitterBackoffV2(TimeSpan.FromMilliseconds(500), 1, null, true));


    /// <summary>
    ///     Http method to fetch the content length of a remote resource.
    /// </summary>
    private static async Task<long> GetContentLengthAsync(string url) {
        var response = await CreateHttpClient()
                           .SendAsync(new(HttpMethod.Head, url));
        return response.Content.Headers.ContentLength ?? 0;
    }

    /// <summary>
    ///     Http method that checks when a image is available
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    public static async Task<bool> IsImageAvailableAsync(string? url) {
        var response = await TrySendAsync(a => {
            a.Method = HttpMethod.Head;
            a.Url = url;
        });
        return response.IsSuccess;
    }

    /// <summary>
    ///     Http method that sends a http request
    ///     you can specify the method, url, headers and body in the config
    ///     returns a object
    /// </summary>
    /// <param name="config"></param>
    /// <returns></returns>
    public static async Task<HttpResponse<object>> TrySendAsync(Action<HttpConfig> config) => await TrySendAsync<object>(config);


    /// <summary>
    ///     Http method that trys to get a string async
    /// </summary>
    /// <returns></returns>
    public static async Task<string?> TryGetStringAsync(string url) => await CreateHttpClient().GetStringAsync(url).TryAsync();

    /// <summary>
    ///     Http method that trys to get a stream
    /// </summary>
    /// <returns></returns>
    public static async Task<HttpResult<Stream?>> TryGetStreamAsync(string url) {
        var http = new HttpResult<Stream?>();
        var val = await CreateHttpClient()
                      .GetAsync(url)
                      .TryAsync(a => http.Exception = a as HttpRequestException);

        http.IsSuccess = val?.IsSuccessStatusCode ?? false;
        http.Request = val?.RequestMessage;
        http.Result =
            val?.IsSuccessStatusCode ?? false
                ? await val.Content.ReadAsStreamAsync()
                : null;
        return http;
    }

    /// <summary>
    ///     Http method that trys to get a stream
    /// </summary>
    /// <returns></returns>
    public static async Task<HttpResult<byte[]?>> TryGetByteArrayAsync(string url) {
        var http = new HttpResult<byte[]?>();
        var val = await CreateHttpClient()
                      .GetAsync(url)
                      .TryAsync(a => http.Exception = a as HttpRequestException);

        http.IsSuccess = val?.IsSuccessStatusCode ?? false;
        http.Request = val?.RequestMessage;
        http.Result =
            val?.IsSuccessStatusCode ?? false
                ? await val.Content.ReadAsByteArrayAsync()
                : null;
        return http;
    }


    /// <summary>
    ///   Http method that trys to Head a request
    ///   </summary>
    public static async Task<HttpResult<HttpResponseMessage>> TryHeadAsync(string url) {
        var http = new HttpResult<HttpResponseMessage>();
        var val = await CreateHttpClient()
                      .SendAsync(new(HttpMethod.Head, url))
                      .TryAsync(a => http.Exception = a as HttpRequestException);

        http.IsSuccess = val?.IsSuccessStatusCode ?? false;
        http.Request = val?.RequestMessage;
        http.Result = val;
        return http;
    }

    /// <summary>
    ///   Http method that trys to get a type async
    /// </summary>
    public static async Task<HttpResult<T?>> TryGetAsync<T>(string url) {
        var http = new HttpResult<T?>();
        var val = await CreateHttpClient()
                      .GetAsync(url)
                      .TryAsync(a => http.Exception = a as HttpRequestException);

        http.IsSuccess = val?.IsSuccessStatusCode ?? false;
        http.Request = val?.RequestMessage;
        http.Result =
            val?.IsSuccessStatusCode ?? false
                ? await val.Content.ReadFromJsonAsync<T>()
                : default;
        return http;
    }
}
