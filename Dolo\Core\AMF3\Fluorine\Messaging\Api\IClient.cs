﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     The client object represents a single client. One client may have multiple connections to different scopes on the
///     same host.
/// </summary>
internal interface IClient : IAttributeStore
{
    /// <summary>
    ///     Gets the client identity.
    /// </summary>
    /// <remarks>
    ///     This will be generated by the server if not passed upon connection from client-side Flex/Flash app.
    /// </remarks>
    string Id { get; }
    /// <summary>
    ///     Get a set of scopes the client is connected to.
    /// </summary>
    ICollection Scopes { get; }
    /// <summary>
    ///     Get a set of connections of a given scope.
    /// </summary>
    ICollection Connections { get; }
    /// <summary>
    ///     Gets an object that can be used to synchronize access.
    /// </summary>
    object SyncRoot { get; }
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    int ClientLeaseTime { get; }
    /// <summary>
    ///     Closes all the connections.
    /// </summary>
    void Disconnect();
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    void Timeout();
    /// <summary>
    ///     Gets pending messages.
    /// </summary>
    /// <param name="waitIntervalMillis"></param>
    /// <returns></returns>
    IMessage[] GetPendingMessages(int waitIntervalMillis);
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="messageClient"></param>
    void RegisterMessageClient(IMessageClient messageClient);
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="messageClient"></param>
    void UnregisterMessageClient(IMessageClient messageClient);
    /// <summary>
    ///     Renews a lease.
    /// </summary>
    void Renew();
    /// <summary>
    ///     Renews a lease.
    /// </summary>
    /// <param name="clientLeaseTime">The amount of time in minutes before client times out.</param>
    void Renew(int clientLeaseTime);
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="connection"></param>
    void Register(IConnection connection);
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="connection"></param>
    void Unregister(IConnection connection);
}