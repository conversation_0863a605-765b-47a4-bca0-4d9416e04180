﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Util;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal sealed class NumberUtils
{

    /// <summary>
    ///     Determines whether the supplied <paramref name="number" /> is an integer.
    /// </summary>
    /// <param name="number">The object to check.</param>
    /// <returns>
    ///     <see lang="true" /> if the supplied <paramref name="number" /> is an integer.
    /// </returns>
    public static bool IsInteger(object number)
        => number is int       || number is short || number is long || number is uint
           || number is ushort || number is ulong || number is byte || number is sbyte;

    /// <summary>
    ///     Determines whether the supplied <paramref name="number" /> is a decimal number.
    /// </summary>
    /// <param name="number">The object to check.</param>
    /// <returns>
    ///     <see lang="true" /> if the supplied <paramref name="number" /> is a decimal number.
    /// </returns>
    public static bool IsDecimal(object number)
        => number is float || number is double || number is decimal;

    /// <summary>
    ///     Determines whether the supplied <paramref name="number" /> is decimal number.
    /// </summary>
    /// <param name="number">The object to check.</param>
    /// <returns>
    ///     <c>true</c> if the specified object is decimal number; otherwise, <c>false</c>.
    /// </returns>
    public static bool IsNumber(object number)
        => IsInteger(number) || IsDecimal(number);

    /// <summary>
    ///     Is the supplied <paramref name="number" /> equal to zero (0)?
    /// </summary>
    /// <param name="number">The number to check.</param>
    /// <returns>
    ///     <see lang="true" /> id the supplied <paramref name="number" /> is equal to zero (0).
    /// </returns>
    public static bool IsZero(object number)
    {
        if (number is int) return (int)number                 == 0;
        if (number is short) return (short)number             == 0;
        if (number is long) return (long)number               == 0;
        if (number is ushort) return (int)number              == 0;
        if (number is uint) return (long)number               == 0;
        if (number is ulong) return Convert.ToDecimal(number) == 0;
        if (number is byte) return (short)number              == 0;
        if (number is sbyte) return (short)number             == 0;
        if (number is float) return (float)number             == 0f;
        if (number is double) return (double)number           == 0d;
        if (number is decimal) return (decimal)number         == 0m;
        return false;
    }

    /// <summary>
    ///     Negates the supplied <paramref name="number" />.
    /// </summary>
    /// <param name="number">The number to negate.</param>
    /// <returns>The supplied <paramref name="number" /> negated.</returns>
    /// <exception cref="System.ArgumentException">
    ///     If the supplied <paramref name="number" /> is not a supported numeric type.
    /// </exception>
    public static object Negate(object number)
    {
        if (number is int) return -(int)number;
        if (number is short) return -(short)number;
        if (number is long) return -(long)number;
        if (number is ushort) return -(int)number;
        if (number is uint) return -(long)number;
        if (number is ulong) return -Convert.ToDecimal(number);
        if (number is byte) return -(short)number;
        if (number is sbyte) return -(short)number;
        if (number is float) return -(float)number;
        if (number is double) return -(double)number;
        if (number is decimal) return -(decimal)number;
        throw new ArgumentException(string.Format("'{0}' is not one of the supported numeric types.", number));
    }

    /// <summary>
    ///     Adds the specified numbers.
    /// </summary>
    /// <param name="m">The first number.</param>
    /// <param name="n">The second number.</param>
    public static object Add(object m, object n)
    {
        CoerceTypes(ref m, ref n);

        if (n is int) return (int)m         + (int)n;
        if (n is short) return (short)m     + (short)n;
        if (n is long) return (long)m       + (long)n;
        if (n is ushort) return (ushort)m   + (ushort)n;
        if (n is uint) return (uint)m       + (uint)n;
        if (n is ulong) return (ulong)m     + (ulong)n;
        if (n is byte) return (byte)m       + (byte)n;
        if (n is sbyte) return (sbyte)m     + (sbyte)n;
        if (n is float) return (float)m     + (float)n;
        if (n is double) return (double)m   + (double)n;
        if (n is decimal) return (decimal)m + (decimal)n;

        return null;
    }

    /// <summary>
    ///     Subtracts the specified numbers.
    /// </summary>
    /// <param name="m">The first number.</param>
    /// <param name="n">The second number.</param>
    public static object Subtract(object m, object n)
    {
        CoerceTypes(ref m, ref n);

        if (n is int) return (int)m         - (int)n;
        if (n is short) return (short)m     - (short)n;
        if (n is long) return (long)m       - (long)n;
        if (n is ushort) return (ushort)m   - (ushort)n;
        if (n is uint) return (uint)m       - (uint)n;
        if (n is ulong) return (ulong)m     - (ulong)n;
        if (n is byte) return (byte)m       - (byte)n;
        if (n is sbyte) return (sbyte)m     - (sbyte)n;
        if (n is float) return (float)m     - (float)n;
        if (n is double) return (double)m   - (double)n;
        if (n is decimal) return (decimal)m - (decimal)n;

        return null;
    }

    /// <summary>
    ///     Multiplies the specified numbers.
    /// </summary>
    /// <param name="m">The first number.</param>
    /// <param name="n">The second number.</param>
    public static object Multiply(object m, object n)
    {
        CoerceTypes(ref m, ref n);

        if (n is int) return (int)m         * (int)n;
        if (n is short) return (short)m     * (short)n;
        if (n is long) return (long)m       * (long)n;
        if (n is ushort) return (ushort)m   * (ushort)n;
        if (n is uint) return (uint)m       * (uint)n;
        if (n is ulong) return (ulong)m     * (ulong)n;
        if (n is byte) return (byte)m       * (byte)n;
        if (n is sbyte) return (sbyte)m     * (sbyte)n;
        if (n is float) return (float)m     * (float)n;
        if (n is double) return (double)m   * (double)n;
        if (n is decimal) return (decimal)m * (decimal)n;

        return null;
    }

    /// <summary>
    ///     Divides the specified numbers.
    /// </summary>
    /// <param name="m">The first number.</param>
    /// <param name="n">The second number.</param>
    public static object Divide(object m, object n)
    {
        CoerceTypes(ref m, ref n);

        if (n is int) return (int)m         / (int)n;
        if (n is short) return (short)m     / (short)n;
        if (n is long) return (long)m       / (long)n;
        if (n is ushort) return (ushort)m   / (ushort)n;
        if (n is uint) return (uint)m       / (uint)n;
        if (n is ulong) return (ulong)m     / (ulong)n;
        if (n is byte) return (byte)m       / (byte)n;
        if (n is sbyte) return (sbyte)m     / (sbyte)n;
        if (n is float) return (float)m     / (float)n;
        if (n is double) return (double)m   / (double)n;
        if (n is decimal) return (decimal)m / (decimal)n;

        return null;
    }

    /// <summary>
    ///     Calculates remainder for the specified numbers.
    /// </summary>
    /// <param name="m">The first number (dividend).</param>
    /// <param name="n">The second number (divisor).</param>
    public static object Modulus(object m, object n)
    {
        CoerceTypes(ref m, ref n);

        if (n is int) return (int)m         % (int)n;
        if (n is short) return (short)m     % (short)n;
        if (n is long) return (long)m       % (long)n;
        if (n is ushort) return (ushort)m   % (ushort)n;
        if (n is uint) return (uint)m       % (uint)n;
        if (n is ulong) return (ulong)m     % (ulong)n;
        if (n is byte) return (byte)m       % (byte)n;
        if (n is sbyte) return (sbyte)m     % (sbyte)n;
        if (n is float) return (float)m     % (float)n;
        if (n is double) return (double)m   % (double)n;
        if (n is decimal) return (decimal)m % (decimal)n;

        return null;
    }

    /// <summary>
    ///     Raises first number to the power of the second one.
    /// </summary>
    /// <param name="m">The first number.</param>
    /// <param name="n">The second number.</param>
    public static object Power(object m, object n)
        => Math.Pow(Convert.ToDouble(m), Convert.ToDouble(n));


    /// <summary>
    ///     Coerces the types so they can be compared.
    /// </summary>
    /// <param name="m">The right.</param>
    /// <param name="n">The left.</param>
    public static void CoerceTypes(ref object m, ref object n)
    {
        var leftTypeCode = System.Convert.GetTypeCode(m);
        var rightTypeCode = System.Convert.GetTypeCode(n);

        if (leftTypeCode > rightTypeCode)
            n = System.Convert.ChangeType(n, leftTypeCode, null);
        else
            m = System.Convert.ChangeType(m, rightTypeCode, null);
    }

    public static int HexToInt(char h)
    {
        if (h is >= '0' and <= '9') return h       - '0';
        if (h is >= 'a' and <= 'f') return h - 'a' + 10;
        if (h is >= 'A' and <= 'F') return h - 'A' + 10;
        return -1;
    }

    public static char IntToHex(int n)
    {
        if (n <= 9) return (char)(n + 48);
        return (char)(n             - 10 + 97);
    }
}