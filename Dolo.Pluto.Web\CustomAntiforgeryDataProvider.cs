using Microsoft.AspNetCore.Antiforgery;
using System.Security.Claims;

namespace Dolo.Pluto.Web;

public class CustomAntiforgeryDataProvider : IAntiforgeryAdditionalDataProvider
{
    public string GetAdditionalData(HttpContext context)
    {
        var user = context.User;
        if (user?.Identity?.IsAuthenticated == true)
        {
            // Try to get a unique identifier from various claim types
            var identifier = user.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                           user.FindFirst(ClaimTypes.Name)?.Value ??
                           user.FindFirst("id")?.Value ??
                           "anonymous";
            
            return identifier;
        }
        
        return string.Empty;
    }

    public bool ValidateAdditionalData(HttpContext context, string additionalData)
    {
        return GetAdditionalData(context) == additionalData;
    }
}