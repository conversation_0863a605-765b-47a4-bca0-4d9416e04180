import React from 'react';
import errorReportingService from '../services/errorReportingService';

/**
 * ErrorBoundary Component
 * 
 * A React Error Boundary that catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 * 
 * Features:
 * - Catches and handles React component errors
 * - Automatically reports errors to developers
 * - Provides a graceful fallback UI
 * - Allows recovery without full page reload
 * - Integrates with the global error reporting system
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Store error details in state
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Report the error to the error reporting service
    try {
      const errorDetails = {
        type: 'React Error Boundary',
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorBoundary: this.constructor.name
      };
      
      errorReportingService.manualReport(error, errorDetails);
    } catch (reportingError) {
      console.warn('Failed to report error through ErrorBoundary:', reportingError);
    }

    // Call the onError prop if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    // Reset the error state to retry rendering
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null 
    });
  };

  render() {
    if (this.state.hasError) {
      // Render custom fallback UI if provided, otherwise render default
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Default fallback UI
      return (
        <div className="min-h-screen bg-bg-base flex items-center justify-center p-4">
          <div className="bg-bg-surface border border-border-l1 rounded-xl shadow-lg max-w-md w-full p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                <svg 
                  className="w-6 h-6 text-white" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth="2" 
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" 
                  />
                </svg>
              </div>
              <div>
                <h2 className="text-lg font-semibold text-span-primary">
                  Something went wrong
                </h2>
                <p className="text-sm text-span-muted">
                  The application encountered an error
                </p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-span-secondary text-sm leading-relaxed mb-3">
                We've encountered an unexpected error. This has been automatically reported to our development team.
              </p>
              
              <div className="flex items-center space-x-2 text-xs text-span-muted mb-3">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Error automatically reported to developers</span>
              </div>
            </div>

            {/* Error details (collapsible) */}
            {this.state.error && (
              <details className="mb-4">
                <summary className="text-xs text-span-muted cursor-pointer hover:text-span-secondary transition-colors select-none">
                  Technical details (click to expand)
                </summary>
                <div className="mt-2 p-3 bg-bg-hover rounded-lg border border-border-l1">
                  <code className="text-xs text-span-muted font-mono break-all">
                    {this.state.error.message}
                  </code>
                </div>
              </details>
            )}

            <div className="flex space-x-3">
              <button
                onClick={this.handleRetry}
                className="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-primary/50"
              >
                Try Again
              </button>
              <button
                onClick={() => window.location.reload()}
                className="flex-1 px-4 py-2 bg-bg-hover text-span-secondary border border-border-l1 rounded-lg hover:bg-bg-surface transition-colors font-medium focus:outline-none focus:ring-2 focus:ring-border-l1"
              >
                Reload App
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
