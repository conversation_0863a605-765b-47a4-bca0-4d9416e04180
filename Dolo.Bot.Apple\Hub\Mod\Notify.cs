﻿using Dolo.Core;
namespace Dolo.Bot.Apple.Hub.Mod;

public class Notify
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("notify")]
    [Description("send notifications")]
    public async Task NotifyAsync(SlashCommandContext ctx)
    {
        foreach (var channel in HubConstant.ActivityChannels)
            await channel!.TrySendMessageAsync(RandomStringGenerator.GetLorem())
                .TryDeleteAfterAsync(TimeSpan.FromMilliseconds(1));
    }
}