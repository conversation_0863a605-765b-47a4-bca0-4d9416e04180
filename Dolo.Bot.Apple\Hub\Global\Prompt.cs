﻿using Dolo.Core.OpenAi;
using System.Text;
using System.Text.Json;
using JsonSerializer=System.Text.Json.JsonSerializer;

namespace Dolo.Bot.Apple.Hub.Global;

public class Prompt
{
    [Command("prompt")]
    [Description("use openai to generate a prompt")]
    public async Task WelcomeAsync(SlashCommandContext ctx, [Description("the prompt message")] string message,
        [Description("enable typing effect")] bool typing = false,
        [Description("use the assistant example: (you are an it expert..)")] string? assistant = null)
    {
        await ctx.Interaction.DeferAsync();

        if (string.IsNullOrEmpty(message))
        {
            await ctx.TryEditResponseAsync("Please provide a message");
            return;
        }

        if (!File.Exists("prompt.token"))
        {
            await ctx.TryEditResponseAsync("Please set the prompt token first");
            return;
        }

        var token = await File.ReadAllTextAsync("prompt.token");
        var prompt = await BypassAI.PromptAsync(a => {
            a.AddMessage(message);
            if (!string.IsNullOrEmpty(assistant))
                a.<PERSON>(assistant, Role.Assistant);
            a.<PERSON>(token);
        });
        if (prompt is null)
        {
            await ctx.TryEditResponseAsync("Please try again later");
            return;
        }

        var stringBuilder = new StringBuilder();
        var reader = new StreamReader(prompt);
        var text = "";
        while (!reader.EndOfStream)
        {
            var json = await reader.ReadLineAsync();
            if (string.IsNullOrEmpty(json)) continue;

            var content = json[6..];
            if (content == "end") continue;

            var data = JsonSerializer.Deserialize<JsonElement>(content);
            var current = data.GetProperty("current").GetString();

            text += current;
            stringBuilder.Append(current);

            if (!typing)
                continue;

            await ctx.TryEditResponseAsync(text);
            await Task.Delay(10);
        }

        if (!typing)
            await ctx.TryEditResponseAsync(stringBuilder.ToString());
    }
}