using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Security.Cryptography.X509Certificates;
using Dolo.Core;
using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor.Services;
using Dolo.Core.Interceptor.Extensions;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor;

public sealed class HttpsInterceptor : IDisposable {
    private readonly InterceptorConfig _config;
    private readonly CertificateService _certificateService;
    private readonly AmfService _amfService;
    private readonly ResponseDecodingService _responseDecodingService;
    private readonly HttpTransactionManager _transactionManager;
    private readonly ConnectionManager _connectionManager;
    private readonly ParallelProcessingEngine _processingEngine;
    private readonly HttpDataRelayService _dataRelayService;
    private readonly SslConnectionHandler _sslHandler;
    private readonly TunnelingService _tunnelingService;
    private readonly HttpRequestHandler _requestHandler;
    private readonly BreakpointManager _breakpointManager;
    private readonly SemaphoreSlim _startupSemaphore = new(1, 1);
    private readonly ILogger<HttpsInterceptor> _logger;
    private TcpListener? _proxyListener;
    private CancellationTokenSource? _cancellationTokenSource;
    private volatile bool _isRunning;
    private bool _disposed;

    public HttpsInterceptor(InterceptorConfig? config = null) {
        _config = config ?? new InterceptorConfig();
        _certificateService = new CertificateService(_config);
        _amfService = new AmfService();

        var loggerFactory = _config.LoggerFactory ?? LoggerFactory.Create(builder => builder.SetMinimumLevel(LogLevel.None));
        _logger = loggerFactory.CreateLogger<HttpsInterceptor>();

        var responseLogger = loggerFactory.CreateLogger<ResponseDecodingService>();
        _responseDecodingService = new ResponseDecodingService(responseLogger, _amfService);

        var transactionLogger = loggerFactory.CreateLogger<HttpTransactionManager>();
        _transactionManager = new HttpTransactionManager(transactionLogger, _responseDecodingService, loggerFactory: loggerFactory);

        var connectionLogger = loggerFactory.CreateLogger<ConnectionManager>();
        _connectionManager = new ConnectionManager(connectionLogger);

        var processingLogger = loggerFactory.CreateLogger<ParallelProcessingEngine>();
        _processingEngine = new ParallelProcessingEngine(processingLogger);

        var breakpointLogger = loggerFactory.CreateLogger<BreakpointManager>();
        _breakpointManager = new BreakpointManager(_config.Breakpoints, breakpointLogger);

        var dataRelayLogger = loggerFactory.CreateLogger<HttpDataRelayService>();
        _dataRelayService = new HttpDataRelayService(_transactionManager, _amfService, _processingEngine, _breakpointManager, dataRelayLogger);

        var sslLogger = loggerFactory.CreateLogger<SslConnectionHandler>();
        _sslHandler = new SslConnectionHandler(_certificateService, _connectionManager, _transactionManager, _dataRelayService, _config, sslLogger);

        var tunnelingLogger = loggerFactory.CreateLogger<TunnelingService>();
        _tunnelingService = new TunnelingService(tunnelingLogger);

        var requestLogger = loggerFactory.CreateLogger<HttpRequestHandler>();
        _requestHandler = new HttpRequestHandler(_config, _sslHandler, _tunnelingService, requestLogger);

        _certificateService.CertificateGenerated += OnCertificateGenerated;
        _transactionManager.TransactionCompleted += OnTransactionCompleted;
        _transactionManager.TransactionTimedOut += OnTransactionTimedOut;
        _connectionManager.ConnectionClosed += OnConnectionClosed;
        _breakpointManager.BreakpointHit += OnBreakpointHit;

        // THREAD SAFETY: Initialize global thread safety validation
        var threadSafetyLogger = loggerFactory.CreateLogger<ThreadSafetyValidator>();
        ThreadSafetyExtensions.InitializeThreadSafetyValidation(threadSafetyLogger);
    }

    public event AsyncEventHandler<HttpsInterceptor, CertificateGeneratedEventArgs>? CertificateGenerated;
    public event AsyncEventHandler<HttpsInterceptor, HttpTransactionCompletedEventArgs>? TransactionCompleted;
    public event AsyncEventHandler<HttpsInterceptor, HttpTransactionTimedOutEventArgs>? TransactionTimedOut;
    public event AsyncEventHandler<HttpsInterceptor, InterceptorErrorEventArgs>? ErrorOccurred;
    public event AsyncEventHandler<HttpsInterceptor, TrafficInterceptedEventArgs>? TrafficIntercepted;
    public event AsyncEventHandler<HttpsInterceptor, BreakpointHitEventArgs>? BreakpointHit;

    public X509Certificate2 RootCertificate => _certificateService.RootCertificate;
    public bool IsRunning => _isRunning;
    public InterceptorConfig Config => _config;

    /// <summary>
    /// Resolves a breakpoint with the specified action
    /// </summary>
    public bool ResolveBreakpoint(string breakpointId, BreakpointAction action)
    {
        return _breakpointManager.ResolveBreakpoint(breakpointId, action);
    }

    /// <summary>
    /// Gets all active breakpoints
    /// </summary>
    public IReadOnlyDictionary<string, BreakpointHitEventArgs> GetActiveBreakpoints()
    {
        return _breakpointManager.GetActiveBreakpoints();
    }

    /// <summary>
    /// Gets the current breakpoint queue size
    /// </summary>
    public int BreakpointQueueSize => _breakpointManager.QueueSize;

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        await _startupSemaphore.WaitAsync(cancellationToken).ConfigureAwait(false);

        try {
            if (_isRunning)
                throw new InvalidOperationException("Interceptor is already running");

            _logger.LogInformation("Starting HTTPS Interceptor");

            await EnsurePortAvailableAsync().ConfigureAwait(false);

            _proxyListener = new TcpListener(_config.BindAddress, _config.ProxyPort);
            _proxyListener.Start();

            _logger.LogInformation("Proxy server started on {BindAddress}:{ProxyPort}", _config.BindAddress, _config.ProxyPort);

            _cancellationTokenSource = new CancellationTokenSource();
            _isRunning = true;

            _logger.LogInformation("Interceptor ready - waiting for connections");

            _ = Task.Run(() => AcceptConnectionsAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);
        }
        finally
        {
            _startupSemaphore.Release();
        }
    }

    public async Task StopAsync() {
        if (!_isRunning) return;

        _logger.LogInformation("Stopping HTTPS Interceptor");

        _isRunning = false;
        _cancellationTokenSource?.Cancel();

        _proxyListener?.Stop();
        _proxyListener = null;

        await _processingEngine.StopAsync().ConfigureAwait(false);
        await Task.Delay(1000).ConfigureAwait(false);

        _logger.LogInformation("HTTPS Interceptor stopped");
    }

    public bool InstallRootCertificate() => _certificateService.InstallRootCertificate();

    public void ClearCertificateCache()
    {
        _certificateService.ClearCertificateCache();
    }

    public (int ActiveConnections, int PendingTransactions, long TotalPaired, long TotalTimeouts) GetStatistics() {
        return _transactionManager.GetStatistics();
    }

    public List<CompletedTransaction> GetTransactionsByHost(string hostname, int? port = null) {
        return _transactionManager.GetTransactionsByHost(hostname, port);
    }

    /// <summary>
    /// Checks if there are invalid cached certificates that should be cleared.
    /// </summary>
    public bool HasInvalidCachedCertificates()
    {
        return _certificateService.HasInvalidCachedCertificates();
    }

    private async Task AcceptConnectionsAsync(CancellationToken cancellationToken) {
        try {
            while (!cancellationToken.IsCancellationRequested && _proxyListener != null) {
                var client = await _proxyListener.AcceptTcpClientAsync(cancellationToken).ConfigureAwait(false);
                _ = Task.Run(() => _requestHandler.HandleClientAsync(client), cancellationToken);
            }
        }
        catch (ObjectDisposedException) {
            // Expected when stopping
        }
        catch (Exception ex) {
            _logger.LogError(ex, "Error accepting connections: {Error}", ex.Message);

            if (ErrorOccurred != null)
                await ErrorOccurred.InvokeAsync(this, new InterceptorErrorEventArgs("AcceptConnections", ex.Message, ex)).ConfigureAwait(false);
        }
    }

    private async Task EnsurePortAvailableAsync() {
        var properties = IPGlobalProperties.GetIPGlobalProperties();
        var listeners = properties.GetActiveTcpListeners();

        if (listeners.Any(l => l.Port == _config.ProxyPort)) {
            throw new InvalidOperationException($"Port {_config.ProxyPort} is already in use");
        }

        await Task.CompletedTask;
    }
    private async void OnCertificateGenerated(object? sender, CertificateGeneratedEventArgs e) {
        if (CertificateGenerated != null)
            await CertificateGenerated.InvokeAsync(this, e).ConfigureAwait(false);
    }

    private async void OnTransactionCompleted(object? sender, HttpTransactionCompletedEventArgs e)
    {
        if (TransactionCompleted != null)
            await TransactionCompleted.InvokeAsync(this, e).ConfigureAwait(false);

        if (TrafficIntercepted != null)
        {
            var args = new TrafficInterceptedEventArgs(
                e.Hostname,
                e.Port,
                e.Protocol,
                e.BytesTransferred,
                e.Duration,
                e.Request,
                e.Response);

            await TrafficIntercepted.InvokeAsync(this, args).ConfigureAwait(false);
        }
    }

    private async void OnTransactionTimedOut(object? sender, HttpTransactionTimedOutEventArgs e)
    {
        if (TransactionTimedOut != null)
            await TransactionTimedOut.InvokeAsync(this, e).ConfigureAwait(false);

        _logger.LogWarning("Transaction {TransactionId} timed out", e.Transaction.TransactionId);
    }

    private void OnConnectionClosed(object? sender, ConnectionClosedEventArgs e) {
        _logger.LogDebug("Connection {ConnectionId} closed: {Reason}", e.ConnectionId, e.Reason);

        // CRITICAL FIX: Clean up per-connection breakpoint context when connection closes
        _breakpointManager.RemoveConnectionContext(e.ConnectionId);

        // Also clean up transaction manager connection context
        _transactionManager.CloseConnection(e.ConnectionId);
    }

    private async Task OnBreakpointHit(BreakpointManager sender, BreakpointHitEventArgs e)
    {
        if (BreakpointHit != null)
            await BreakpointHit.InvokeAsync(this, e).ConfigureAwait(false);

        _logger.LogInformation("Breakpoint hit: {BreakpointId} for {Type} on {Url}",
            e.BreakpointId, e.Type, e.Url);
    }

    public void Dispose() {
        if (_disposed) return;
        _disposed = true;

        StopAsync().GetAwaiter().GetResult();

        _startupSemaphore?.Dispose();
        _cancellationTokenSource?.Dispose();
        _certificateService?.Dispose();

        _connectionManager?.Dispose();
        _processingEngine?.Dispose();
        _transactionManager?.Dispose();
        _dataRelayService?.Dispose();
        _breakpointManager?.Dispose();

        // THREAD SAFETY: Cleanup thread safety validation
        ThreadSafetyExtensions.DisposeThreadSafetyValidation();
    }
}
