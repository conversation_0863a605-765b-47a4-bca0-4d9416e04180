﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
public class AMFHeader
{
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string CredentialsHeader = "Credentials";
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string ServiceBrowserHeader = "DescribeService";
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string ClearedCredentials = "ClearedCredentials";
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string CredentialsIdHeader = "CredentialsId";
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public const string RequestPersistentHeader = "RequestPersistentHeader";

    /// <summary>
    ///     Initializes a new instance of the AMFHeader class.
    /// </summary>
    /// <param name="name"></param>
    /// <param name="mustUnderstand"></param>
    /// <param name="content"></param>
    public AMFHeader(string name, bool mustUnderstand, object? content)
    {
        Name = name;
        MustUnderstand = mustUnderstand;
        Content = content;
    }
    /// <summary>
    ///     Gets the header name.
    /// </summary>
    public string? Name
    {
        get;
    }
    /// <summary>
    ///     Gets the header content.
    /// </summary>
    public object? Content
    {
        get;
    }
    /// <summary>
    ///     If a header is sent to the Flash Player with must understand set to true and the NetConnection instance's client
    ///     object does not have a method to handle the header, then the Flash Player will invoke the onStatus handler on the
    ///     NetConnection object.
    /// </summary>
    public bool MustUnderstand
    {
        get;
    }
    /// <summary>
    ///     This member supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public bool IsClearedCredentials
    {
        get
        {
            if (Content is string)
                return (string)Content == ClearedCredentials;
            return false;
        }
    }
}
