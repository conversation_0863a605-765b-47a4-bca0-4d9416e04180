﻿using Dolo.Core.Cryption;
using MethodTimer;
using System.Buffers.Text;
using System.Net.Security;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;

namespace Dolo.Core.Http;

public static partial class Http
{

    /// <summary>
    ///     Creates an HttpClient instance with the specified proxy.
    /// </summary>
    /// <param name="proxy">The proxy to be used by the HttpClient. If null, the HttpClient will be created without a proxy.</param>
    /// <returns>An HttpClient instance configured with the specified proxy.</returns>
    private static HttpClient CreateHttpClient(IWebProxy? proxy = null, bool useOCSP = false)
    {
        var handler = new HttpClientHandler();
        if (useOCSP)
        {
            handler.SslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13;
            handler.CheckCertificateRevocationList = true;
            handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
            {
                if (sslPolicyErrors != SslPolicyErrors.None
                    || chain is null
                    || cert is null)
                    return false;

                chain.ChainPolicy = new()
                {
                    RevocationMode = X509RevocationMode.Online,
                    RevocationFlag = X509RevocationFlag.ExcludeRoot
                };
                var chainBuilt = chain.Build(cert);
                return chainBuilt;
            };
        }

        if (proxy == null)
            return new(handler);

        handler.UseProxy = true;
        handler.Proxy = proxy;
        return new(handler);
    }

    /// <summary>
    ///     Http method that sends a http request
    ///     you can specify the method, url, headers and body in the config
    ///     deserialize the response to the specified type
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="config"></param>
    /// <returns></returns>
    [Time]
    public static async Task<HttpResponse<T>> TrySendAsync<T>(Action<HttpConfig> config)
    {
        var cfg = config.GetAction();
        var client = CreateHttpClient(cfg.Proxy, cfg.UseOCSP);
        var request = new HttpRequestMessage(cfg.Method, cfg.Url)
        {
            Content = cfg.Content,
            Version = cfg.Version ?? HttpVersion.Version11
        };
        var response = new HttpResponse<T>
        {
            Request = request
        };

        if (cfg.WithoutTokenType)
            request.Headers.TryAddWithoutValidation("Authorization", cfg.AuthToken);
        else if (cfg.AuthToken != null)
            request.Headers.Authorization = new(cfg.AuthType ?? "Bearer", cfg.AuthToken);

        // set content type header 
        if (cfg.ContentType != null && request.Content != null)
            request.Content.Headers.ContentType = new(cfg.ContentType.GetContentType());

        // set referer header
        if (cfg.Referer != null) request.Headers.Referrer = new(cfg.Referer);

        // set accept header
        if (cfg.Accept != null) request.Headers.Accept.TryParseAdd(cfg.Accept);

        // set encoding header
        if (cfg.Encoding != null) request.Headers.AcceptEncoding.TryParseAdd(cfg.Encoding);

        // set user-agent
        if (cfg.Agent != null) request.Headers.UserAgent.TryParseAdd(cfg.Agent);

        // set origin
        if (cfg.Origin != null) request.Headers.TryAddWithoutValidation("Origin", cfg.Origin);

        if (cfg.HttpHeaders.Count != 0)
        foreach (var header in cfg.HttpHeaders)
            request.Headers.TryAddWithoutValidation(header.Key, header.Value);

        // make a request
        try
        {
            response.Response = await RetryPolicy.ExecuteAsync(async () => await client.SendAsync(request.Clone(), cfg.CancellationToken.GetValueOrDefault()));
            response.IsSuccess = response.Response.IsSuccessStatusCode;

            // if the response is not successful, return the response
            if (!response.IsSuccess)
                return response;

            if (cfg.UseDecryption) {
                // if decryption is enabled, decrypt the response content
                var encryptedContent = await response.Response.Content.ReadAsStringAsync();
                var decryptedContent = CustomEncryptor.DoDecrypt(encryptedContent);
                if (string.IsNullOrEmpty(decryptedContent)) {
                    response.Exception = new InvalidOperationException("Decrypted content is null or empty.");
                    response.IsSuccess = false;
                    return response;
                }

                Console.WriteLine(decryptedContent);
                Console.WriteLine(typeof(T).ToJson());
                response.Body = JsonConvert.DeserializeObject<T>(decryptedContent);
                return response; 
            }

            // if the response is successful, try to deserialize the response content
            response.Body = await response.Response.Content.ReadAsAsync<T>().TryAsync(a => Console.WriteLine(a.Message));
            return response;
        }
        catch (Exception ex)
        {
            response.Exception = ex;
        }
        return response;
    }
}
