﻿namespace Dolo.Bot.Apple.Hub.System;

public static class BadAttachmentFilter
{
    public static async Task<bool> IsOkAsync(MessageArgs e)
    {
        if (!e.Message.Attachments.Any(a => a.MediaType is "application/x-msdos-program" or "application/zip"))
            return true;

        if (!e.Guild.TryGetMember(e.Author.Id, out var guildMember)
            || e.Author.IsBot
            || guildMember.Roles.ContainsMany(HubRoles.Friend, HubRoles.Admin)
            || HubChannel.DevLog is null)
            return true;


        await e.Message.TryDeleteMessageAsync();
        await e.Channel.TrySendMessageAsync($"{e.Author.Mention}, You are not allowed to send this type of file.");

        await HubChannel.DevLog.TrySendMessageAsync($"User: {e.Message.Author.Mention} ({e.Message.Author.Id}) tried to send a file with the following extension: {e.Message.Attachments[0].MediaType}");
        return false;
    }
}