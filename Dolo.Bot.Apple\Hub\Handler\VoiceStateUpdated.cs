﻿namespace Dolo.Bot.Apple.Hub.Handler;

public static class VoiceStateUpdated
{
    public static async Task InvokeAsync(this VoiceStateUpdatedEventArgs e)
    {
        var debugUser = await e.GetUserAsync();
        Console.WriteLine($"[Voice Debug] VoiceStateUpdated triggered for user {debugUser?.Username} ({debugUser?.Id})");

        if (Hub.Guild is null
            || HubChannel.Voice is null
            || HubChannel.VoiceTopic is null)
        {
            Console.WriteLine("[Voice Debug] Early return: Hub.Guild, HubChannel.Voice, or HubChannel.VoiceTopic is null");
            return;
        }

        var guild = await e.GetGuildAsync();
        var user = await e.GetUserAsync();
        var channel = await e.GetChannelAsync();
        DiscordChannel? afterChannel = null;
        if (e.After != null)
        {
            afterChannel = await e.After.GetChannelAsync();
            if (afterChannel != null && afterChannel.Parent != HubChannel.VoiceTopic)
                return;
        }

        if(channel != null && channel.Parent != HubChannel.VoiceTopic)
            return;

        var beforeChannel = e.Before != null ? await e.Before.GetChannelAsync() : null;
        
        
        
        // invoked when the user joined the create channel
        Console.WriteLine($"[Voice Debug] Checking if user joined create channel. Channel ID: {channel?.Id}, Create Channel ID: {HubChannel.Voice?.Id}");
        if (channel?.Id == HubChannel.Voice.Id)
        {
            Console.WriteLine($"[Voice Debug] User joined create channel, invoking UserJoinedCreateChannel");
            await UserJoinedCreateChannel.InvokeAsync(new(channel, user, guild));
        }

        // invoked when the user left the voice channel and it was empty
        if (beforeChannel != null && beforeChannel.Id != HubChannel.Voice.Id && !beforeChannel.Users.Any())
            await UserLeftVoiceEmpty.InvokeAsync(new(beforeChannel, user, guild));

        // invoked when the user joined the voice channel
        if (afterChannel != null && afterChannel.Id != HubChannel.Voice.Id)
            await UserJoinedVoice.InvokeAsync(new(channel, user, guild));

        // invoked when the user left the voice channel
        if (e.Before                     != null
            && e.Before.IsSelfMuted      == e.After?.IsSelfMuted
            && e.Before.IsSelfDeafened   == e.After?.IsSelfDeafened
            && e.Before.IsSelfStream     == e.After?.IsSelfStream
            && e.Before.IsSelfVideo      == e.After?.IsSelfVideo
            && e.Before.IsServerDeafened == e.After?.IsServerDeafened
            && e.Before.IsServerMuted    == e.After?.IsServerMuted
            && e.Before.IsSuppressed     == e.After?.IsSuppressed)
            await UserLeftVoice.InvokeAsync(new(beforeChannel, user, guild));
    }
}
