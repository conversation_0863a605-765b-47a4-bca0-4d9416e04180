﻿namespace Dolo.Core.OpenAi;

internal class BypassAIMessages
{
    [JsonProperty("profile")]
    public string? Profile { get; set; }
    [JsonProperty("chat")]
    public ChatContent? Chat { get; set; }

    public class ChatContent
    {
        [JsonProperty("messages")]
        public List<Message>? Messages { get; set; }
    }

    public class Message
    {

        [JsonProperty("role")]
        public string? Role { get; set; }
        [JsonProperty("text")]
        public string? Text { get; set; }
    }
}