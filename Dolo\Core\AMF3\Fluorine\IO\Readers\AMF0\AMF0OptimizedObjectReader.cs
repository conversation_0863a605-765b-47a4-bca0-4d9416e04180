﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO.Bytecode;
namespace Dolo.Core.AMF3.Fluorine.IO.Readers.AMF0;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF0OptimizedObjectReader : IAMFReader
{
    private readonly Hashtable _optimizedReaders;

    public AMF0OptimizedObjectReader() => _optimizedReaders = new();

    #region IAMFReader Members

    public object ReadData(AMFReader reader)
    {
        object instance = null;
        var typeIdentifier = reader.ReadString();
        var reflectionOptimizer = _optimizedReaders[typeIdentifier] as IReflectionOptimizer;
        if (reflectionOptimizer == null)
            lock (_optimizedReaders)
                if (!_optimizedReaders.Contains(typeIdentifier))
                {
                    //Temporary reader
                    _optimizedReaders[typeIdentifier] = new AMF0TempObjectReader();
                    var type = ObjectFactory.Locate(typeIdentifier);
                    if (type != null)
                    {
                        instance = ObjectFactory.CreateInstance(type);
                        reader.AddReference(instance);
                        if (type != null)
                        {
                            //Fixup
                            if (reflectionOptimizer != null)
                                _optimizedReaders[typeIdentifier] = reflectionOptimizer;
                            else
                                _optimizedReaders[typeIdentifier] = new AMF0TempObjectReader();
                        }
                    }
                    else
                    {
                        reflectionOptimizer = new AMF0TypedASObjectReader(typeIdentifier);
                        _optimizedReaders[typeIdentifier] = reflectionOptimizer;
                        instance = reflectionOptimizer.ReadData(reader, null);
                    }
                }
                else
                {
                    reflectionOptimizer = _optimizedReaders[typeIdentifier] as IReflectionOptimizer;
                    instance = reflectionOptimizer.ReadData(reader, null);
                }
        else
            instance = reflectionOptimizer.ReadData(reader, null);

        return instance;
    }

    #endregion
}

internal class AMF0TempObjectReader : IReflectionOptimizer
{
    #region IReflectionOptimizer Members

    public object CreateInstance() => default;

    public object ReadData(AMFReader reader, ClassDefinition classDefinition)
    {
        var amfObject = reader.ReadObject();
        return amfObject;
    }

    #endregion
}