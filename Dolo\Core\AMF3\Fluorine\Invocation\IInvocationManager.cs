﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Invocation;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal interface IInvocationManager
{
    /// <summary>
    ///     Gets a stack-based, user-defined storage area that is useful for communication between callback handlers.
    /// </summary>
    Stack<object> Context { get; }
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    Dictionary<object, object> Properties { get; }
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    object Result { get; set; }
}