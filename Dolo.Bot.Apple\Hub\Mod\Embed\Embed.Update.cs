﻿namespace Dolo.Bot.Apple.Hub.Mod.Embed;

public partial class Embed
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("update")]
    [Description("update an existing embed")]
    public async Task UpdateAsync(SlashCommandContext ctx,
     [Description("the type of the embed")] EmbedType embedType)
    {
        await ctx.Interaction.DeferAsync(true);

        var em = GetEmbedFromType(embedType);
        var ms = await GetMessageFromTypeAsync(embedType);

        await ms.TryModifyAsync(em);
        await ctx.TryEditResponseAsync($"{HubEmoji.Apple} Embed {embedType} updated");
    }
}