﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Birthday;

public partial class Birthday
{
    [Command("me")]

[Description("show your birthday")]
    public async Task MeAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(ctx.Channel == HubChannel.Birthday);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == ctx.User.Id);
        if (member is null)
            return;

        // member have to add the birthday 
        if (member.Birthday is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » Please add your birthday in <#{HubChannel.Birthday?.Id}>");
            return;
        }

        // print the birthday of the member
        await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » Your birthday is in `{member.Birthday.DayOfBirth.AddHours(2).GetTimeString()}`");
    }
}