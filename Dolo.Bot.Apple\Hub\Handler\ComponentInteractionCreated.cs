﻿using Dolo.Bot.Apple.Hub.Interaction;
using Dolo.Bot.Shard;
namespace Dolo.Bot.Apple.Hub.Handler;

public static class ComponentInteractionCreated
{
    public static async Task InvokeAsync(this ComponentInteractionCreatedEventArgs e)
    {
        if (e.Id == "question-pluto-verify")
            await e.HandleWelcomeAsync();

        if (e.Id is "select-settings"
            or "fake-notify"
            or "fake-notify-true"
            or "fake-notify-false"
            or "welcome-message"
            or "welcome-message-delete"
            or "welcome-message-delete-true"
            or "welcome-message-delete-false"
            or "welcome-message-true"
            or "welcome-message-false")
            await e.HandleSettingAsync();

        if (e.Id.StartsWith("event-"))
            await e.InvokeEventAsync(Hub.MemberSearchSystem, HubEmoji.IceCube!);

        if (e.Id == "create-invite")
            await e.InvokeCreateInviteAsync(HubCache.GetInvites(e.User.Id));
    }
}
