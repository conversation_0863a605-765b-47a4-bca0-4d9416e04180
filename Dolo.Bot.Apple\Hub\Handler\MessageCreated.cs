﻿using Dolo.Bot.Apple.Hub.Global.Interaction;
using Dolo.Core;
using Dolo.Database;
using Newtonsoft.Json;
namespace Dolo.Bot.Apple.Hub.Handler;

public static class MessageCreated
{
    public static async Task InvokeAsync(MessageArgs e)
    {
        try
        {
            if (e.Channel.IsPrivate
                || e.Message.Content is null
                || e.Guild.Id != Hub.Guild?.Id
                || e.Author.IsSystem.GetValueOrDefault())
                return;

            // add the message to the cache
            HubCache.TryAddOrUpdateMessage(e.Message.Id, e.Message);

            // check if the filter system is ok to process this message
            if (!await BadUrlFilter.IsOkAsync(e) || !await BadChannelFilter.IsOkAsync(e) || !await BadAttachmentFilter.IsOkAsync(e))
                return;

            // this will be run in background
            await BadWordFilter.DetectAsync(e);

            if (e.Message.Content.StartsWith("meow", StringComparison.CurrentCultureIgnoreCase))
                await e.MeowAsync();

            if (e.Message.Content.StartsWith("av ", StringComparison.CurrentCultureIgnoreCase) ||
                e.Message.Content.Equals("av", StringComparison.CurrentCultureIgnoreCase))
                await e.AvatarAsync();


            // add creating for happy birthday
            if (e.Message.Content.StartsWith("hbday", StringComparison.CurrentCultureIgnoreCase)
                || e.Message.Content.StartsWith("happy birthday", StringComparison.CurrentCultureIgnoreCase)
                || e.Message.Content.StartsWith("happy bday", StringComparison.CurrentCultureIgnoreCase))
            {
                await e.Message.TryCreateReactionAsync(HubEmoji.WhiteHeart);
                await e.Message.TryCreateReactionAsync(HubEmoji.WhiteFlyingHeart);
                await e.Message.TryCreateReactionAsync(HubEmoji.Tada);
            }

            // add creating for welcomes
            if (e.Message.Content.Equals("wel", StringComparison.CurrentCultureIgnoreCase)
                || e.Message.Content.StartsWith("welcome", StringComparison.CurrentCultureIgnoreCase))
            {
                await e.Message.TryCreateReactionAsync(HubEmoji.WhitePopHeart);
                await e.Message.TryCreateReactionAsync(HubEmoji.WhiteFlyingHeart);
            }

            // apple yes or no
            if (e.Message.Content.Equals("apple dice", StringComparison.CurrentCultureIgnoreCase))
                await e.Channel.TrySendMessageAsync(HubConstant.DiceNumbers.Shuffle().FirstOrDefault()?.ToString());
            else if (e.Message.Content.StartsWith("apple", StringComparison.CurrentCultureIgnoreCase))
            {
                var question = e.Message.Content[5..];
                if (question.Length > 5)
                    // random answer between yes and no
                    await e.Channel.TrySendMessageAsync($".{new[] { HubEmoji.Yes, HubEmoji.No }[RandomNumberGenerator.GetInt32(0, 2)]}");
            }

            // check if the channel has required data for the level system
            if (!Hub.LevelSystemChannel.Contains(e.Channel)
                || e.Message.Content.Split().Length < 2)
                return;


            // get the member from the database
            var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == e.Author.Id);
            var serverSettings = await Mongo.ServerSettings.GetFirstAsync();
            if (member is null)
                return;

            // assign one point to the user
            member.Level.Points += 1;

            // check if the user has reached the low point
            if (member.Level.Points >= serverSettings!.Level.Low && !e.Guild.Members[e.Author.Id].Roles.Contains(HubRoles.Low))
            {
                member.Level.Level = "Low";
                await e.Guild.Members[e.Author.Id].TryGrantRoleAsync(HubRoles.Low);
                await e.Channel.TrySendMessageAsync($"{HubEmoji.Pluto} » {e.Author.Mention} has reached the `low` level congratulation.");
            }

            // check if the user has reached the high point
            if (member.Level.Points >= serverSettings.Level.High && !e.Guild.Members[e.Author.Id].Roles.Contains(HubRoles.High))
            {
                member.Level.Level = "High";
                await e.Guild.Members[e.Author.Id].TryGrantRoleAsync(HubRoles.High);
                await e.Channel.TrySendMessageAsync($"{HubEmoji.Pluto} » {e.Author.Mention} has reached the `high` level congratulation.");
            }

            // check if the user has reached the epic point
            if (member.Level.Points >= serverSettings.Level.Epic && !e.Guild.Members[e.Author.Id].Roles.Contains(HubRoles.Epic))
            {
                member.Level.Level = "Epic";
                await e.Guild.Members[e.Author.Id].TryGrantRoleAsync(HubRoles.Epic);
                await e.Channel.TrySendMessageAsync($"{HubEmoji.Pluto} » {e.Author.Mention} has reached the `epic` level congratulation.");
            }

            // update the user in the mongodb collection with the filter of the user id 
            await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, e.Author.Id), Builders<ServerMember>.Update.Set(a => a.Level, member.Level));
        }
        catch (Exception c)
        {
            Console.WriteLine(c.ToJson(Formatting.Indented));
        }
    }
}