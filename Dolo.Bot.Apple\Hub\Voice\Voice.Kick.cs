﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Voice;

public partial class Voice
{
    [Command("kick")]
    [Description("kick a user out of the voice channel")]
    public async Task VoiceKickAsync(SlashCommandContext ctx, [Description("the user which should be kicked")] DiscordUser user)
    {
        if (Hub.Guild is null)
            return;

        // defer the message
        await ctx.Interaction.DeferAsync();

        // print error if the topic is not the voice system
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryEditResponseAsync("This command can only be used in voice channels.");
            return;
        }

        // check if the user is a member of the server
        if (!Hub.Guild.TryGetMember(user.Id, out var member))
        {
            await ctx.TryEditResponseAsync("The user is not a server member.");
            return;
        }

        // check if the user is in the voice channel
        if (member.VoiceState is null)
        {
            await ctx.TryEditResponseAsync("The user is not in a voice channel.");
            return;
        }

        // get the channel database entry
        var usr = await Mongo.Voice.GetOneAsync(a => a.TextChannel == ctx.Channel.Id);
        if (usr is null)
            return;

        // try to get the channel 
        var channel = Hub.Guild.TryGetChannel(usr.Channel);
        if (channel is null)
            return;

        // check if the user is a moderator
        if (usr.Moderator.Contains(user.Id))
        {
            await ctx.TryEditResponseAsync("The user is an moderator and cannot be kicked.");
            return;
        }

        // check if the user is the owner
        if (usr.Owner == user.Id || member.Roles.Contains(HubRoles.Admin))
        {
            await ctx.TryEditResponseAsync("The user is the owner and cannot be kicked.");
            return;
        }

        // check if the user is allowed to perform the command
        if (usr.Owner != ctx.User.Id && !usr.Moderator.Contains(ctx.User.Id) && !member.Roles.Contains(HubRoles.Admin))
        {
            await ctx.TryEditResponseAsync("You are not a channel moderator.");
            return;
        }

        // check if the user is a admin
        if (member.Roles.ContainsMany(HubRoles.Admin, HubRoles.Energy))
        {
            await ctx.TryEditResponseAsync("The user is an admin and cannot be kicked.");
            return;
        }

        // kick the user
        await member.TryModifyAsync(a => a.VoiceChannel = null);

        // send the message
        await ctx.TryEditResponseAsync($"{user.Mention} has been kicked from the voice channel.");
    }
}