﻿#pragma warning disable CS8604

namespace Dolo.Core.Discord;

public static class DiscordMessageExtension
{
    /// <summary>
    ///     Try to get reactions of a message
    /// </summary>
    /// <param name="msg">The message to get reactions from.</param>
    /// <param name="limit">The maximum number of reactions to get.</param>
    /// <param name="emoji">The emoji to filter reactions by.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<IReadOnlyList<DiscordUser>?> TryGetReactionsAsync(this DiscordMessage msg, int limit, DiscordEmoji? emoji, Action<Exception>? onError = null)
    {
        return await TryIt.TryValueTaskAsync(async () =>
        {
            var reactions = new List<DiscordUser>();
            await foreach (var user in msg.GetReactionsAsync(emoji))
            {
                reactions.Add(user);
                if (reactions.Count >= limit)
                    break;
            }
            return reactions.AsReadOnly();
        }, onError);
    }

    /// <summary>
    ///     Try to create a reaction to a message
    /// </summary>
    /// <param name="msg">The message to react to.</param>
    /// <param name="emoji">The emoji to react with.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryCreateReactionAsync(this DiscordMessage msg, DiscordEmoji? emoji, Action<Exception>? onError = null)
        => await msg.CreateReactionAsync(emoji).TryAsync(onError);

    /// <summary>
    ///     Try to get a message from a channel
    /// </summary>
    /// <param name="channel">The channel to get the message from.</param>
    /// <param name="id">The ID of the message to get.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TryGetMessageAsync(this DiscordChannel channel, ulong id, Action<Exception>? onError = null)
        => await channel.GetMessageAsync(id).TryAsync(onError);

    /// <summary>
    ///     Try to get a amount of messages from a channel
    /// </summary>
    /// <param name="channel">The channel to get messages from.</param>
    /// <param name="limit">The maximum number of messages to get.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<IEnumerable<DiscordMessage>?> TryGetMessagesAsync(this DiscordChannel channel, int limit, Action<Exception>? onError = null)
    {
        try
        {
            var msgs = new List<DiscordMessage>();
            await foreach (var msg in channel.GetMessagesAsync(limit))
                msgs.Add(msg);
            return msgs;
        }
        catch (Exception ex)
        {
            onError?.Invoke(ex);
            return null;
        }
    }

    /// <summary>
    ///     Try to bulk delete messages from a channel
    /// </summary>
    /// <param name="msg">The channel to delete messages from.</param>
    /// <param name="msgs">The messages to delete.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryDeleteMessageAsync(this DiscordChannel msg, List<DiscordMessage>? msgs, Action<Exception>? onError = null)
        => await msg.DeleteMessagesAsync(msgs).TryAsync(onError);

    /// <summary>
    ///     Try to delete a message from a channel
    /// </summary>
    /// <param name="msg">The message to delete.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryDeleteMessageAsync(this DiscordMessage msg, Action<Exception>? onError = null)
        => await msg.DeleteAsync().TryAsync(onError);

    /// <summary>
    ///     Try to respond to a discord message
    /// </summary>
    /// <param name="msg">The message to respond to.</param>
    /// <param name="content">The content of the response.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TryRespondAsync(this DiscordMessage msg, string content, Action<Exception>? onError = null)
        => await msg.RespondAsync(content).TryAsync(onError);

    /// <summary>
    ///     Try to respond to a discord message
    /// </summary>
    /// <param name="msg">The message to respond to.</param>
    /// <param name="embed">The embed to send.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TryRespondAsync(this DiscordMessage msg, DiscordEmbed embed, Action<Exception>? onError = null)
        => await msg.RespondAsync(embed).TryAsync(onError);

    /// <summary>
    ///     Try to respond to a discord message
    /// </summary>
    /// <param name="msg">The message to respond to.</param>
    /// <param name="content">The content of the response.</param>
    /// <param name="embed">The embed to send.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TryRespondAsync(this DiscordMessage msg, string content, DiscordEmbed embed, Action<Exception>? onError = null)
        => await msg.RespondAsync(content, embed).TryAsync(onError);

    /// <summary>
    ///     Try to respond to a discord message
    /// </summary>
    /// <param name="msg">The message to respond to.</param>
    /// <param name="builder">The message builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TryRespondAsync(this DiscordMessage msg, DiscordMessageBuilder builder, Action<Exception>? onError = null)
        => await msg.RespondAsync(builder).TryAsync(onError);

    /// <summary>
    ///     Try to respond to a discord message
    /// </summary>
    /// <param name="msg">The message to respond to.</param>
    /// <param name="builder">The action to configure message builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TryRespondAsync(this DiscordMessage msg, Action<DiscordMessageBuilder> builder, Action<Exception>? onError = null)
        => await msg.RespondAsync(builder).TryAsync(onError);

    /// <summary>
    ///     Try to respond to a discord message
    /// </summary>
    /// <param name="msg">The message to respond to.</param>
    /// <param name="content">The content of the response.</param>
    /// <param name="builder">The embed builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TryRespondAsync(this DiscordMessage msg, string content, DiscordEmbedBuilder builder, Action<Exception>? onError = null)
        => await msg.RespondAsync(content, builder).TryAsync(onError);

    /// <summary>
    ///     Try to send a message
    /// </summary>
    /// <param name="channel">The channel to send the message to.</param>
    /// <param name="content">The content of the message.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordChannel channel, string? content, Action<Exception>? onError = null)
        => await channel.SendMessageAsync(content).TryAsync(onError);

    /// <summary>
    ///     Try to send a message
    /// </summary>
    /// <param name="channel">The channel to send the message to.</param>
    /// <param name="embed">The embed to send.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordChannel channel, DiscordEmbed embed, Action<Exception>? onError = null)
        => await channel.SendMessageAsync(embed).TryAsync(onError ?? (a => Console.WriteLine(a.ToJson())));

    /// <summary>
    ///     Try to send a message
    /// </summary>
    /// <param name="channel">The channel to send the message to.</param>
    /// <param name="content">The content of the message.</param>
    /// <param name="embed">The embed to send.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordChannel channel, string content, DiscordEmbed embed, Action<Exception>? onError = null)
        => await channel.SendMessageAsync(content, embed).TryAsync(onError);

    /// <summary>
    ///     Try to send a message
    /// </summary>
    /// <param name="channel">The channel to send the message to.</param>
    /// <param name="builder">The message builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordChannel channel, DiscordMessageBuilder builder, Action<Exception>? onError = null)
        => await channel.SendMessageAsync(builder).TryAsync(onError);

    /// <summary>
    ///     Try to send a message
    /// </summary>
    /// <param name="channel">The channel to send the message to.</param>
    /// <param name="builder">The action to configure message builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task<DiscordMessage?> TrySendMessageAsync(this DiscordChannel channel, Action<DiscordMessageBuilder> builder, Action<Exception>? onError = null)
        => await channel.SendMessageAsync(builder).TryAsync(onError);

    /// <summary>
    ///     Try to modify a message
    /// </summary>
    /// <param name="msg">The message to modify.</param>
    /// <param name="builder">The message builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryModifyAsync(this DiscordMessage msg, DiscordMessageBuilder builder, Action<Exception>? onError = null)
        => await msg.ModifyAsync(builder).TryAsync(onError);

    /// <summary>
    ///     Try to modify a message
    /// </summary>
    /// <param name="msg">The message to modify.</param>
    /// <param name="builder">The action to configure message builder.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryModifyAsync(this DiscordMessage msg, Action<DiscordMessageBuilder> builder, Action<Exception>? onError = null)
        => await msg.ModifyAsync(builder).TryAsync(onError);

    /// <summary>
    ///     Delete a message after a specified time span
    /// </summary>
    /// <param name="msg">The message to delete.</param>
    /// <param name="span">The time to wait before deletion.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryDeleteAfterAsync(this DiscordMessage msg, TimeSpan span, Action<Exception>? onError = null)
    {
        await Task.Delay(span);
        await msg.TryDeleteMessageAsync(onError);
    }

    /// <summary>
    ///     Try to delete a specific message
    /// </summary>
    /// <param name="channel">The channel containing the message.</param>
    /// <param name="id">The ID of the message to delete.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryDeleteMessageAsync(this DiscordChannel channel, ulong id, Action<Exception>? onError = null)
    {
        var msg = await channel.TryGetMessageAsync(id, onError);
        if (msg is not null)
            await msg.TryDeleteMessageAsync(onError);
    }

    /// <summary>
    ///     Try to delete a specific message after a certain amount of time
    /// </summary>
    /// <param name="msg">The task that returns the message to delete.</param>
    /// <param name="span">The time to wait before deletion.</param>
    /// <param name="callback">Callback to execute after deletion.</param>
    /// <param name="onError">Optional action to handle exceptions.</param>
    public static async Task TryDeleteAfterAsync(this Task<DiscordMessage?> msg, TimeSpan span, Action? callback = null, Action<Exception>? onError = null)
    {
        var message = await msg;
        if (message is not null)
        {
            await Task.Delay(span);
            await message.TryDeleteMessageAsync(onError);
            callback?.Invoke();
        }
    }
}