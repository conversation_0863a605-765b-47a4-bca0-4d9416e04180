﻿namespace Dolo.Core;

public static class Desktop
{
    /// <summary>
    ///     Gets all files
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpEverything(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories)
    {
        var files = new List<FileInfo>();
        files.AddRange(DumpDesktopFiles(searchPattern, searchOption));
        files.AddRange(DumpDocumentsFiles(searchPattern, searchOption));
        files.AddRange(DumpDownloadsFiles(searchPattern, searchOption));
        files.AddRange(DumpMusicFiles(searchPattern, searchOption));
        files.AddRange(DumpPicturesFiles(searchPattern, searchOption));
        files.AddRange(DumpVideosFiles(searchPattern, searchOption));
        files.AddRange(DumpComputerFiles(searchPattern, searchOption));
        return files;
    }

    /// <summary>
    ///     Gets all files in desktop folder
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpDesktopFiles(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => GetFiles(Environment.SpecialFolder.Desktop, searchPattern, searchOption);

    /// <summary>
    ///     Gets all files in desktop folder
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpPicturesFiles(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => GetFiles(Environment.SpecialFolder.MyPictures, searchPattern, searchOption);

    /// <summary>
    ///     Gets all files in documents folder (My Documents)
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpDocumentsFiles(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => GetFiles(Environment.SpecialFolder.MyDocuments, searchPattern, searchOption);

    /// <summary>
    ///     Gets all files in downloads folder (My Downloads)
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpDownloadsFiles(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => GetFiles(Environment.SpecialFolder.MyDocuments, searchPattern, searchOption);

    /// <summary>
    ///     Gets all files in music folder (My Music)
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpMusicFiles(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => GetFiles(Environment.SpecialFolder.MyMusic, searchPattern, searchOption);

    /// <summary>
    ///     Gets all files in videos folder (My Videos)
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpVideosFiles(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => GetFiles(Environment.SpecialFolder.MyVideos, searchPattern, searchOption);

    /// <summary>
    ///     Gets all files in the computer (My Computer)
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> DumpComputerFiles(string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => GetFiles(Environment.SpecialFolder.MyComputer, searchPattern, searchOption);


    /// <summary>
    ///     Gets all files in a special folder
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> GetFiles(Environment.SpecialFolder folder, string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => TryIt.This(() => {
        var files = new List<FileInfo>();
        var directory = new DirectoryInfo(Environment.GetFolderPath(folder));
        files.AddRange(directory.GetFiles(searchPattern, searchOption));
        return files;
    }) ?? new List<FileInfo>();

    /// <summary>
    ///     Gets all directories in a special folder
    /// </summary>
    /// <returns></returns>
    public static List<DirectoryInfo> GetDirectories(Environment.SpecialFolder folder, string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => TryIt.This(() => {
        var directories = new List<DirectoryInfo>();
        var directory = new DirectoryInfo(Environment.GetFolderPath(folder));
        directories.AddRange(directory.GetDirectories(searchPattern, searchOption));
        return directories;
    }) ?? new List<DirectoryInfo>();

    /// <summary>
    ///     Gets all files in a path
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> GetFiles(string path, string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => TryIt.This(() => {
        var files = new List<FileInfo>();
        var directory = new DirectoryInfo(path);
        files.AddRange(directory.GetFiles(searchPattern, searchOption));
        return files;
    }) ?? new List<FileInfo>();

    /// <summary>
    ///     Gets all directories in a path
    /// </summary>
    /// <returns></returns>
    public static List<DirectoryInfo> GetDirectories(string path, string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => TryIt.This(() => {
        var directories = new List<DirectoryInfo>();
        var directory = new DirectoryInfo(path);
        directories.AddRange(directory.GetDirectories(searchPattern, searchOption));
        return directories;
    }) ?? new List<DirectoryInfo>();

    /// <summary>
    ///     Gets all files in a directory
    /// </summary>
    /// <returns></returns>
    public static List<FileInfo> GetFiles(DirectoryInfo directory, string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => TryIt.This(() => {
        var files = new List<FileInfo>();
        files.AddRange(directory.GetFiles(searchPattern, searchOption));
        return files;
    }) ?? new List<FileInfo>();

    /// <summary>
    ///     Gets all directories in a directory
    /// </summary>
    /// <returns></returns>
    public static List<DirectoryInfo> GetDirectories(DirectoryInfo directory, string searchPattern = "*.*", SearchOption searchOption = SearchOption.AllDirectories) => TryIt.This(() => {
        var directories = new List<DirectoryInfo>();
        directories.AddRange(directory.GetDirectories(searchPattern, searchOption));
        return directories;
    }) ?? new List<DirectoryInfo>();
}