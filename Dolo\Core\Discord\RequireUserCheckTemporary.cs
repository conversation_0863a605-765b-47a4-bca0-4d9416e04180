using DSharpPlus.Commands.ContextChecks;
using DSharpPlus.Commands.Processors.SlashCommands;

public sealed class RequireUserCheckTemporary : IContextCheck<RequirePermissionsAttribute>
{
    public ValueTask<string?> ExecuteCheckAsync(RequirePermissionsAttribute attribute, CommandContext context)
    {
        DiscordPermissions requiredBotPermissions = attribute.BotPermissions;
        DiscordPermissions requiredUserPermissions = attribute.UserPermissions;

        if (context is SlashCommandContext slashContext)
        {
            if (!slashContext.Interaction.AppPermissions.HasAllPermissions(requiredBotPermissions))
            {
                return ValueTask.FromResult<string?>("The bot did not have the needed permissions to execute this command.");
            }
            else if (!slashContext.Member!.PermissionsIn(context.Channel).HasAllPermissions(requiredUserPermissions))
            {
                return ValueTask.FromResult<string?>("The executing user did not have the needed permissions to execute this command.");
            }

            return ValueTask.FromResult<string?>(null);
        }
        else if (context.Guild is null)
        {
            return ValueTask.FromResult<string?>("Guild is null.");
        }
        else if (!context.Guild!.CurrentMember.PermissionsIn(context.Channel).HasAllPermissions(requiredBotPermissions))
        {
            return ValueTask.FromResult<string?>("The bot did not have the needed permissions to execute this command.");
        }
        else if (!context.Member!.PermissionsIn(context.Channel).HasAllPermissions(requiredUserPermissions))
        {
            return ValueTask.FromResult<string?>("The executing user did not have the needed permissions to execute this command.");
        }

        return ValueTask.FromResult<string?>(null);
    }
}