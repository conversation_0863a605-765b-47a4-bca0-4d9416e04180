﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Contains the properties for configuring the destination network element.
///     This is the <b>network</b> element in the services-config.xml file.
/// </summary>
internal sealed class NetworkSettings : Hashtable
{
    private NetworkSettings()
    {}

    internal NetworkSettings(XmlNode networkDefinitionNode)
    {
        foreach (XmlNode propertyNode in networkDefinitionNode.SelectNodes("*"))
            if (propertyNode.InnerXml != null && propertyNode.InnerXml != string.Empty)
                this[propertyNode.Name] = propertyNode.InnerXml;
            else
            {
                if (propertyNode.Attributes != null)
                    foreach (XmlAttribute attribute in propertyNode.Attributes)
                        this[propertyNode.Name + "_" + attribute.Name] = attribute.Value;
            }
    }

    /// <summary>
    ///     Gets whether data paging is enabled for the destination.
    /// </summary>
    public bool PagingEnabled
    {
        get
        {
            if (ContainsKey("paging_enabled"))
                return System.Convert.ToBoolean(this["paging_enabled"]);
            return false;
        }
    }

    /// <summary>
    ///     Gets the paging size. When paging is enabled, this indicates the number of records to be sent to the client when
    ///     the client-side DataService.fill() method is called.
    /// </summary>
    public int PagingSize
    {
        get
        {
            if (ContainsKey("paging_pageSize"))
                return System.Convert.ToInt32(this["paging_pageSize"]);
            return 0;
        }
    }

    /// <summary>
    ///     Gets the idle time in minutes before a subscriber is unsubscribed.
    ///     The value to 0 (zero) means subscribers are not forced to unsubscribe automatically.
    /// </summary>
    /// <remarks>The default value is 20.</remarks>
    public int SessionTimeout
    {
        get
        {
            if (ContainsKey("session-timeout"))
                return System.Convert.ToInt32(this["session-timeout"]);
            return 20;
        }
    }
}