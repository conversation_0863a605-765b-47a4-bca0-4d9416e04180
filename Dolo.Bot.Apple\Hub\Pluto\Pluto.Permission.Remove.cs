﻿using Dolo.Database;
using Dolo.Pluto.Shard.License.Feature;
namespace Dolo.Bot.Apple.Hub.Pluto;

public partial class Pluto
{
    public partial class Permission
    {
        [RequirePermissions(DiscordPermission.BanMembers)]
        [Command("remove")]
        [Description("remove a permission from a user")]
        public async Task Removesync(SlashCommandContext ctx, [Description("the user that should get the permission")] DiscordUser user, [Description("the permission")] LicensePermission permission)
        {
            await ctx.Interaction.DeferAsync(true);

            var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);
            if (member?.License is null)
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User `{user.Username}` is not licensed");
                return;
            }

            if (!member.License.HasPermission(permission))
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User `{user.Username}` has not the permission `{permission}`");
                return;
            }

            var builder = Builders<ServerMember>.Update
                .Pull(a => a.License!.Permissions, permission);

            if (member.License.HasFeature<FeatureUpload>() && permission == LicensePermission.Farming)
                builder = builder.Pull(a => a.License!.Features, member.License.GetFeature<FeatureUpload>());

            if (member.License.HasFeature<FeatureFamePurchased>() && permission == LicensePermission.Fame)
                builder = builder.Pull(a => a.License!.Features, member.License.GetFeature<FeatureFamePurchased>());

            if (member.License.HasFeature<FeatureAutographPurchased>() && permission == LicensePermission.Autograph)
                builder = builder.Pull(a => a.License!.Features, member.License.GetFeature<FeatureAutographPurchased>());

            // update the database
            await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, user.Id), builder);

            // send response ephemeral
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User `{user.Username}` has been removed the permission `{permission}`");
        }
    }
}