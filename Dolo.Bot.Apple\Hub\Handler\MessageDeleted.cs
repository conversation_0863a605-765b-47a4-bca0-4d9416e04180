﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Handler;

public static class MessageDeleted
{
    public static async Task InvokeAsync(this MessageDeletedEventArgs e)
    {
        if ((e.Channel?.IsPrivate).GetValueOrDefault()
            || e.Message?.Content is null
            || e.Guild?.Id     != Hub.Guild?.Id
            || e.Message.Flags == DiscordMessageFlags.Ephemeral)
            return;

        // try to get the message from the cache
        if (!HubCache.TryGetMessage(e.Message.Id, out var message) || message is null)
            return;

        // try to remove the message out of the cache
        HubCache.TryRemoveMessage(e.Message.Id);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == message.Author.Id);
        if (member is null)
            return;

        // remove a point if the points aren't 0
        if (member.Level.Points != 0)
            member.Level.Points -= 1;

        // update the member in the database
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, message.Author.Id), Builders<ServerMember>.Update.Set(a => a.Level.Points, member.Level.Points));
    }
}