﻿using System.Text;
namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [Command("invites")]

[Description("shows how many invites you made")]
    public async Task InvitesAsync(SlashCommandContext ctx, [Description("to show the invites of a user (optional)")] DiscordUser? user = null)
    {
        await ctx.Interaction.DeferAsync();

        if (Hub.MemberSearchSystem.Members.Count == 0)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.GhostLove} Invites are being indexed at the moment, please try again later.");
            return;
        }

        // get the member from the database
        var memberId = user == null ? ctx.Member!.Id : user.Id;
        var inviteCount = Hub.MemberSearchSystem.GetInviteCount(memberId);

        await ctx.TryEditResponseAsync(new DiscordWebhookBuilder()
           .WithContent($"{HubEmoji.GhostLove} <@{ctx.User.Id}> » {new StringBuilder()
               .Append(user.IfNullThen(
               $"You have {inviteCount:N0} {"invite".Pluralize(inviteCount)}",
               $"**{user?.Username}** has `{inviteCount:N0}` {"invite".Pluralize(inviteCount)}"))}")
           .AddActionRowComponent(new DiscordButtonComponent(DiscordButtonStyle.Primary, $"create-invite", "Create Invite", false, new(HubEmoji.Astro!))));
    }
}