﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Blacklist
    {
        [Command("banGuild")]
        [Description("add a guild to blacklist")]
        public async Task AddAsync(SlashCommandContext ctx,
            [Description("the guild that should be blacklisted")] string guild)
        {
            if (ctx.User.Id != 440584675740876810) return;


            await ctx.TryDeferAsync(true);
            await ctx.TryEditResponseAsync("Trying to blacklist guild ..");
            var config = await Mongo.PixiSettings.GetFirstAsync();
            if (config is null) return;

            if (!ulong.TryParse(guild, out var val))
            {
                await ctx.TryEditResponseAsync("Guild is not valid");
                return;
            }

            if (config.HasBlockGuild(val))
            {
                await ctx.TryEditResponseAsync("Guild is already blacklisted");
                return;
            }

            config.BlockGuild(val);

            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(x => x.Guilds, config.Guilds));
            await ctx.TryEditResponseAsync($"Guild `{val}` has been blacklisted");
        }
    }
}