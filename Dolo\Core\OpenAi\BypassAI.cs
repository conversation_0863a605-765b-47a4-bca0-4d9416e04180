﻿using Dolo.Core.Http;
namespace Dolo.Core.OpenAi;

public static class BypassAI
{
    /// <summary>
    ///     Bypass openAI limitation and use unlimited
    ///     usages of the AI
    /// </summary>
    /// <returns></returns>
    public static async Task<Stream?> PromptAsync(Action<Prompt> prompt)
    {
        var pro = prompt.GetAction();
        var request = await Http.Http.TrySendAsync(a => {
            a.Url = "https://api.app.prod.grazie.aws.intellij.net/user/v5/llm/chat/stream/v3";
            a.Method = HttpMethod.Post;
            a.ContentType = HttpContentType.ApplicationJson;
            a.AddHeader("Accept-Charset", "UTF-8")
                .AddHeader("grazie-agent", "{\"name\":\"Rider\",\"version\":\"2023.3 EAP 8\"}")
                .AddHeader("grazie-authenticate-jwt", pro.Token!)
                .AddHeader("grazie-original-user-jwt", pro.Token!)
                .AddHeader("User-Agent", "Ktor client");
            a.Content = new StringContent(new BypassAIMessages
            {
                Profile = "openai-gpt-4",
                Chat = new()
                {
                    Messages = pro.Messages
                }
            }.ToJson()!);
        });

        return request.IsSuccess ? await request.TryGetStreamAsync() : null;
    }


    /// <summary>
    ///     Bypass openAI limitation and use unlimited
    ///     usages of the AI
    /// </summary>
    /// <returns></returns>
    public static async Task<Stream?> PromptAsync(string prompt) => await PromptAsync(a => a.AddMessage(prompt));
}