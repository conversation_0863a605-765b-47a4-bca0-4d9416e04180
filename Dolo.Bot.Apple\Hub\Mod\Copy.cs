﻿namespace Dolo.Bot.Apple.Hub.Mod;

public class Copy
{
    [Command("copy")]
    [Description("copy a sticker to your server")]
    [RequirePermissions(DiscordPermission.Administrator)]
    public async Task CopyAsync(SlashCommandContext ctx, [Description("the sticker to copy")] string message)
    {
        await ctx.Interaction.DeferAsync(true);


        if (!ulong.TryParse(message, out var id))
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Invalid message id");
            return;
        }

        var msg = await ctx.Channel.TryGetMessageAsync(id);
        if (msg is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Message not found");
            return;
        }

        if (msg.Stickers.Count is 0)
            return;

        var sticker = msg.Stickers[0];
        var stream = await Http.TryGetStreamAsync(sticker.StickerUrl);
        if (!stream.IsSuccess)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Sticker not found");
            return;
        }
        await ctx.Guild.CreateStickerAsync(sticker.Name, sticker.Description, "faceParty", stream.Result, sticker.FormatType);
        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Sticker copied");
    }
}