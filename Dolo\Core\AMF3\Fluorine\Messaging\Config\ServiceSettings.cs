﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Services.Remoting;
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Contains the properties for configuring services.
///     This is the <b>service</b> element in the services-config.xml file.
/// </summary>
internal sealed class ServiceSettings
{
    private readonly object _objLock = new();


    internal ServiceSettings(ServiceConfigSettings serviceConfigSettings)
    {
        ServiceConfigSettings = serviceConfigSettings;
        SupportedMessageTypes = new(1);
        DestinationSettings = [];
        AdapterSettings = [];
    }

    internal ServiceSettings(ServiceConfigSettings serviceConfigSettings, string id, string @class)
    {
        ServiceConfigSettings = serviceConfigSettings;
        SupportedMessageTypes = new(1);
        DestinationSettings = [];
        AdapterSettings = [];
        Id = id;
        Class = @class;
    }

    /// <summary>
    ///     Gets the service identity.
    /// </summary>
    public string Id { get; private set; }

    /// <summary>
    ///     Gets the service type.
    /// </summary>
    public string Class { get; private set; }

    /// <summary>
    ///     Gets a dictionary of supported message types.
    /// </summary>
    public Hashtable SupportedMessageTypes { get; }

    /// <summary>
    ///     Gets the collection of destination settings.
    /// </summary>
    public DestinationSettingsCollection DestinationSettings { get; }

    /// <summary>
    ///     Gets the collection of adapter settings.
    /// </summary>
    public AdapterSettingsCollection AdapterSettings { get; }

    /// <summary>
    ///     Gets or sets the default adapter.
    /// </summary>
    public AdapterSettings DefaultAdapter { get; set; }

    /// <summary>
    ///     Gets the ServiceConfigSettings reference.
    /// </summary>
    public ServiceConfigSettings ServiceConfigSettings { get; }

    internal void Init(string configPath)
    {
        var servicesXml = new XmlDocument();
        servicesXml.Load(configPath);
        var root = servicesXml.DocumentElement;
        Init(root);
    }

    internal void Init(XmlNode serviceElement)
    {
        Id = serviceElement.Attributes["id"].Value;
        Class = serviceElement.Attributes["class"].Value;
        var messageTypes = serviceElement.Attributes["messageTypes"].Value;
        var messageTypesList = messageTypes.Split([',']);
        foreach (var messageType in messageTypesList)
        {
            var type = AMFConfiguration.Instance.ClassMappings.GetType(messageType);
            SupportedMessageTypes[messageType] = type;
        }

        //Read adapters
        var adaptersNode = serviceElement.SelectSingleNode("adapters");
        if (adaptersNode != null)
            foreach (XmlNode adapterNode in adaptersNode.SelectNodes("*"))
            {
                var adapterSettings = new AdapterSettings(adapterNode);
                AdapterSettings.Add(adapterSettings);
                if (adapterSettings.Default)
                    DefaultAdapter = adapterSettings;
            }
        else
        {
            var adapterSettings = new AdapterSettings("dotnet", typeof(RemotingAdapter).FullName, true);
            DefaultAdapter = adapterSettings;
            AdapterSettings.Add(adapterSettings);
        }

        //Read destinations
        var destinationNodeList = serviceElement.SelectNodes("destination");
        foreach (XmlNode destinationNode in destinationNodeList)
        {
            var destinationSettings = new DestinationSettings(this, destinationNode);
            DestinationSettings.Add(destinationSettings);
        }
    }

    internal DestinationSettings CreateDestinationSettings(string id, string source)
    {
        lock (_objLock)
            if (!DestinationSettings.ContainsKey(id))
            {
                var adapterSettings = new AdapterSettings("dotnet", typeof(RemotingAdapter).FullName, false);
                var destinationSettings = new DestinationSettings(this, id, adapterSettings, source);
                DestinationSettings.Add(destinationSettings);
                return destinationSettings;
            }
            else
                return DestinationSettings[id];
    }
}

/// <summary>
///     Strongly typed ServiceSettings collection.
/// </summary>
internal sealed class ServiceSettingsCollection : CollectionBase
{
    private readonly Hashtable _serviceDictionary;

    /// <summary>
    ///     Initializes a new instance of the ServiceSettingsCollection class.
    /// </summary>
    public ServiceSettingsCollection() => _serviceDictionary = new();

    /// <summary>
    ///     Gets or sets the ServiceSettings element at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index of the element to get or set.</param>
    /// <returns>The element at the specified index.</returns>
    public ServiceSettings this[int index]
    {
        get => List[index] as ServiceSettings;
        set => List[index] = value;
    }

    /// <summary>
    ///     Gets or sets the ServiceSettings element with the specified key.
    /// </summary>
    /// <param name="key">The id of the ServiceSettings element to get or set.</param>
    /// <returns>The element with the specified key.</returns>
    public ServiceSettings this[string key]
    {
        get => _serviceDictionary[key] as ServiceSettings;
        set => _serviceDictionary[key] = value;
    }

    /// <summary>
    ///     Adds a ServiceSettings to the collection.
    /// </summary>
    /// <param name="value">The ServiceSettings to add to the collection.</param>
    /// <returns>The position into which the new element was inserted.</returns>
    public int Add(ServiceSettings value)
    {
        _serviceDictionary[value.Id] = value;
        return List.Add(value);
    }

    /// <summary>
    ///     Determines the index of a specific item in the collection.
    /// </summary>
    /// <param name="value">The ServiceSettings to locate in the collection.</param>
    /// <returns>The index of value if found in the collection; otherwise, -1.</returns>
    public int IndexOf(ServiceSettings value) => List.IndexOf(value);

    /// <summary>
    ///     Inserts a ServiceSettings item to the collection at the specified index.
    /// </summary>
    /// <param name="index">The zero-based index at which value should be inserted.</param>
    /// <param name="value">The ServiceSettings to insert into the collection.</param>
    public void Insert(int index, ServiceSettings value)
    {
        _serviceDictionary[value.Id] = value;
        List.Insert(index, value);
    }

    /// <summary>
    ///     Removes the first occurrence of a specific ServiceSettings from the collection.
    /// </summary>
    /// <param name="value">The ServiceSettings to remove from the collection.</param>
    public void Remove(ServiceSettings value)
    {
        _serviceDictionary.Remove(value.Id);
        List.Remove(value);
    }

    /// <summary>
    ///     Determines whether the collection contains a specific ServiceSettings value.
    /// </summary>
    /// <param name="value">The ServiceSettings to locate in the collection.</param>
    /// <returns>true if the ServiceSettings is found in the collection; otherwise, false.</returns>
    public bool Contains(ServiceSettings value) => List.Contains(value);
}