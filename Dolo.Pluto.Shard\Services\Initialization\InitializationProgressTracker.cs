namespace Dolo.Pluto.Shard.Services.Initialization;

public class InitializationProgressTracker : IInitializationProgressTracker
{
    public int ProgressPercentage { get; private set; } = 15;
    
    public event Action<string, int>? OnProgressUpdated;

    public void UpdateProgress(string detail, int percentage)
    {
        ProgressPercentage = percentage;
        OnProgressUpdated?.Invoke(detail, percentage);
    }

    public void CompleteProgress()
    {
        UpdateProgress("Initialization complete", 100);
    }
}
