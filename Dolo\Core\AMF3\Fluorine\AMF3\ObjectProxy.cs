﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.AMF3;

/// <summary>
///     Flex ObjectProxy class.
/// </summary>
internal class ObjectProxy : Dictionary<string, object>
{
    #region IExternalizable Members

    /// <summary>
    ///     Decode the ObjectProxy from a data stream.
    /// </summary>
    /// <param name="input">IDataInput interface.</param>
    public void ReadExternal(IDataInput input)
    {
        var value = input.ReadObject();
        if (value is IDictionary)
        {
            var dictionary = value as IDictionary;
            foreach (DictionaryEntry entry in dictionary) Add(entry.Key as string, entry.Value);
        }
    }

    /// <summary>
    ///     Encode the ObjectProxy for a data stream.
    /// </summary>
    /// <param name="output">IDataOutput interface.</param>
    public void WriteExternal(IDataOutput output)
    {
        var asObject = new ASObject();
        foreach (var entry in this) asObject.Add(entry.Key, entry.Value);
        output.WriteObject(asObject);
    }

    #endregion
}