﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF0;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF0ObjectWriter : IAMFWriter
{
    #region IAMFWriter Members

    public bool IsPrimitive => false;

    public void WriteData(AMFWriter writer, object data)
    {
        if (data is IList)
        {
            var list = data as IList;
            var array = new object[list.Count];
            list.CopyTo(array, 0);
            writer.WriteArray(ObjectEncoding.AMF0, array);
            return;
        }
        if (data is IDictionary)
        {
            writer.WriteAssociativeArray(ObjectEncoding.AMF0, data as IDictionary);
            return;
        }
        if (data is Exception)
        {
            writer.WriteASO(ObjectEncoding.AMF0, new ExceptionASO(data as Exception));
            return;
        }
        writer.WriteObject(ObjectEncoding.AMF0, data);
    }

    #endregion
}