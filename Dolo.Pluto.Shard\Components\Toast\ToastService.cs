namespace Dolo.Pluto.Shard.Components.Toast;

public class ToastService
{
    private readonly List<ToastModel> _toasts = [];
    
    public event Action? OnToastsChanged;

    public IReadOnlyList<ToastModel> Toasts => _toasts.AsReadOnly();

    public void ShowSuccess(string message, string? title = null, int autoCloseDelay = 5000)
    {
        ShowToast(message, title, ToastType.Success, autoCloseDelay);
    }

    public void ShowError(string message, string? title = null, int autoCloseDelay = 8000)
    {
        ShowToast(message, title, ToastType.Error, autoCloseDelay);
    }

    public void ShowWarning(string message, string? title = null, int autoCloseDelay = 6000)
    {
        ShowToast(message, title, ToastType.Warning, autoCloseDelay);
    }

    public void ShowInfo(string message, string? title = null, int autoCloseDelay = 5000)
    {
        ShowToast(message, title, ToastType.Info, autoCloseDelay);
    }

    private void ShowToast(string message, string? title, ToastType type, int autoCloseDelay)
    {
        var toast = new ToastModel
        {
            Id = Guid.NewGuid(),
            Message = message,
            Title = title ?? "",
            Type = type,
            AutoCloseDelay = autoCloseDelay,
            IsVisible = true
        };

        _toasts.Add(toast);
        
        // Dispatch to UI thread for MAUI compatibility
        if (Microsoft.Maui.Controls.Application.Current?.Dispatcher != null)
        {
            Microsoft.Maui.Controls.Application.Current.Dispatcher.Dispatch(() => OnToastsChanged?.Invoke());
        }
        else
        {
            OnToastsChanged?.Invoke();
        }
    }

    public void RemoveToast(Guid id)
    {
        var toast = _toasts.FirstOrDefault(t => t.Id == id);
        if (toast != null)
        {
            _toasts.Remove(toast);
            OnToastsChanged?.Invoke();
        }
    }

    public void ClearAll()
    {
        _toasts.Clear();
        OnToastsChanged?.Invoke();
    }
}

public class ToastModel
{
    public Guid Id { get; set; }
    public string Message { get; set; } = "";
    public string Title { get; set; } = "";
    public ToastType Type { get; set; }
    public bool IsVisible { get; set; }
    public int AutoCloseDelay { get; set; }
}

public enum ToastType
{
    Success,
    Error,
    Warning,
    Info
}
