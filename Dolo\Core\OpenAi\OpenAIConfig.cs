﻿// ReSharper disable InconsistentNaming
namespace Dolo.Core.OpenAi;

public class OpenAIConfig
{
    public string? ApiKey { get; private set; }
    public string? Organization { get; private set; }

    /// <summary>
    ///     Set the API key for the OpenAI API
    /// </summary>
    public OpenAIConfig SetAPIKey(string apiKey)
    {
        ApiKey = apiKey;
        return this;
    }

    /// <summary>
    ///     Set the organization for the API key
    /// </summary>
    public OpenAIConfig SetOrganization(string organization)
    {
        Organization = organization;
        return this;
    }
}