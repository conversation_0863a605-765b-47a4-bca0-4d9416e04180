﻿using System.Globalization;
using System.Net.Http;

namespace Dolo.Core.Http;

public static partial class Http
{
    /// <summary>
    /// Attempts to download content from a specified URL and store it in memory.
    /// </summary>
    /// <param name="config">Configuration for the HTTP download including URL and proxy settings.</param>
    /// <param name="progress">Optional callback for progress updates during download.
    /// Returns tuple containing (bytesReceived, totalBytes, percentage as string).</param>
    /// <returns>HttpDownload object containing the downloaded content and status information.</returns>
    /// <remarks>
    /// - Progress updates are throttled to avoid excessive callbacks
    /// - Uses a buffer size of 8KB for streaming
    /// - Returns content in a MemoryStream positioned at the beginning
    /// </remarks>
    public static async Task<HttpDownload> TryDownloadAsync(
        Action<HttpDownloadConfig> config,
        Action<(long bytesReceived, long totalBytes, string? percentage)>? progress = null)
    {
        ArgumentNullException.ThrowIfNull(config);
        var download = new HttpDownload();
        
        try
        {
            var cfg = config.GetAction();
            using var client = CreateHttpClient(cfg.Proxy);
            using var request = new HttpRequestMessage(HttpMethod.Get, cfg.Url);
            using var response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
            
            response.EnsureSuccessStatusCode();
            var contentLength = response.Content.Headers.ContentLength ?? -1;
            var lastReportedProgress = -1L;
            
            await using var contentStream = await response.Content.ReadAsStreamAsync();
            download.Stream = new MemoryStream();
            
            var buffer = new byte[8192];
            var totalBytesRead = 0L;
            
            while (true)
            {
                var bytesRead = await contentStream.ReadAsync(buffer);
                if (bytesRead == 0) break;
                
                await download.Stream.WriteAsync(buffer.AsMemory(0, bytesRead));
                totalBytesRead += bytesRead;
                
                if (progress != null && contentLength > 0)
                {
                    var currentProgress = Math.Floor((decimal)totalBytesRead / contentLength * 100);
                    if (currentProgress > lastReportedProgress)
                    {
                        progress.Invoke((totalBytesRead, contentLength, 
                            currentProgress.ToString(CultureInfo.InvariantCulture)));
                        lastReportedProgress = (long)currentProgress;
                    }
                }
            }
            
            download.Stream.Position = 0;
            download.IsSuccess = true;
        }
        catch (HttpRequestException ex)
        {
            download.Exception = ex;
        }
        
        return download;
    }
}