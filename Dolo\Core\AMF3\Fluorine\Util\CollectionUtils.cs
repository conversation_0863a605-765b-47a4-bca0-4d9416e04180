﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Collections.ObjectModel;
namespace Dolo.Core.AMF3.Fluorine.Util;

/// <summary>
///     Collection utility class.
/// </summary>
internal abstract class CollectionUtils
{
    /// <summary>
    ///     Determines whether the collection is null or empty.
    /// </summary>
    /// <param name="collection">The collection.</param>
    /// <returns>
    ///     <c>true</c> if the collection is null or empty; otherwise, <c>false</c>.
    /// </returns>
    public static bool IsNullOrEmpty(ICollection collection)
    {
        if (collection != null) return collection.Count == 0;
        return true;
    }

    public static IList CreateList(Type listType)
    {
        ValidationUtils.ArgumentNotNull(listType, "listType");

        IList list;
        Type readOnlyCollectionType;
        var isReadOnlyOrFixedSize = false;

        if (listType.IsArray)
        {
            list = new List<object>();
            isReadOnlyOrFixedSize = true;
        }
        else if (ReflectionUtils.IsSubClass(listType, typeof(ReadOnlyCollection<>), out readOnlyCollectionType))
        {
            var readOnlyCollectionContentsType = readOnlyCollectionType.GetGenericArguments()[0];
            var genericEnumerable =
                ReflectionUtils.MakeGenericType(typeof(IEnumerable<>), readOnlyCollectionContentsType);
            var suitableConstructor = false;

            foreach (var constructor in listType.GetConstructors())
            {
                IList<ParameterInfo> parameters = constructor.GetParameters();

                if (parameters.Count == 1)
                    if (genericEnumerable.IsAssignableFrom(parameters[0].ParameterType))
                    {
                        suitableConstructor = true;
                        break;
                    }
            }

            if (!suitableConstructor)
                throw new(string.Format("Readonly type {0} does not have a public constructor that takes a type that implements {1}.",
                listType, genericEnumerable));

            // can't add or modify a readonly list
            // use List<T> and convert once populated
            list = (IList)CreateGenericList(readOnlyCollectionContentsType);
            isReadOnlyOrFixedSize = true;
        }
        else if (typeof(IList).IsAssignableFrom(listType) && ReflectionUtils.IsInstantiatableType(listType))
            list = (IList)Activator.CreateInstance(listType);
        else
            throw new(string.Format("Cannot create and populate list type {0}.", listType));

        // create readonly and fixed sized collections using the temporary list
        if (isReadOnlyOrFixedSize)
        {
            if (listType.IsArray)
                list = ((List<object>)list).ToArray();
            else if (ReflectionUtils.IsSubClass(listType, typeof(ReadOnlyCollection<>)))
                list = (IList)Activator.CreateInstance(listType, list);
        }

        return list;
    }

    public static Array CreateArray(Type type, ICollection collection)
    {
        ValidationUtils.ArgumentNotNull(collection, "collection");

        if (collection is Array)
            return collection as Array;

        var tempList = new List<object>();
        foreach (var obj in collection)
            tempList.Add(obj);
        return tempList.ToArray();
    }

    public static List<T> CreateList<T>(params T[] values) => [.. values];

    /// <summary>
    ///     Determines whether the collection is null or empty.
    /// </summary>
    /// <param name="collection">The collection.</param>
    /// <returns>
    ///     <c>true</c> if the collection is null or empty; otherwise, <c>false</c>.
    /// </returns>
    public static bool IsNullOrEmpty<T>(ICollection<T> collection)
    {
        if (collection != null) return collection.Count == 0;
        return true;
    }

    /// <summary>
    ///     Determines whether the collection is null, empty or its contents are uninitialized values.
    /// </summary>
    /// <param name="list">The list.</param>
    /// <returns>
    ///     <c>true</c> if the collection is null or empty or its contents are uninitialized values; otherwise, <c>false</c>.
    /// </returns>
    public static bool IsNullOrEmptyOrDefault<T>(IList<T> list)
    {
        if (IsNullOrEmpty(list))
            return true;

        return ReflectionUtils.ItemsUnitializedValue(list);
    }

    /// <summary>
    ///     Makes a slice of the specified list in between the start and end indexes.
    /// </summary>
    /// <param name="list">The list.</param>
    /// <param name="start">The start index.</param>
    /// <param name="end">The end index.</param>
    /// <returns>A slice of the list.</returns>
    public static IList<T> Slice<T>(IList<T> list, int? start, int? end) => Slice(list, start, end, null);

    /// <summary>
    ///     Makes a slice of the specified list in between the start and end indexes,
    ///     getting every so many items based upon the step.
    /// </summary>
    /// <param name="list">The list.</param>
    /// <param name="start">The start index.</param>
    /// <param name="end">The end index.</param>
    /// <param name="step">The step.</param>
    /// <returns>A slice of the list.</returns>
    public static IList<T> Slice<T>(IList<T> list, int? start, int? end, int? step)
    {
        if (list == null)
            throw new ArgumentNullException("list");

        if (step == 0)
            throw new ArgumentException("Step cannot be zero.", "step");

        var slicedList = new List<T>();

        // nothing to slice
        if (list.Count == 0)
            return slicedList;

        // set defaults for null arguments
        var s = step           ?? 1;
        var startIndex = start ?? 0;
        var endIndex = end     ?? list.Count;

        // start from the end of the list if start is negitive
        startIndex = startIndex < 0 ? list.Count + startIndex : startIndex;

        // end from the start of the list if end is negitive
        endIndex = endIndex < 0 ? list.Count + endIndex : endIndex;

        // ensure indexes keep within collection bounds
        startIndex = Math.Max(startIndex, 0);
        endIndex = Math.Min(endIndex, list.Count - 1);

        // loop between start and end indexes, incrementing by the step
        for (var i = startIndex; i < endIndex; i += s) slicedList.Add(list[i]);

        return slicedList;
    }


    /// <summary>
    ///     Adds the elements of the specified collection to the specified generic IList.
    /// </summary>
    /// <param name="initial">The list to add to.</param>
    /// <param name="collection">The collection of elements to add.</param>
    public static void AddRange<T>(IList<T> initial, IEnumerable<T> collection)
    {
        if (initial == null)
            throw new ArgumentNullException("initial");

        if (collection == null)
            return;

        foreach (var value in collection) initial.Add(value);
    }

    public static List<T> Distinct<T>(List<T> collection)
    {
        var distinctList = new List<T>();

        foreach (var value in collection)
            if (!distinctList.Contains(value))
                distinctList.Add(value);

        return distinctList;
    }

    public static List<List<T>> Flatten<T>(params IList<T>[] lists)
    {
        var flattened = new List<List<T>>();
        var currentList = new Dictionary<int, T>();

        Recurse(new List<IList<T>>(lists), 0, currentList, flattened);

        return flattened;
    }

    private static void Recurse<T>(IList<IList<T>> global, int current, Dictionary<int, T> currentSet,
        List<List<T>> flattenedResult)
    {
        var currentArray = global[current];

        for (var i = 0; i < currentArray.Count; i++)
        {
            currentSet[current] = currentArray[i];

            if (current == global.Count - 1)
            {
                var items = new List<T>();

                for (var k = 0; k < currentSet.Count; k++) items.Add(currentSet[k]);

                flattenedResult.Add(items);
            }
            else
                Recurse(global, current + 1, currentSet, flattenedResult);
        }
    }

    public static List<T> CreateList<T>(ICollection collection)
    {
        if (collection == null)
            throw new ArgumentNullException("collection");

        var array = new T[collection.Count];
        collection.CopyTo(array, 0);

        return [.. array];
    }

    public static bool ListEquals<T>(IList<T> a, IList<T> b)
    {
        if (a == null || b == null)
            return a == null && b == null;

        if (a.Count != b.Count)
            return false;

        var comparer = EqualityComparer<T>.Default;

        for (var i = 0; i < a.Count; i++)
            if (!comparer.Equals(a[i], b[i]))
                return false;

        return true;
    }


    public static IList<T> Minus<T>(IList<T> list, IList<T> minus)
    {
        ValidationUtils.ArgumentNotNull(list, "list");

        var result = new List<T>(list.Count);
        foreach (var t in list)
            if (minus == null || !minus.Contains(t))
                result.Add(t);

        return result;
    }


    public static object CreateGenericList(Type listType)
    {
        ValidationUtils.ArgumentNotNull(listType, "listType");

        return ReflectionUtils.CreateGeneric(typeof(List<>), listType);
    }

    public static bool IsListType(Type type)
    {
        ValidationUtils.ArgumentNotNull(type, "listType");

        if (type.IsArray)
            return true;
        if (typeof(IList).IsAssignableFrom(type))
            return true;
        if (ReflectionUtils.IsSubClass(type, typeof(IList<>)))
            return true;
        return false;
    }

    #region GetSingleItem

    public static object GetSingleItem(IList list) => GetSingleItem(list, false);

    public static object GetSingleItem(IList list, bool returnDefaultIfEmpty)
    {
        if (list.Count == 1)
            return list[0];
        if (returnDefaultIfEmpty && list.Count == 0)
            return null;
        throw new(string.Format("Expected single item in list but got {1}.", list.Count));
    }

    #endregion
}