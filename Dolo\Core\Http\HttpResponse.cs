﻿namespace Dolo.Core.Http;

/// <summary>
///     Represents an HTTP response with a specific type of content.
/// </summary>
/// <typeparam name="T">The type of the content in the response.</typeparam>
public class HttpResponse<T>
{
    /// <summary>
    ///     Gets the content of the response.
    /// </summary>
    public T? Body { get; internal set; }

    /// <summary>
    ///     Gets a value indicating whether the request was successful.
    /// </summary>
    public bool IsSuccess { get; internal set; }

    /// <summary>
    ///     Gets the exception that occurred during the request, if any.
    /// </summary>
    public Exception? Exception { get; internal set; }

    /// <summary>
    ///     Gets the original request message.
    /// </summary>
    public HttpRequestMessage? Request { get; internal set; }

    /// <summary>
    ///     Gets the response message from the server.
    /// </summary>
    public HttpResponseMessage? Response { get; internal set; }

    /// <summary>
    ///     Gets the status code of the response, if any.
    /// </summary>
    public HttpStatusCode? StatusCode => Response?.StatusCode;

    /// <summary>
    ///     Attempts to get the response content as a stream.
    /// </summary>
    /// <returns>A stream containing the response content, or null if the response is null.</returns>
    public async Task<Stream?> TryGetStreamAsync()
    {
        if (Response == null) return null;
        return await Response.Content.ReadAsStreamAsync().TryAsync();
    }

    /// <summary>
    ///     Attempts to get the response content as a string.
    /// </summary>
    /// <returns>A string containing the response content, or null if the response is null.</returns>
    public async Task<string?> TryGetStringAsync()
    {
        if (Response == null) return null;
        return await Response.Content.ReadAsStringAsync().TryAsync();
    }

    /// <summary>
    ///     Attempts to get the response content as a byte array.
    /// </summary>
    /// <returns>A byte array containing the response content, or null if the response is null.</returns>
    public async Task<byte[]?> TryGetByteArrayAsync()
    {
        if (Response == null) return null;
        return await Response.Content.ReadAsByteArrayAsync().TryAsync();
    }

    /// <summary>
    ///     Attempts to get the response content as a dynamic object.
    /// </summary>
    /// <returns>A dynamic object containing the response content, or null if the response is null.</returns>
    public async Task<dynamic?> TryGetDynamicAsync()
    {
        if (Response == null) return null;
        return await Response.Content.ReadAsAsync<dynamic>().TryAsync();
    }
}