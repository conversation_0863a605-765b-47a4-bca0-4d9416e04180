﻿global using System.Runtime.CompilerServices;
global using System;
global using System.Diagnostics;
global using System.IO;
global using System.Reflection;
global using System.Net;
global using System.Net.WebSockets;
global using System.Net.Http.Headers;
global using System.Collections.Generic;
global using System.Collections.Specialized;
global using System.Collections.Concurrent;
global using System.Collections;
global using System.Linq;
global using System.Linq.Expressions;
global using System.Text;
global using System.Text.RegularExpressions;
global using System.Threading.Tasks;
global using System.Security.Cryptography;
global using Microsoft.AspNetCore.SignalR.Client;
global using Microsoft.JSInterop;
global using Dolo.Core.Extension;
global using Dolo.Core.AMF3.Fluorine;
global using Dolo.Core.AMF3.Fluorine.AMF3;
global using Dolo.Core.AMF3.Fluorine.Collections;
global using Dolo.Core.AMF3.Fluorine.Configuration;
global using Dolo.Core.AMF3.Fluorine.Context;
global using Dolo.Core.AMF3.Fluorine.DependencyInjection;
global using Dolo.Core.AMF3.Fluorine.Exceptions;
global using Dolo.Core.AMF3.Fluorine.Invocation;
global using Dolo.Core.AMF3.Fluorine.IO;
global using Dolo.Core.AMF3.Fluorine.Messaging;
global using Dolo.Core.AMF3.Fluorine.Util;

global using Dolo.Core.AMF3;
global using MongoDB.Driver;
global using Newtonsoft.Json;
global using Newtonsoft.Json.Linq;
global using DSharpPlus;
global using DSharpPlus.Entities;
global using DSharpPlus.Net;
global using DSharpPlus.Net.Models;
global using DSharpPlus.Commands;


[assembly: InternalsVisibleTo("Dolo.Bot.Apple")]
[assembly: InternalsVisibleTo("Dolo.Bot.Log")]
[assembly: InternalsVisibleTo("Dolo.Bot.Pixi")]
[assembly: InternalsVisibleTo("Dolo.Nebula")]
[assembly: InternalsVisibleTo("Dolo.Planet")]
[assembly: InternalsVisibleTo("Dolo.Secure")]
[assembly: InternalsVisibleTo("Dolo.Obfuscation")]
[assembly: InternalsVisibleTo("Dolo.Tool.Debug")]
[assembly: InternalsVisibleTo("Dolo.Tool.Unlocker")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Shard")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Server")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Maui")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Tools.Fame")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Tools.Autograph")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Tools.Charles")]
[assembly: InternalsVisibleTo("Pluto")]
[assembly: InternalsVisibleTo("Dolo.Test")]
[assembly: InternalsVisibleTo("PlutoOAuth")]

