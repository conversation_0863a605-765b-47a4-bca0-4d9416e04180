using Dolo.Pluto.Shard.Services;
using Dolo.Planet;
using Dolo.Pluto.Tool.Pixler.Components;
using Dolo.Pluto.Shard.Components.Toast;

namespace Dolo.Pluto.Tool.Pixler.Services;

public class ExchangeLogicService : IService {
    public ExchangeState CurrentExchange { get; private set; } = new();
    public event Action<ExchangeState>? ExchangeStateChanged;
    public event Action<ExchangeError>? ExchangeErrorOccurred;

    private CancellationTokenSource? _cancellationTokenSource;

    public async Task<ExchangeResult> StartExchangeAsync(MspClient accountA, MspClient accountB, ulong itemId, string itemName) {
        if (CurrentExchange.IsActive)
            return ExchangeResult.CreateFailure("Exchange already in progress");

        _cancellationTokenSource = new CancellationTokenSource();
        var token = _cancellationTokenSource.Token;

        CurrentExchange = new ExchangeState
        {
            IsActive = true,
            ItemName = itemName,
            Progress = 0,
            Status = "Initializing exchange...",
            CurrentRound = 0,
            TotalRounds = 0,
            SentCount = 0,
            ReceivedCount = 0,
            AccountAStatus = "Checking gift limit...",
            AccountBStatus = "Checking gift limit...",
            AccountAHasItem = false,
            AccountBHasItem = false
        };

        ExchangeStateChanged?.Invoke(CurrentExchange);

        try {
            return await PerformExchangeAsync(accountA, accountB, itemId, itemName, token);
        }
        catch (OperationCanceledException) {
            return ExchangeResult.CreateFailure("Exchange was cancelled");
        }
        catch (Exception ex) {
            return ExchangeResult.CreateFailure($"Exchange failed: {ex.Message}");
        }
        finally {
            CurrentExchange = new ExchangeState();
            ExchangeStateChanged?.Invoke(CurrentExchange);
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }
    }

    public void CancelExchange() {
        _cancellationTokenSource?.Cancel();
        CurrentExchange = new ExchangeState();
        ExchangeStateChanged?.Invoke(CurrentExchange);
    }

    private async Task<ExchangeResult> PerformExchangeAsync(MspClient accountA, MspClient accountB, ulong itemId, string itemName, CancellationToken token) {
        // Step 1: Check gift limits for both accounts
        UpdateStatus("Checking gift limits...", 5);

        var accountAGiftLimit = await CheckGiftLimitAsync(accountA, "Account A");
        if (accountAGiftLimit.HasLimit) {
            NotifyError(ExchangeErrorType.GiftLimitReached, "Account A has reached daily gift limit", "A");
            return ExchangeResult.CreateFailure("Account A has reached daily gift limit");
        }

        var accountBGiftLimit = await CheckGiftLimitAsync(accountB, "Account B");
        if (accountBGiftLimit.HasLimit) {
            NotifyError(ExchangeErrorType.GiftLimitReached, "Account B has reached daily gift limit", "B");
            return ExchangeResult.CreateFailure("Account B has reached daily gift limit");
        }

        // Step 2: Check inventory for the item
        UpdateStatus("Checking inventories...", 15);

        var inventoryCheck = await CheckInventoryAsync(accountA, accountB, itemId, itemName);
        if (!inventoryCheck.Success) {
            NotifyError(ExchangeErrorType.ItemNotFound, inventoryCheck.ErrorMessage, null);
            return ExchangeResult.CreateFailure(inventoryCheck.ErrorMessage);
        }

        // Step 3: Start the exchange loop
        return await PerformExchangeLoopAsync(accountA, accountB, itemId, itemName, inventoryCheck, token);
    }

    private async Task<ExchangeResult> PerformExchangeLoopAsync(MspClient accountA, MspClient accountB, ulong itemId, string _, InventoryCheckResult inventoryCheck, CancellationToken token)
    {
        var currentSender = inventoryCheck.AccountAHasItem ? accountA : accountB;
        var currentReceiver = inventoryCheck.AccountAHasItem ? accountB : accountA;
        var senderName = inventoryCheck.AccountAHasItem ? "A" : "B";
        var receiverName = inventoryCheck.AccountAHasItem ? "B" : "A";

        CurrentExchange.AccountAHasItem = inventoryCheck.AccountAHasItem;
        CurrentExchange.AccountBHasItem = inventoryCheck.AccountBHasItem;
        CurrentExchange.TotalRounds = 50; // Estimate based on typical exchange patterns

        var round = 1;
        var totalSent = 0;
        var totalReceived = 0;

        while (!token.IsCancellationRequested)
        {
            CurrentExchange.CurrentRound = round;
            UpdateStatus($"Round {round}: {senderName} → {receiverName}", 20 + (round * 60 / CurrentExchange.TotalRounds));

            // Update account statuses
            CurrentExchange.AccountAStatus = senderName == "A" ? "Sending gift..." : "Waiting for gift...";
            CurrentExchange.AccountBStatus = senderName == "B" ? "Sending gift..." : "Waiting for gift...";
            ExchangeStateChanged?.Invoke(CurrentExchange);

            // Check gift limit before sending
            var senderGiftLimit = await CheckGiftLimitAsync(currentSender, $"Account {senderName}");
            if (senderGiftLimit.HasLimit)
            {
                NotifyError(ExchangeErrorType.GiftLimitReached, $"Account {senderName} reached daily gift limit", senderName);
                return ExchangeResult.CreateSuccess($"Exchange completed. Account {senderName} reached gift limit after {totalSent} transfers.");
            }

            // Send gift
            var receiverId = currentReceiver.User.Actor.Id;
            var giftResult = await currentSender.GiveGiftAsync(receiverId, itemId);

            if (!giftResult.HasGiftedSuccessfully)
            {
                NotifyError(ExchangeErrorType.GiftFailed, $"Failed to send gift: {giftResult.Description}", senderName);
                return ExchangeResult.CreateFailure($"Gift sending failed: {giftResult.Description}");
            }

            totalSent++;
            CurrentExchange.SentCount = totalSent;
            CurrentExchange.AccountAStatus = senderName == "A" ? "Gift sent" : "Opening gift...";
            CurrentExchange.AccountBStatus = senderName == "B" ? "Gift sent" : "Opening gift...";
            ExchangeStateChanged?.Invoke(CurrentExchange);

            await Task.Delay(1000, token); // Brief delay for UI updates

            // TODO: Implement gift opening logic when available in MspClient
            // For now, we'll assume the gift is automatically opened
            totalReceived++;
            CurrentExchange.ReceivedCount = totalReceived;

            // Switch roles for next round
            (currentSender, currentReceiver) = (currentReceiver, currentSender);
            (senderName, receiverName) = (receiverName, senderName);

            round++;

            // Safety check to prevent infinite loops
            if (round > CurrentExchange.TotalRounds)
            {
                return ExchangeResult.CreateSuccess($"Exchange completed after {CurrentExchange.TotalRounds} rounds. Total transfers: {totalSent}");
            }

            await Task.Delay(2000, token); // Delay between rounds
        }

        return ExchangeResult.CreateFailure("Exchange was cancelled");
    }

    private async Task<GiftLimitResult> CheckGiftLimitAsync(MspClient client, string accountName)
    {
        try
        {
            var result = await client.HasGiftLimitAsync();
            return new GiftLimitResult { HasLimit = result.Value, AccountName = accountName };
        }
        catch (Exception ex)
        {
            return new GiftLimitResult { HasLimit = true, AccountName = accountName, Error = ex.Message };
        }
    }

    private async Task<InventoryCheckResult> CheckInventoryAsync(MspClient accountA, MspClient accountB, ulong itemId, string itemName)
    {
        try
        {
            var accountAItems = await accountA.LoadPagedActorGiftableItems();
            var accountBItems = await accountB.LoadPagedActorGiftableItems();

            var accountAHasItem = accountAItems.Any(item => item.Cloth?.Id == (int)itemId);
            var accountBHasItem = accountBItems.Any(item => item.Cloth?.Id == (int)itemId);

            if (!accountAHasItem && !accountBHasItem)
            {
                return InventoryCheckResult.Failure($"Neither account has the item '{itemName}' in their inventory");
            }

            return new InventoryCheckResult
            {
                Success = true,
                AccountAHasItem = accountAHasItem,
                AccountBHasItem = accountBHasItem
            };
        }
        catch (Exception ex)
        {
            return InventoryCheckResult.Failure($"Failed to check inventory: {ex.Message}");
        }
    }

    private void UpdateStatus(string status, int progress)
    {
        CurrentExchange.Status = status;
        CurrentExchange.Progress = progress;
        ExchangeStateChanged?.Invoke(CurrentExchange);
    }

    private void NotifyError(ExchangeErrorType errorType, string message, string? affectedAccount)
    {
        var error = new ExchangeError
        {
            Type = errorType,
            Message = message,
            AffectedAccount = affectedAccount
        };
        ExchangeErrorOccurred?.Invoke(error);
    }
}

// Supporting classes and enums
public class ExchangeState
{
    public bool IsActive { get; set; }
    public string ItemName { get; set; } = "";
    public string Status { get; set; } = "";
    public int Progress { get; set; }
    public int CurrentRound { get; set; }
    public int TotalRounds { get; set; }
    public int SentCount { get; set; }
    public int ReceivedCount { get; set; }
    public string AccountAStatus { get; set; } = "";
    public string AccountBStatus { get; set; } = "";
    public bool AccountAHasItem { get; set; }
    public bool AccountBHasItem { get; set; }
}

public class ExchangeResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = "";

    public static ExchangeResult CreateSuccess(string message) => new() { Success = true, Message = message };
    public static ExchangeResult CreateFailure(string message) => new() { Success = false, Message = message };
}

public class ExchangeError
{
    public ExchangeErrorType Type { get; set; }
    public string Message { get; set; } = "";
    public string? AffectedAccount { get; set; }
}

public enum ExchangeErrorType
{
    GiftLimitReached,
    ItemNotFound,
    GiftFailed,
    NetworkError,
    UnknownError
}

public class GiftLimitResult
{
    public bool HasLimit { get; set; }
    public string AccountName { get; set; } = "";
    public string? Error { get; set; }
}

public class InventoryCheckResult
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = "";
    public bool AccountAHasItem { get; set; }
    public bool AccountBHasItem { get; set; }

    public static InventoryCheckResult Failure(string errorMessage) => new() { Success = false, ErrorMessage = errorMessage };
}