﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF0;

internal class AMF0CacheableObjectWriter : IAMFWriter
{

    #region IAMFWriter Members

    public bool IsPrimitive
        => true;

    public void WriteData(AMFWriter writer, object data)
    {
        writer.WriteData(ObjectEncoding.AMF0, (data as CacheableObject).Object);
    }

    #endregion
}