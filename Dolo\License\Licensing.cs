﻿namespace Dolo.License;

public class Licensing<T> where T : class
{
    public static T? License { get; set; }

    public static bool HasFile(string? file) => File.Exists(file);
    public static bool HasLicense() => License != null;

    public static T? GetLicense(string file)
    {
        if (HasLicense()) return License;
        return HasFile(file)
                   ? JsonConvert.DeserializeObject<T>(File.ReadAllText(file))
                   : null;
    }
}