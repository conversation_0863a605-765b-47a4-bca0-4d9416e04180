﻿using Bogus;
namespace Dolo.Core;

public static class RandomStringGenerator
{
    private static readonly Faker Faker = new();

    /// <summary>
    ///     Get random name
    /// </summary>
    /// <returns></returns>
    public static string GetName(int length = 10) => Faker.Name.Random.String2(length);

    /// <summary>
    ///     Get random hacker gibberish
    /// </summary>
    /// <returns></returns>
    public static string GetHacker() => Faker.Hacker.Random.String();

    /// <summary>
    ///     Get random port
    /// </summary>
    /// <returns></returns>
    public static int GetPort() => Faker.Internet.Port();

    /// <summary>
    ///     Get random lorem
    /// </summary>
    /// <param name="length"></param>
    /// <returns></returns>
    public static string GetLorem(int length = 10) => Faker.Lorem.Random.String2(length);

    /// <summary>
    ///     Get random image
    /// </summary>
    /// <returns></returns>
    public static string GetImage() => Faker.Image.Random.String();

    /// <summary>
    ///     Get random ips
    /// </summary>
    /// <returns></returns>
    public static string GetIp() => Faker.Internet.Ip();

    /// <summary>
    ///     Get random email
    /// </summary>
    /// <returns></returns>
    public static string GetEmail() => Faker.Internet.Email();

    /// <summary>
    ///     Get random password
    /// </summary>
    /// <returns></returns>
    public static string GetPassword(int length = 10) => Faker.Internet.Password(length);

    /// <summary>
    ///     Get random url
    /// </summary>
    /// <returns></returns>
    public static string GetUrl() => Faker.Internet.Url();


    /// <summary>
    ///     Get random username
    /// </summary>
    /// <returns></returns>
    public static string GetUsername() => Faker.Internet.UserName().Replace("_", "").Replace(".", "");

    /// <summary>
    ///     Get random domain
    /// </summary>
    /// <returns></returns>
    public static string GetDomain() => Faker.Internet.DomainName();

    /// <summary>
    ///     Get random domain word
    /// </summary>
    /// <returns></returns>
    public static string GetDomainWord() => Faker.Internet.DomainWord();

    /// <summary>
    ///     Get random mac address
    /// </summary>
    /// <returns></returns>
    public static string GetMac() => Faker.Internet.Mac();

    /// <summary>
    ///     Get random ipv6
    /// </summary>
    /// <returns></returns>
    public static string GetIpv6() => Faker.Internet.Ipv6();

    /// <summary>
    ///     Get random color
    /// </summary>
    /// <returns></returns>
    public static string GetColor() => Faker.Internet.Color();


    /// <summary>
    ///     Get random string based on length
    /// </summary>
    /// <param name="length"></param>
    /// <returns></returns>
    public static string GetString(int length) => GetString(length, false);

    /// <summary>
    ///     Get random string based on randomization
    /// </summary>
    /// <param name="isRandom"></param>
    /// <returns></returns>
    public static string GetString(bool isRandom) => GetString(20, isRandom);
    private static string GetString(int length, bool isRandom)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return new(Enumerable.Repeat(chars, isRandom ? RandomNumberGenerator.GetInt32(10, 500) : length)
            .Select(s => s[RandomNumberGenerator.GetInt32(s.Length)]).ToArray());
    }
}