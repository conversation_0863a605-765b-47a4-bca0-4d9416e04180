using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Services.Logging;

public class SimpleFileLoggerProvider : ILoggerProvider
{
    private readonly string _logDirectory;
    private static readonly string LogDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".toolbox", "logs");

    public SimpleFileLoggerProvider()
    {
        _logDirectory = LogDirectory;

        try 
        {
            Directory.CreateDirectory(_logDirectory);

            // Clean up old log files (keep only last 7 days)
            CleanupOldLogFiles();
        }
        catch (Exception ex) 
        {
            Console.WriteLine($"Failed to create log directory: {ex.Message}");
        }
    }

    private void CleanupOldLogFiles() 
    {
        try 
        {
            var cutoffDate = DateTime.Now.AddDays(-7);
            var logFiles = Directory.GetFiles(_logDirectory, "shard-*.log");

            foreach (var logFile in logFiles) 
            {
                var fileInfo = new FileInfo(logFile);
                if (fileInfo.CreationTime < cutoffDate) 
                {
                    try 
                    {
                        File.Delete(logFile);
                    }
                    catch 
                    {
                        // Ignore deletion errors
                    }
                }
            }
        }
        catch 
        {
            // Ignore cleanup errors
        }
    }

    public ILogger CreateLogger(string categoryName) => new SimpleFileLogger(categoryName, _logDirectory);

    public void Dispose() 
    {
        GC.SuppressFinalize(this);
    }
}
