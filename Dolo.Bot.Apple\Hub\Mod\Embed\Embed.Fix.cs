using System.Text;

namespace Dolo.Bot.Apple.Hub.Mod.Embed;

public partial class Embed
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("fix")]
    [Description("attempt to fix embed update issues")]
    public async Task FixAsync(SlashCommandContext ctx, 
        [Description("the type of the embed")] EmbedType embedType)
    {
        await ctx.Interaction.DeferAsync(true);

        var results = new StringBuilder();
        results.AppendLine($"🔧 **Fixing {embedType} Embed**\n");

        try
        {
            // Get channel and message info
            var channelInfo = embedType switch
            {
                EmbedType.Welcome => ("Welcome", HubChannel.Welcome, 772233477688655912UL),
                EmbedType.Birthday => ("Birthday", HubChannel.Birthday, 813048025273466920UL),
                EmbedType.Verification => ("Activator", HubChannel.Activator, 961269735352332299UL),
                EmbedType.Reward => ("Reward", HubChannel.Reward, 1207108766118576200UL),
                _ => ("Unknown", null, 0UL)
            };

            var (channelName, channel, messageId) = channelInfo;

            if (channel == null)
            {
                results.AppendLine($"❌ **Cannot fix**: {channelName} channel not found");
                await ctx.TryEditResponseAsync(results.ToString());
                return;
            }

            // Check if message exists
            var existingMessage = await channel.TryGetMessageAsync(messageId);
            
            if (existingMessage == null)
            {
                results.AppendLine($"🔄 **Creating new message**: Original message {messageId} not found");
                
                // Create new message
                var embed = GetEmbedFromType(embedType);
                var newMessage = await channel.TrySendMessageAsync(embed);
                
                if (newMessage != null)
                {
                    results.AppendLine($"✅ **Success**: New message created with ID {newMessage.Id}");
                    results.AppendLine($"⚠️ **Action Required**: Update HubMessage.cs with new message ID:");
                    results.AppendLine($"```csharp");
                    results.AppendLine($"// Replace the old ID with: {newMessage.Id}");
                    results.AppendLine($"```");
                }
                else
                {
                    results.AppendLine($"❌ **Failed**: Could not create new message");
                }
            }
            else
            {
                results.AppendLine($"🔄 **Updating existing message**: Message {messageId} found");
                
                // Check if bot owns the message
                if (existingMessage.Author?.Id != ctx.Client.CurrentUser.Id)
                {
                    results.AppendLine($"❌ **Cannot update**: Bot doesn't own this message");
                    results.AppendLine($"   - Message author: {existingMessage.Author?.Username}");
                    results.AppendLine($"   - Bot user: {ctx.Client.CurrentUser.Username}");
                    results.AppendLine($"💡 **Solution**: Delete the message and use `/embed add {embedType.ToString().ToLower()}`");
                }
                else
                {
                    // Try to update the message
                    try
                    {
                        var embed = GetEmbedFromType(embedType);
                        await existingMessage.TryModifyAsync(embed);
                        results.AppendLine($"✅ **Success**: Message updated successfully");
                    }
                    catch (Exception ex)
                    {
                        results.AppendLine($"❌ **Failed**: Could not update message - {ex.Message}");
                        results.AppendLine($"💡 **Try**: Use `/embed diagnose {embedType.ToString().ToLower()}` for more details");
                    }
                }
            }

            // Additional fixes for Welcome embed
            if (embedType == EmbedType.Welcome)
            {
                results.AppendLine($"\n🔍 **Welcome Embed Channel Check**:");
                
                var channelIssues = new List<string>();
                if (HubChannel.Changelog == null) channelIssues.Add("Changelog");
                if (HubChannel.Rules == null) channelIssues.Add("Rules");
                if (HubChannel.News == null) channelIssues.Add("News");
                
                if (channelIssues.Any())
                {
                    results.AppendLine($"⚠️ **Channel Issues**: {string.Join(", ", channelIssues)} channels are null");
                    results.AppendLine($"   - Check HubChannel.cs for correct channel IDs");
                    results.AppendLine($"   - Verify channels exist in the guild");
                }
                else
                {
                    results.AppendLine($"✅ **All channels**: All referenced channels are accessible");
                }
            }

        }
        catch (Exception ex)
        {
            results.AppendLine($"❌ **Error**: {ex.Message}");
        }

        var embedResult = new DiscordEmbedBuilder()
            .WithTitle($"🔧 Embed Fix Results - {embedType}")
            .WithDescription(results.ToString())
            .WithColor(DiscordColor.Blue)
            .WithTimestamp(DateTimeOffset.UtcNow);

        await ctx.TryEditResponseAsync(embedResult.Build());
    }
}
