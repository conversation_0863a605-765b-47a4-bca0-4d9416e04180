﻿global using DSharpPlus;
global using Dolo.Bot.Log.Hub;
global using DSharpPlus.Entities;
using Dolo.Authentication;
using Dolo.Bot.Log.Hub.Handler;
using Dolo.Bot.Log.Hub.Services;
using Microsoft.Extensions.Logging;

var botToken = Authenticator.GetAuthValue("LogToken");
Hub.Discord = DiscordClientBuilder.CreateDefault(botToken!, DiscordIntents.All)
    .ConfigureRestClient(a => a.Timeout = new(0, 0, 30))
    .ConfigureLogging(a => a.AddConsole())
    .ConfigureEventHandlers(a => {
        a.HandleGuildMemberAdded((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageCreated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageUpdated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleMessageDeleted((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleGuildMemberRemoved((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleInteractionCreated((_, eventArgs) => eventArgs.InvokeAsync());
        a.HandleGuildDownloadCompleted((_, _) => {
            Console.WriteLine("Ready ...");

            // Start attachment monitoring
            _ = Task.Run(async () => {
                while (true)
                {
                    await Task.Delay(TimeSpan.FromMinutes(10)); // Log stats every 10 minutes

                    var (queueCount, isProcessing) = AttachmentCloneService.GetStatus();
                    var totalCached = HubCache.GetTotalCachedAttachments();
                    var clonedCount = HubCache.GetClonedAttachmentCount();

                    if (totalCached > 0 || queueCount > 0)
                    {
                        Console.WriteLine($"[Attachment Monitor] Queue: {queueCount}, Processing: {isProcessing}, Cached: {totalCached:N0}, Cloned: {clonedCount:N0} ({(totalCached > 0 ? (clonedCount * 100.0 / totalCached):0):F1}%)");
                    }
                }
            });

            return Task.CompletedTask;
        });
    }).DisableDefaultLogging()
    .Build();

await Hub.Discord.ConnectAsync(new("analyzer", DiscordActivityType.Playing), DiscordUserStatus.Idle);
await Task.Delay(-1);
