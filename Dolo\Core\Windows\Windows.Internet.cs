﻿using Microsoft.Win32;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;

namespace Dolo.Core.Windows;

public partial class Windows
{
    const int HWND_BROADCAST = 0xffff;
    const uint WM_SETTINGCHANGE = 0x001A;
    const uint SMTO_ABORTIFHUNG = 0x0002;

    [DllImport("wininet.dll")]
    public static extern bool InternetSetOption(nint hInternet, int dwOption, nint lpBuffer, int dwBufferLength);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    static extern IntPtr SendMessageTimeout(
        IntPtr hWnd,
        uint Msg,
        UIntPtr wParam,
        string lParam,
        uint fuFlags,
        uint uTimeout,
        out UIntPtr lpdwResult);
    [SupportedOSPlatform("windows")]
    public static Task SetWinHttpProxyAsync(string proxy) =>
        Process.Start(new ProcessStartInfo {
            FileName = "netsh",
            Arguments = $"winhttp set proxy {proxy}",
            UseShellExecute = false,
            RedirectStandardOutput = true,
            CreateNoWindow = true
        })!
        .WaitForExitAsync();
    [SupportedOSPlatform("windows")]
    public static void RefreshProxy(string proxy) {
        DisableProxy();
        EnableProxy(proxy);
        _ = SendMessageTimeout(
            (IntPtr)HWND_BROADCAST,
            WM_SETTINGCHANGE,
            UIntPtr.Zero,
            "Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings",
            SMTO_ABORTIFHUNG,
            100,
            out _);
    }

    [SupportedOSPlatform("windows")]
    public static void DisableProxy()
    {
        var key = Registry.CurrentUser.OpenSubKey(
            "Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings", true);
        key?.SetValue("ProxyEnable", 0);
        key?.SetValue("ProxyServer", 0);
        key?.Close();
        InternetSetOption(nint.Zero, 39, nint.Zero, 0);
        InternetSetOption(nint.Zero, 37, nint.Zero, 0);
    }

    [SupportedOSPlatform("windows")]
    public static void EnableProxy(string proxy) {
        var key = Registry.CurrentUser.OpenSubKey(
            "Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings", true);
        key?.SetValue("ProxyEnable", 1);
        key?.SetValue("ProxyServer", proxy);
        key?.Close();
        InternetSetOption(nint.Zero, 39, nint.Zero, 0);
        InternetSetOption(nint.Zero, 37, nint.Zero, 0);
    }

    [SupportedOSPlatform("windows")]
    public static bool HasProxy()
    {
        var key = Registry.CurrentUser.OpenSubKey(
            "Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings", true);
        var proxyEnable = key?.GetValue("ProxyEnable");
        var proxyOverride = key?.GetSubKeyNames().Any(x => x.Contains("ProxyOverride"));
        key?.Close();
        return proxyEnable is 1 || proxyOverride.GetValueOrDefault();
    }
}
