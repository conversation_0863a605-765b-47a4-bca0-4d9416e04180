﻿namespace Dolo.Bot.Apple.Hub.Mod.Embed;

public partial class Embed
{
    [RequirePermissions(DiscordPermission.Administrator)]
    [Command("add")]
    [Description("add a new embed to the current channel or target")]
    public async Task AddAsync(SlashCommandContext ctx,
        [Description("the type of the embed")] EmbedType embedType,
        [Description("optional the channel")] DiscordChannel? channel)
    {
        await ctx.Interaction.DeferAsync(true);

        var ch = channel ?? ctx.Channel;
        var em = GetEmbedFromType(embedType);

        await ch.TrySendMessageAsync(em);
        await ctx.TryEditResponseAsync($"{HubEmoji.Apple} Embed {embedType} added into {ch.Name}");
    }
}