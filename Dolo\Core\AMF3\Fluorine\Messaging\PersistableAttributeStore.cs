﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.IO;
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using Dolo.Core.AMF3.Fluorine.Messaging.Api.Persistence;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class PersistableAttributeStore : AttributeStore, IPersistable
{
    protected long _lastModified = -1;
    protected string _name;
    protected string _path;
    protected bool _persistent = true;
    protected IPersistenceStore _store;
    protected string _type;

    public PersistableAttributeStore(string type, string name, string path, bool persistent)
    {
        _name = name;
        _path = path;
        _type = type;
        _persistent = persistent;
    }

    public virtual string Type
    {
        get => _type;
        set => _type = value;
    }

    protected void OnModified()
    {
        _lastModified = Environment.TickCount;
        _store?.Save(this);
    }

    public override bool RemoveAttribute(string name)
    {
        var result = base.RemoveAttribute(name);
        if (result && !name.StartsWith(Constants.TransientPrefix))
            OnModified();
        return result;
    }

    public override void RemoveAttributes()
    {
        base.RemoveAttributes();
        OnModified();
    }

    public override bool SetAttribute(string name, object value)
    {
        var result = base.SetAttribute(name, value);
        if (result && !name.StartsWith(Constants.TransientPrefix))
            OnModified();
        return result;
    }

    public override void SetAttributes(IAttributeStore values)
    {
        base.SetAttributes(values);
        OnModified();
    }

    /// <summary>
    ///     Sets multiple attributes on this object.
    /// </summary>
    /// <param name="values">Dictionary of attributes.</param>
    public override void SetAttributes(IDictionary<string, object> values)
    {
        base.SetAttributes(values);
        OnModified();
    }

    #region IPersistable Members

    public virtual bool IsPersistent
    {
        get => _persistent;
        set => _persistent = value;
    }

    public virtual string Name
    {
        get => _name;
        set => _name = value;
    }

    public virtual string Path
    {
        get => _path;
        set => _path = value;
    }

    public virtual long LastModified
        => _lastModified;

    public virtual IPersistenceStore Store
    {
        get => _store;
        set
        {
            _store = value;
            _store?.Load(this);
        }
    }

    public void Serialize(AMFWriter writer)
    {}

    public void Deserialize(AMFReader reader)
    {}

    #endregion
}