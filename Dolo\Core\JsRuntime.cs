﻿namespace Dolo.Core;

public static class JsRuntime
{
    /// <summary>
    ///     Try to invoke a method
    ///     <para>Example: <code>await TryInvokeAsync&lt;string&gt;(jsRuntime, "myFunction", "Hello World");</code></para>
    ///     <para>Example: <code>await TryInvokeAsync&lt;string&gt;(jsRuntime, "myFunction", "Hello World", 123);</code></para>
    /// </summary>
    public static async Task<T?> TryInvokeAsync<T>(this IJSRuntime runtime, string identifier, params object?[]? parameter) => await TryIt.ThisAsync(async () => await runtime.InvokeAsync<T>(identifier, parameter));

    /// <summary>
    ///     Try to invoke a void method
    /// </summary>
    /// <returns></returns>
    public static async Task TryInvokeVoidAsync(this IJSRuntime runtime, string identifier, params object?[]? parameter) => await TryIt.ThisAsync(async () => await runtime.InvokeVoidAsync(identifier, parameter));
}