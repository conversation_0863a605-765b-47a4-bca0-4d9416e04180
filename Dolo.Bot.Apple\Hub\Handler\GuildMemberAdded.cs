﻿using Dolo.Database;
#pragma warning disable CS8604// Possible null reference argument.
namespace Dolo.Bot.Apple.Hub.Handler;

public static class GuildMemberAdded
{
    public static async Task InvokeAsync(this GuildMemberAddedEventArgs e)
    {
        if (HubChannel.Chat is null || HubChannel.BanLog is null)
            return;

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == e.Member.Id);
        var settings = await Mongo.ServerSettings.GetFirstAsync();

        // if the member is null then process the embed and add new user into database
        if (member is null)
        {
            // send the message
            var message = settings?.IsWelcomeMessageEnabled ?? false
                              ? await HubChannel.Chat.TrySendMessageAsync($"Hey {e.Member.Mention} {HubEmoji.WhiteHeart}")
                              : default;
            var memberDatabase = new ServerMember(e.Member, message);

            // add to database
            await Mongo.ServerMembers.AddAsync(new ServerMember(e.Member, message));

            // delete the message if auto delete is enabled
            if (message != null && settings is { IsWelcomeMessageDeleteAfterEnabled: true })
                await Task.Factory.StartNew(() => message.TryDeleteAfterAsync(TimeSpan.FromMinutes(settings.WelcomeMessageDeleteAfterDelay)));


            // set  member 
            member = memberDatabase;
        }

        // grant the astronomical role
        await e.Member.TryGrantRoleAsync(HubRoles.Astro);

        // assign banned role to member
        if (member.State.IsBanned)
            await e.Member.TryTimeoutAsync(HubConstant.TimeoutTime);

        // assign low role if the member has a low role
        if (member.Level.IsLow)
            await e.Member.TryGrantRoleAsync(HubRoles.Low);

        // assign high role if the member has a high role
        if (member.Level.IsHigh)
            await e.Member.TryGrantRoleAsync(HubRoles.High);

        // assign booster role if the member has a booster role
        if (member.Level.IsEpic)
            await e.Member.TryGrantRoleAsync(HubRoles.Epic);


        var joinMessage = await Hub.MemberSearchSystem.GetMessageAsync(e.Member);
        if (joinMessage is null)
            return;

        await HubChannel.Hello!.TrySendMessageAsync(HubEmbed.NewUser(e.Member, joinMessage));
    }
}