﻿using System.Drawing;
using System.Globalization;
using System.Runtime.Versioning;
using ImageFormat=System.Drawing.Imaging.ImageFormat;
namespace Dolo.Core.Extension;

public static class ColorExtension
{
    /// <summary>
    ///     Converts a System.Drawing.Color to a hex color.
    /// </summary>
    /// <param name="color">specific color</param>
    /// <returns>a hex color</returns>
    public static string ToColorHex(this Color color) => $"#{color.R:X2}{color.G:X2}{color.B:X2}";

    /// <summary>
    ///     Converts a hex color to a System.Drawing.Color
    /// </summary>
    /// <param name="color"></param>
    /// <returns></returns>
    public static string ToColorHex(this string? color) => color is null ? "#000000" : ColorTranslator.FromHtml(color).ToColorHex();

    /// <summary>
    ///     Converts a int color to a hex color
    /// </summary>
    /// <param name="color"></param>
    /// <returns></returns>
    public static string ToColorHex(this int color) => $"#{color:X6}";

    /// <summary>
    ///     Convert hex to 0x
    /// </summary>
    /// <param name="color"></param>
    /// <returns></returns>
    public static string ToZeroX(this int color) => ToColorHex(color).Replace("#", "0x");

    /// <summary>
    ///     Convert hex to 0x
    /// </summary>
    /// <param name="color"></param>
    /// <returns></returns>
    public static string ToZeroX(this string color) => ToColorHex(color).Replace("#", "0x");

    /// <summary>
    ///     Converts the color to rgb integer
    /// </summary>
    /// <param name="color">specific color</param>
    /// <returns>rgb integer color</returns>
    public static int ToInt(this Color color) => color.ToArgb();

    /// <summary>
    ///     Converts a color to rgb integer
    /// </summary>
    /// <param name="color">the hex color (# is optional</param>
    /// <returns>return the rgb integer</returns>
    public static int ToInt(this string color)
    {
        if (string.IsNullOrWhiteSpace(color)) return 0;
        if (color.StartsWith("#"))
            color = color[1..];

        return color.Length != 6 ? 0 : int.Parse(color, NumberStyles.HexNumber);
    }

    /// <summary>
    ///     Converts rgb integer to color
    /// </summary>
    /// <param name="color">rgb integer color</param>
    /// <returns>return a hex color with #</returns>
    public static string FromInt(this int color) => color is 0 ? "#000000" : $"#{color:X6}";

    /// <summary>
    ///     Get a random hex color
    /// </summary>
    /// <returns>return the hex color</returns>
    public static string RandomColor() => $"#{RandomNumberGenerator.GetInt32(0, 0x1000000):X6}";

    /// <summary>
    ///     Checks whenever the color is valid hex color
    /// </summary>
    /// <param name="color">the color in hex</param>
    /// <returns>true if valid</returns>
    public static bool IsValidHexColor(this string color)
    {
        if (string.IsNullOrWhiteSpace(color)) return false;
        if (color.StartsWith("#"))
            color = color[1..];

        return color.Length == 6 && int.TryParse(color, NumberStyles.HexNumber, null, out _);
    }

    [SupportedOSPlatform("windows")]
    public static byte[] ToTransparent(this byte[] bytes, Color color)
    {
        var stream = new MemoryStream(bytes);
        var bitmap = new Bitmap(new MemoryStream(bytes));
        var transparentColor = ColorTranslator.FromHtml(color.ToColorHex());
        bitmap.MakeTransparent(transparentColor);
        bitmap.Save(stream, ImageFormat.Png);
        return stream.ToArray();
    }
}