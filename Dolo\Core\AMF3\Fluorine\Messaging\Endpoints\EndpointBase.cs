﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class EndpointBase : IEndpoint
{
    protected ChannelSettings _channelSettings;
    protected MessageBroker _messageBroker;

    public EndpointBase(MessageBroker messageBroker, ChannelSettings channelSettings)
    {
        _messageBroker = messageBroker;
        _channelSettings = channelSettings;
        Id = _channelSettings.Id;
    }

    #region IEndpoint Members

    public string Id
    {
        get;
        set;
    }

    public MessageBroker GetMessageBroker()
        => _messageBroker;

    public ChannelSettings GetSettings()
        => _channelSettings;

    public virtual void Start()
    {}

    public virtual void Stop()
    {}

    public virtual void Push(IMessage message, MessageClient messageClient)
    {
        throw new NotSupportedException();
    }

    public virtual Task Service()
        => Task.FromResult<object>(null);

    public virtual Task<IMessage> ServiceMessage(IMessage message)
    {
        ValidationUtils.ArgumentNotNull(message, "message");
        return _messageBroker.RouteMessage(message, this);
    }

    public virtual bool IsSecure
        => false;

    #endregion
}