﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Exceptions;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     The MessageException class is used to report exceptions within the messaging system.
/// </summary>
internal class MessageException : AMFException
{

    /// <summary>
    ///     Initializes a new instance of the MessageException class.
    /// </summary>
    public MessageException() => ExtendedData = new();
    /// <summary>
    ///     Initializes a new instance of the MessageException class.
    /// </summary>
    /// <param name="extendedData">Additional information.</param>
    public MessageException(ASObject extendedData) => ExtendedData = extendedData;
    /// <summary>
    ///     Initializes a new instance of the MessageException class.
    /// </summary>
    /// <param name="inner">Reference to the inner exception that is the cause of this exception.</param>
    public MessageException(Exception inner) : base(inner.Message, inner)
    {
        ExtendedData = new();
        RootCause = inner;
    }
    /// <summary>
    ///     Initializes a new instance of the MessageException class.
    /// </summary>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    public MessageException(string message) : base(message) => ExtendedData = new();
    /// <summary>
    ///     Initializes a new instance of the AMFCoreException class with a specified error message and a reference to the
    ///     inner exception that is the cause of this exception.
    /// </summary>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    /// <param name="inner">
    ///     The exception that is the cause of the current exception. If the innerException parameter is not a
    ///     null reference (Nothing in Visual Basic), the current exception is raised in a catch block that handles the inner
    ///     exception.
    /// </param>
    /// <remarks>
    ///     An exception that is thrown as a direct result of a previous exception should include a reference to the
    ///     previous exception in the InnerException property. The InnerException property returns the same value that is
    ///     passed into the constructor, or a null reference (Nothing in Visual Basic) if the InnerException property does not
    ///     supply the inner exception value to the constructor.
    /// </remarks>
    public MessageException(string message, Exception inner) : base(message, inner) => ExtendedData = new();
    /// <summary>
    ///     Initializes a new instance of the MessageException class.
    /// </summary>
    /// <param name="inner">Reference to the inner exception that is the cause of this exception.</param>
    /// <param name="extendedData">Additional information.</param>
    public MessageException(Exception inner, ASObject extendedData) : base(inner.Message, inner)
    {
        ExtendedData = extendedData;
        RootCause = inner;
    }
    /// <summary>
    ///     Initializes a new instance of the MessageException class with a specified error message.
    /// </summary>
    /// <param name="extendedData">Additional information.</param>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    public MessageException(ASObject extendedData, string message) : base(message) => ExtendedData = extendedData;
    /// <summary>
    ///     Initializes a new instance of the MessageException class with a specified error message and a reference to the
    ///     inner exception that is the cause of this exception.
    /// </summary>
    /// <param name="extendedData">Additional information.</param>
    /// <param name="message">The error message that explains the reason for the exception.</param>
    /// <param name="inner">Reference to the inner exception that is the cause of this exception.</param>
    /// <remarks>
    ///     An exception that is thrown as a direct result of a previous exception should include a reference to the
    ///     previous exception in the InnerException property. The InnerException property returns the same value that is
    ///     passed into the constructor, or a null reference (Nothing in Visual Basic) if the InnerException property does not
    ///     supply the inner exception value to the constructor.
    /// </remarks>
    public MessageException(ASObject extendedData, string message, Exception inner) : base(message, inner)
    {
        ExtendedData = extendedData;
        RootCause = inner;
    }

    /// <summary>
    ///     Gets or sets the fault code for the error.
    /// </summary>
    public string FaultCode
    {
        get;
        set;
    } = "Server.Processing";
    /// <summary>
    ///     Root cause for the error.
    /// </summary>
    public object RootCause
    {
        get;
        set;
    }

    /// <summary>
    ///     Return additional information to the client as part of a message exception.
    /// </summary>
    public ASObject ExtendedData
    {
        get;
    }

    internal virtual ErrorMessage GetErrorMessage()
    {
        var errorMessage = new ErrorMessage();
        errorMessage.faultCode = FaultCode;
        errorMessage.faultString = Message;
        errorMessage.extendedData = ExtendedData;
        return errorMessage;
    }
}