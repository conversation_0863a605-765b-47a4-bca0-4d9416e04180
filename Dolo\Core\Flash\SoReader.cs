﻿namespace Dolo.Core.Flash;

internal class SoReader
{
    private readonly byte[] _fileData;
    public int FileSize;
    public int Pos;

    public SoReader(string filename)
    {
        _fileData = File.ReadAllBytes(filename);
        FileSize = 0;
        Pos = 0;
    }

    public byte Read8() => _fileData[Pos++];

    public ushort Read16()
    {
        ushort val = _fileData[Pos++];
        val = (ushort)(val << 8 | _fileData[Pos++]);
        return val;
    }

    public uint Read32()
    {
        uint val = 0;
        for (var i = 0; i < 4; i++)
            val = val << 8 | _fileData[Pos++];

        return val;
    }

    public int ReadCompressedInt()
    {
        var val = 0;
        byte part;
        var finished = true;
        var dataBytes = 0;
        for (var i = 0; i < 3; i++)
        {
            part = Read8();
            finished = (part & 0x80) == 0;
            val = val << 7;
            val |= part & 0b01111111;
            dataBytes += 7;
            if (finished)
                break;
        }
        if (!finished)
        {
            part = Read8();
            val = val << 8;
            val |= part;
            dataBytes += 8;
        }

        if (val >> dataBytes - 1 == 1 && dataBytes == 29)
            val = (int)-(~((uint)val | 0xFFFFFFFF << dataBytes) + 1);

        return val;
    }

    public double ReadDouble()
    {
        var doubleRaw = new byte[8];
        for (var i = 0; i < 8; i++)
            doubleRaw[i] = _fileData[Pos + 7 - i];

        Pos += 8;
        var val = BitConverter.ToDouble(doubleRaw, 0);
        return val;
    }

    public string ReadString(int length)
    {
        var val = Encoding.UTF8.GetString(_fileData, Pos, length);
        Pos += length;
        return val;
    }
}