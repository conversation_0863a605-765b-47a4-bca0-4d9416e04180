﻿namespace Dolo.Bot.Apple.Hub.Mod.Embed;

[Command("embed")]
[Description("send an embed message")]
public partial class Embed 
{
    private static async Task<DiscordMessage> GetMessageFromTypeAsync(EmbedType embedType) => embedType switch
    {
        EmbedType.Welcome      => await HubMessage.GetWelcomeAsync(),
        EmbedType.Birthday     => await HubMessage.GetBirthdayAsync(),
        EmbedType.Verification => await HubMessage.GetActivatorAsync(),
        EmbedType.Reward       => await HubMessage.GetRewardAsync()
    };
    private static DiscordMessageBuilder GetEmbedFromType(EmbedType type) => type switch
    {
        EmbedType.Welcome      => HubEmbed.Welcome,
        EmbedType.Birthday     => HubEmbed.Birthday,
        EmbedType.Verification => HubEmbed.Verify,
        EmbedType.Reward       => HubEmbed.Rewards
    };
}