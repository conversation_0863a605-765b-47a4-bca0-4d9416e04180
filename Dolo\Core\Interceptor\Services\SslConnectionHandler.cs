using System.Net.Security;
using System.Net.Sockets;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor.Services;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class SslConnectionHandler
{
    private readonly CertificateService _certificateService;
    private readonly ConnectionManager _connectionManager;
    private readonly HttpTransactionManager _transactionManager;
    private readonly HttpDataRelayService _dataRelayService;
    private readonly InterceptorConfig _config;
    private readonly ILogger<SslConnectionHandler> _logger;
    private readonly SemaphoreSlim _handshakeSemaphore = new(5, 5);

    public SslConnectionHandler(
        CertificateService certificateService,
        ConnectionManager connectionManager,
        HttpTransactionManager transactionManager,
        HttpDataRelayService dataRelayService,
        InterceptorConfig config,
        ILogger<SslConnectionHandler> logger)
    {
        _certificateService = certificateService;
        _connectionManager = connectionManager;
        _transactionManager = transactionManager;
        _dataRelayService = dataRelayService;
        _config = config;
        _logger = logger;
    }

    public async Task HandleSslConnectionAsync(TcpClient client, NetworkStream clientStream, string hostname, int port)
    {
        await _handshakeSemaphore.WaitAsync().ConfigureAwait(false);

        try
        {
            _logger.LogInformation("Starting SSL handshake for {Host}:{Port}", hostname, port);

            // Create connection in the pairing engine first to get the unified connection ID
            var connectionId = _transactionManager.CreateConnection(hostname, port, "HTTPS");
            if (string.IsNullOrEmpty(connectionId))
            {
                _logger.LogError("Failed to create connection in pairing engine for {Host}:{Port}", hostname, port);
                return;
            }

            // Register the connection with the same ID in the connection manager
            _connectionManager.RegisterConnectionWithId(client, connectionId, hostname, port);

            await clientStream.WriteAsync("HTTP/1.1 200 Connection established\r\n\r\n"u8.ToArray()).ConfigureAwait(false);

            var serverCert = _certificateService.GetOrCreateCertificate(hostname);
            _logger.LogDebug("Using certificate for {Host}: {Subject}", hostname, serverCert.Subject);

            var sslServerStream = new SslStream(clientStream, false);
            await sslServerStream.AuthenticateAsServerAsync(serverCert, false, SslProtocols.Tls12 | SslProtocols.Tls13, false).ConfigureAwait(false);

            var serverClient = new TcpClient();
            await serverClient.ConnectAsync(hostname, port).ConfigureAwait(false);
            var serverStream = serverClient.GetStream();

            var sslClientStream = new SslStream(serverStream, false, ValidateServerCertificate);

            var clientOptions = new SslClientAuthenticationOptions
            {
                TargetHost = hostname,
                EnabledSslProtocols = SslProtocols.Tls12 | SslProtocols.Tls13,
                CertificateRevocationCheckMode = X509RevocationMode.Online,
                RemoteCertificateValidationCallback = ValidateServerCertificate
            };

            await sslClientStream.AuthenticateAsClientAsync(clientOptions).ConfigureAwait(false);

            _logger.LogInformation("SSL handshake completed for {Host}:{Port}", hostname, port);

            var clientToServer = _dataRelayService.RelayDataAsync(sslServerStream, sslClientStream, connectionId, "Client->Server", hostname, port);
            var serverToClient = _dataRelayService.RelayDataAsync(sslClientStream, sslServerStream, connectionId, "Server->Client", hostname, port);

            await Task.WhenAny(clientToServer, serverToClient).ConfigureAwait(false);

            _logger.LogInformation("SSL connection completed for {Host}:{Port}", hostname, port);

            _connectionManager.CloseConnection(connectionId, "SSL session ended");

            try
            {
                sslServerStream.Close();
                sslClientStream.Close();
                serverClient.Close();
            }
            catch
            {
                // Ignore cleanup errors
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ SSL connection failed for {Host}:{Port}: {Error}", hostname, port, ex.Message);
        }
        finally
        {
            _handshakeSemaphore.Release();
        }
    }

    private bool ValidateServerCertificate(object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors)
    {
        if (!_config.EnableOcspValidation)
            return true;

        if (sslPolicyErrors != SslPolicyErrors.None || chain is null || certificate is null)
            return false;

        var cert = new X509Certificate2(certificate);
        var ocspStatus = _certificateService.ValidateServerCertificate(cert);

        return ocspStatus == OcspStatus.Good;
    }
}
