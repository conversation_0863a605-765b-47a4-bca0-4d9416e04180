﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     The IFlexFactory interface is implemented by factory components that provide object instances.
/// </summary>
internal interface IFlexFactory
{
    /// <summary>
    ///     This method is called when the definition of an instance that this factory looks up is initialized.
    /// </summary>
    /// <param name="id">The factory identity.</param>
    /// <param name="properties">Configuration properties.</param>
    /// <returns>A FactoryInstance instance.</returns>
    FactoryInstance CreateFactoryInstance(string id, Hashtable properties);
    /// <summary>
    ///     This method is called by the default implementation of FactoryInstance.Lookup.
    /// </summary>
    /// <param name="factoryInstance">FactoryInstance used to retrieve the object instance.</param>
    /// <returns>The Object instance to use for the given operation for the current destination.</returns>
    object Lookup(FactoryInstance factoryInstance);
}