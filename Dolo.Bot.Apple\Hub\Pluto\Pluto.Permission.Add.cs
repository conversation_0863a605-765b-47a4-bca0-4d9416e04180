﻿using Dolo.Database;
using Dolo.Pluto.Shard.License.Feature;
namespace Dolo.Bot.Apple.Hub.Pluto;

public partial class Pluto
{
    public partial class Permission
    {
        [RequirePermissions(DiscordPermission.BanMembers)]
        [Command("add")]
        [Description("give a user a permission")]
        public async Task AddAsync(SlashCommandContext ctx, [Description("the user that should get the permission")] DiscordUser user, [Description("the permission")] LicensePermission permission)
        {
            await ctx.Interaction.DeferAsync(true);

            var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);
            if (member?.License is null)
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User `{user.Username}` is not licensed");
                return;
            }

            if (member.License.HasPermission(permission))
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User `{user.Username}` has already the permission `{permission}`");
                return;
            }
            

            try
            {
                var builder = Builders<ServerMember>.Update.Push(a => a.License!.Permissions, permission);

                // give the user fame feature if they don't have it
                if (permission == LicensePermission.Fame && !member.License.HasFeature<FeatureFamePurchased>())
                    builder = builder.Push(a => a.License!.Features, new FeatureFamePurchased()
                        .GeneratePrivateKey()
                        .SetId(member.UserId));

                // give the user autograph feature if they don't have it
                if (permission == LicensePermission.Autograph && !member.License.HasFeature<FeatureAutographPurchased>())
                    builder = builder.Push(a => a.License!.Features, new FeatureAutographPurchased()
                        .GeneratePrivateKey()
                        .SetId(member.UserId));

                // update the database
                await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, user.Id), builder);
                
                // get the member from the guild and try to dm them
                var dcMember = await ctx.Guild!.GetMemberAsync(user.Id);
                await dcMember.TryDmAsync($"""
                # New Permission {HubEmoji.MspHeart}

                You got a new permission on pluto
                Permission: {permission}
                """);
            }
            catch (Exception e)
            {
                e.Log();
                return;
            }
            // send response ephemeral
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User `{user.Username}` has been given the permission `{permission}`");
        }
    }
}