﻿using DSharpPlus.SlashCommands.Attributes;

namespace Dolo.Bot.Apple.Hub.Mod;

public class Del
{
    [SlashRequirePermissions(true, DiscordPermission.ManageMessages)]
    [Command("del")]
    [Description("remove messages from the channel")]
    public async Task DelAsync(SlashCommandContext ctx, [Description("the amount of messages that should be deleted")] long amount)
    {
        await ctx.Interaction.DeferAsync(true);

        // get the messages
        var msg = await ctx.Channel.TryGetMessagesAsync((int)amount);

        // get all messages that are not older than 14 days
        var msgFilter = msg?.Where(m => m.Timestamp > DateTimeOffset.Now.AddDays(-14)).ToList();

        // check if the messages are not null and have any
        if (msgFilter is { } && !msgFilter.Any())
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » No messages found or messages are older than 14 days");
            return;
        }

        // bulk delete all messages
        await ctx.Channel.TryDeleteMessageAsync(msgFilter);

        // print the message
        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Deleted {msgFilter?.Count:N0} messages");
    }
}