using System.Reflection;

namespace Dolo.Pluto.Shard.Services;

/// <summary>
/// Service for providing shared static files from Shard to all tools
/// </summary>
public class SharedStaticFileService
{
    private readonly Assembly _assembly = typeof(SharedStaticFileService).Assembly;
    private readonly Dictionary<string, string> _contentTypes = new()
    {
        { ".js", "application/javascript" },
        { ".css", "text/css" },
        { ".html", "text/html" },
        { ".json", "application/json" },
        { ".svg", "image/svg+xml" },
        { ".png", "image/png" },
        { ".jpg", "image/jpeg" },
        { ".jpeg", "image/jpeg" },
        { ".gif", "image/gif" },
        { ".ico", "image/x-icon" }
    };

    /// <summary>
    /// Gets a shared static file as a stream
    /// </summary>
    /// <param name="relativePath">Relative path from wwwroot (e.g., "tailwind.js")</param>
    /// <returns>Stream of the file or null if not found</returns>
    public Stream? GetFileStream(string relativePath)
    {
        var resourceName = $"Dolo.Pluto.Shard.wwwroot.{relativePath.Replace('/', '.')}";
        return _assembly.GetManifestResourceStream(resourceName);
    }

    /// <summary>
    /// Gets the content type for a file based on its extension
    /// </summary>
    /// <param name="fileName">File name or path</param>
    /// <returns>Content type string</returns>
    public string GetContentType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return _contentTypes.TryGetValue(extension, out var contentType) ? contentType : "application/octet-stream";
    }

    /// <summary>
    /// Gets all available embedded resource names that start with the wwwroot prefix
    /// </summary>
    /// <returns>List of resource names</returns>
    public IEnumerable<string> GetAvailableFiles()
    {
        const string prefix = "Dolo.Pluto.Shard.wwwroot.";
        return _assembly.GetManifestResourceNames()
            .Where(name => name.StartsWith(prefix))
            .Select(name => name.Substring(prefix.Length).Replace('.', '/'));
    }

    /// <summary>
    /// Checks if a file exists in the embedded resources
    /// </summary>
    /// <param name="relativePath">Relative path from wwwroot</param>
    /// <returns>True if file exists</returns>
    public bool FileExists(string relativePath)
    {
        var resourceName = $"Dolo.Pluto.Shard.wwwroot.{relativePath.Replace('/', '.')}";
        return _assembly.GetManifestResourceNames().Contains(resourceName);
    }
}
