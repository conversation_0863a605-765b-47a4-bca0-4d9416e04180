namespace Dolo.Bot.Apple.Hub.Voice.Music;

public static class AudioConfig
{
    // Audio quality settings
    public const int SampleRate = 48000;
    public const int Channels = 2;
    public const int FrameSize = 960; // 20ms at 48kHz
    public const int BufferSize = FrameSize * Channels * 2; // 16-bit samples

    // Performance settings
    public const bool MuteHeadphones = true; // Configure bot to ignore incoming audio (prevent feedback)
    public const int MaxConcurrentStreams = 1; // Limit concurrent audio streams
    public const bool UseOpusEncoding = true; // Use Opus encoding for better quality

    // yt-dlp settings
    public const string YtDlpPath = "yt-dlp";
    public const string FfmpegPath = "ffmpeg";
    
    // Audio format arguments for yt-dlp
    public const string AudioFormatArgs = "-f bestaudio --audio-format mp3";
    
    // FFmpeg arguments for PCM conversion
    public const string FfmpegArgs = "-i pipe:0 -ac 2 -f s16le -ar 48000 -loglevel quiet pipe:1";
    
    // Combined command for audio streaming
    public static string GetStreamCommand(string url) => 
        $"{AudioFormatArgs} -o - \"{url}\" | {FfmpegPath} {FfmpegArgs}";
}
