using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Pixler.Components.Content;

namespace Dolo.Pluto.Tool.Pixler.Services;

public class MainService : IService, IMainService
{
    public Main Main { get; set; } = default!;

    // Application state properties can be added here if needed
    public bool IsInitialized { get; set; } = true; // Set to true to skip initialization screen
    public Shard.Toolbox.Tool? Tool { get; set; }

    public void StateHasChangedAsync()
    {
        Main.StateHasChangedAsync();
    }
}