﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Pluto;

public partial class Pluto
{
    public partial class System
    {
        [RequirePermissions(DiscordPermission.BanMembers)]
        [Command("user")]
        [Description("show the information about the pluto user")]
        public async Task UserAsync(SlashCommandContext ctx, [Description("the licensed user")] DiscordUser user)
        {
            await ctx.Interaction.DeferAsync(true);

            // get the member in the database 
            var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);
            if (member is null)
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User `{user.Username}` is not a server-member");
                return;
            }

            if (member.License is null)
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » User {user.Username} has not a licensed account.");
                return;
            }

            await ctx.TryEditResponseAsync(HubEmbed.Pluto(user as <PERSON><PERSON><PERSON><PERSON><PERSON>, member).Embeds[0]);
        }
    }
}