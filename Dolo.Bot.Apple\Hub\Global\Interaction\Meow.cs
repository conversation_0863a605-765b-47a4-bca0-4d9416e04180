﻿namespace Dolo.Bot.Apple.Hub.Global.Interaction;

public static class Meow 
{
    public static async Task MeowAsync(this MessageArgs ctx)
    {
        var req = await Http.TrySendAsync<object>(a => {
            a.Url = "https://api.thecatapi.com/v1/images/search";
            a.Method = HttpMethod.Get;
            a.ContentType = HttpContentType.ApplicationJson;
        });

        // return if not success
        if (!req.IsSuccess || req.Body is null)
            return;

        // use dynamic to get the body deserialized
        var data = req.Body as dynamic;

        // send picture to the chat
        await ctx.Channel.TrySendMessageAsync((string)data[0].url);
    }
}