using Dolo.Bot.Apple.Hub.Voice.Music.Entities;
using System.Diagnostics;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Dolo.Bot.Apple.Hub.Voice.Music.Services;

public static class SearchService
{
    private static readonly HttpClient _httpClient = new();
    private static readonly Regex _youtubeUrlRegex = new(@"(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})", RegexOptions.Compiled);
    private static readonly Regex _spotifyUrlRegex = new(@"(?:https?:\/\/)?(?:open\.)?spotify\.com\/track\/([a-zA-Z0-9]{22})", RegexOptions.Compiled);

    static SearchService()
    {
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "Dolo-Music-Bot/1.0");
    }

    private static string GetYtDlpPath()
    {
        // Get the directory where the current executable is running from
        var executableDir = Path.GetDirectoryName(global::System.Reflection.Assembly.GetExecutingAssembly().Location) ?? "";

        // Check multiple possible locations for yt-dlp.exe
        var possiblePaths = new[]
        {
            Path.Combine(executableDir, "yt-dlp.exe"),  // Same directory as executable
            ".\\yt-dlp.exe",                            // Current working directory
            "yt-dlp.exe",                               // Just the filename
            "yt-dlp"                                    // Without extension
        };

        Console.WriteLine($"[Music Search] Executable directory: {executableDir}");
        Console.WriteLine($"[Music Search] Current working directory: {Directory.GetCurrentDirectory()}");

        foreach (var path in possiblePaths)
        {
            Console.WriteLine($"[Music Search] Checking: {path} - {(File.Exists(path) ? "EXISTS" : "NOT FOUND")}");
            if (File.Exists(path))
            {
                Console.WriteLine($"[Music Search] Found yt-dlp at: {path}");
                return path;
            }
        }

        Console.WriteLine("[Music Search] yt-dlp.exe not found in any location, using default path");
        return Path.Combine(executableDir, "yt-dlp.exe"); // Default to executable directory
    }

    public static async Task<List<Track>> SearchAsync(string query, DiscordUser requestedBy, int maxResults = 5)
    {
        try
        {
            // Check if it's a direct URL
            if (IsYouTubeUrl(query))
            {
                var track = await GetYouTubeTrackAsync(query, requestedBy);
                return track != null ? new List<Track> { track } : new List<Track>();
            }

            if (IsSpotifyUrl(query))
            {
                var track = await GetSpotifyTrackAsync(query, requestedBy);
                return track != null ? new List<Track> { track } : new List<Track>();
            }

            // Search YouTube for the query
            return await SearchYouTubeAsync(query, requestedBy, maxResults);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music Search] Error searching for '{query}': {ex.Message}");
            return new List<Track>();
        }
    }

    public static bool IsYouTubeUrl(string input)
    {
        return _youtubeUrlRegex.IsMatch(input);
    }

    public static bool IsSpotifyUrl(string input)
    {
        return _spotifyUrlRegex.IsMatch(input);
    }

    private static async Task<Track?> GetYouTubeTrackAsync(string url, DiscordUser requestedBy)
    {
        try
        {
            var match = _youtubeUrlRegex.Match(url);
            if (!match.Success) return null;

            var videoId = match.Groups[1].Value;

            // Use yt-dlp to get video info
            var ytDlpPath = GetYtDlpPath();
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = ytDlpPath,
                    Arguments = $"--get-title --get-duration --get-thumbnail \"{url}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                var error = await process.StandardError.ReadToEndAsync();
                Console.WriteLine($"[Music] yt-dlp failed for {url}: {error}");
                return null;
            }

            var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            Console.WriteLine($"[Music] yt-dlp output for {url}:");
            for (int i = 0; i < lines.Length; i++)
            {
                Console.WriteLine($"[Music] Line {i}: {lines[i]}");
            }

            if (lines.Length < 2)
            {
                Console.WriteLine($"[Music] yt-dlp returned insufficient data for {url}");
                return null;
            }

            // yt-dlp with --get-title --get-duration --get-thumbnail returns:
            // Line 0: Title
            // Line 1: Duration
            // Line 2: Thumbnail URL
            var title = lines[0].Trim();
            var durationStr = lines.Length > 1 ? lines[1].Trim() : "3:00";
            var thumbnailUrl = lines.Length > 2 ? lines[2].Trim() : $"https://img.youtube.com/vi/{videoId}/maxresdefault.jpg";

            Console.WriteLine($"[Music] Parsed - Title: {title}, Duration: {durationStr}, Thumbnail: {thumbnailUrl}");

            // Parse duration (format: MM:SS or HH:MM:SS)
            var duration = ParseDuration(durationStr);

            return Track.FromYouTube(
                title: title,
                url: url,
                duration: duration,
                thumbnail: thumbnailUrl,
                requestedBy: requestedBy,
                videoId: videoId
            );
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music Search] Error getting YouTube track: {ex.Message}");
            return null;
        }
    }

    private static Task<Track?> GetSpotifyTrackAsync(string url, DiscordUser requestedBy)
    {
        try
        {
            // Spotify integration requires Spotify API credentials and is not implemented
            Console.WriteLine($"[Music Search] Spotify URLs not supported without API credentials: {url}");
            return Task.FromResult<Track?>(null);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music Search] Error getting Spotify track: {ex.Message}");
            return Task.FromResult<Track?>(null);
        }
    }

    private static async Task<List<Track>> SearchYouTubeAsync(string query, DiscordUser requestedBy, int maxResults)
    {
        try
        {
            // Simplified approach: just get URLs and titles, then extract video IDs
            var ytDlpPath = GetYtDlpPath();
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = ytDlpPath,
                    Arguments = $"--get-url --get-title --get-duration \"ytsearch{maxResults}:{query}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                var error = await process.StandardError.ReadToEndAsync();
                Console.WriteLine($"[Music] yt-dlp search failed for: {query} - {error}");
                return [];
            }

            var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            var results = new List<Track>();

            Console.WriteLine($"[Music] yt-dlp search output for '{query}':");
            for (int i = 0; i < lines.Length; i++)
            {
                Console.WriteLine($"[Music] Line {i}: {lines[i]}");
            }

            // Parse results (groups of 3 lines: URL, title, duration)
            for (int i = 0; i < lines.Length - 2; i += 3)
            {
                if (results.Count >= maxResults) break;

                var url = lines[i].Trim();
                var title = lines[i + 1].Trim();
                var durationStr = lines[i + 2].Trim();

                // Extract video ID from URL
                var match = _youtubeUrlRegex.Match(url);
                if (!match.Success) continue;

                var videoId = match.Groups[1].Value;
                var duration = ParseDuration(durationStr);
                var thumbnailUrl = $"https://img.youtube.com/vi/{videoId}/maxresdefault.jpg";

                Console.WriteLine($"[Music] Parsed track - URL: {url}, Title: {title}, Duration: {durationStr}, VideoID: {videoId}");

                results.Add(Track.FromYouTube(
                    title: title,
                    url: url,
                    duration: duration,
                    thumbnail: thumbnailUrl,
                    requestedBy: requestedBy,
                    videoId: videoId
                ));
            }

            return results;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music Search] Error searching YouTube: {ex.Message}");
            return [];
        }
    }

    public static async Task<List<Track>> GetPlaylistAsync(string playlistUrl, DiscordUser requestedBy)
    {
        try
        {
            // Use yt-dlp to get playlist tracks
            var ytDlpPath = GetYtDlpPath();
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = ytDlpPath,
                    Arguments = $"--get-title --get-duration --get-id --get-thumbnail --flat-playlist \"{playlistUrl}\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                }
            };

            process.Start();
            var output = await process.StandardOutput.ReadToEndAsync();
            await process.WaitForExitAsync();

            if (process.ExitCode != 0)
            {
                var error = await process.StandardError.ReadToEndAsync();
                Console.WriteLine($"[Music] yt-dlp playlist failed for: {playlistUrl} - {error}");
                return [];
            }

            var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            var tracks = new List<Track>();

            // Parse playlist results (groups of 4 lines: title, duration, id, thumbnail)
            for (int i = 0; i < lines.Length - 3; i += 4)
            {
                var title = lines[i].Trim();
                var durationStr = lines[i + 1].Trim();
                var videoId = lines[i + 2].Trim();
                var thumbnailUrl = lines[i + 3].Trim();

                var duration = ParseDuration(durationStr);
                var url = $"https://youtube.com/watch?v={videoId}";

                tracks.Add(Track.FromYouTube(
                    title: title,
                    url: url,
                    duration: duration,
                    thumbnail: thumbnailUrl,
                    requestedBy: requestedBy,
                    videoId: videoId
                ));
            }

            return tracks;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Music Search] Error getting playlist: {ex.Message}");
            return [];
        }
    }

    public static void Dispose()
    {
        _httpClient?.Dispose();
    }





    private static TimeSpan ParseDuration(string durationStr)
    {
        try
        {
            // Handle formats: MM:SS or HH:MM:SS
            var parts = durationStr.Split(':');
            if (parts.Length == 2)
            {
                // MM:SS format
                var minutes = int.Parse(parts[0]);
                var seconds = int.Parse(parts[1]);
                return new TimeSpan(0, minutes, seconds);
            }
            else if (parts.Length == 3)
            {
                // HH:MM:SS format
                var hours = int.Parse(parts[0]);
                var minutes = int.Parse(parts[1]);
                var seconds = int.Parse(parts[2]);
                return new TimeSpan(hours, minutes, seconds);
            }
        }
        catch
        {
            // Fallback to 3 minutes if parsing fails
        }

        return TimeSpan.FromMinutes(3);
    }

}
