﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml.Linq;
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF0;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMF0XElementWriter : IAMFWriter
{
    #region IAMFWriter Members

    public bool IsPrimitive
        => false;

    public void WriteData(AMFWriter writer, object data)
    {
        writer.WriteXElement(data as XElement);
    }

    #endregion
}