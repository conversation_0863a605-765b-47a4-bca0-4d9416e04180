using Dolo.Core;
using Dolo.Planet.Entities;
using Dolo.Planet.Entities.Cloth;
using Dolo.Planet.Entities.Internal;
using Dolo.Planet.NET.Entities;

namespace Dolo.Planet.NET.Commands.AMFMovieStarService;

internal static class LoadActorGiftableItems
{
    /// <summary>
    ///     Loads the giftable items for an actor asynchronously.
    /// </summary>
    /// <param name="actor">The ID of the actor. If 0, the current user's actor ID is used.</param>
    /// <param name="mthd">The method name, automatically filled by the CallerMemberName attribute.</param>
    /// <returns>
    ///     A Task that represents the asynchronous operation. The task result is an MspList object with the giftable items data.
    /// </returns>
    public static async Task<MspList<MspClothRel>> LoadPagedActorGiftableItemsAsync(
        this MspApi api,
        int actor = 0,
        int pageIndex = 0,
        int pageSize = 1000,
        CommandConfig? config = null,
        [CallerMemberName] string mthd = "")
    {
        var req = await api.SendAsync<List<InternalActorClothesRel>>(mthd, new RestConfig()
                      .SetCancellationToken(config?.CancellationToken)
                      .AddParameter(actor == 0 ? api._client.User.Actor.Id : actor)
                      .AddParameter(pageIndex)
                      .AddParameter(pageSize)
                      .SetProxy(config?.Proxy)).ConfigureAwait(false);

        var res = new MspList<MspClothRel>
        {
            HttpException = req.Exception,
            Success = req.Success,
            HttpRequest = !req.Success ? default : req.Request,
            HttpResponse = !req.Success ? default : req.Response
        };


        if (!req.Success || req.Result is null)
            return res;

        req.Result?.ForEach(a => {
            if (a.Cloth is null)
                return;

            res.Add(new MspClothRel
            {
                ActorId = a.ActorId,
                ClothId = a.ClothesId,
                Color = a.Color,
                Id = a.ActorClothesRelId,
                IsWearing = a.IsWearing != 0,
                Cloth = new MspCloth
                {
                    CategoryId = a.Cloth.ClothesCategoryId,
                    ColorScheme = a.Cloth.ColorScheme,
                    DiamondsPrice = a.Cloth.DiamondsPrice,
                    Discount = a.Cloth.Discount,
                    Id = a.Cloth.ClothesCategoryId,
                    IsNew = a.Cloth.IsNew != 0,
                    IsVip = a.Cloth.Vip   != 0,
                    LastUpdatedAt = a.Cloth.LastUpdated,
                    ThemeId = a.Cloth.ThemeId,
                    Name = a.Cloth.Name,
                    Price = a.Cloth.Price,
                    Scale = a.Cloth.Scale,
                    ShopId = a.Cloth.ShopId,
                    SkinId = a.Cloth.SkinId,
                    Sortorder = a.Cloth.Sortorder,
                    Filename = a.Cloth.Swf,
                    SwfUrl = MspApi.GetCategoryUrl(a.Cloth.ClothesCategoryId) +
                             (string.IsNullOrEmpty(a.Cloth?.Swf)
                                  ? a.Cloth?.Filename
                                  : a.Cloth.Swf.Replace(" ", "%20")) +
                             (string.IsNullOrEmpty(a.Cloth?.Swf) ? "" : ".swf"),
                    Category = new MspClothCategory
                    {
                        Id = a.Cloth?.ClothesCategory?.ClothesCategoryId ?? 0,
                        Name = a.Cloth?.ClothesCategory?.Name,
                        SlotTypeId = a.Cloth?.ClothesCategory?.SlotTypeId ?? 0
                    }
                }
            });
        });

        return res;
    }


}