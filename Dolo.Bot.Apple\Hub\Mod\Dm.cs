﻿namespace Dolo.Bot.Apple.Hub.Mod;

public class Dm
{
    [RequirePermissions(DiscordPermission.ManageMessages)]
    [Command("dm")]
    [Description("send a direct message")]
    public async Task DmAsync(SlashCommandContext ctx, [Description("the user that gets the message")] DiscordUser user, [Description("the message to send")] string message)
    {
        await ctx.Interaction.DeferAsync(true);

        if (user is not DiscordMember member)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You can only send messages to members");
            return;
        }

        if (!await member.TryDmAsync(message))
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Failed to send the message");
            return;
        }


        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Message sent to {member.Username} `{message}`");
    }
}