﻿namespace Dolo.Bot.Apple.Hub;

/// <summary>
///     Represents the Hub class which contains static properties and fields used across the application.
/// </summary>
public static class Hub
{
    /// <summary>
    ///     Represents the OAuth salt key used for authentication.
    /// </summary>
    public const string OAuthSaltKey = "<EMAIL>:EzHgy7LaanFQ9PKw22K!L#@RFQVS28Xrn@v6k_/fRh=7Lamf9?)+n)0oz@cy!3!yKM7xm:@kBWxF=Gj0qKYaAUf):jk:m0)15YGlcU/=Q5e.SAUQkwK37I5nPcKqLsTKbqnbhR";

    /// <summary>
    ///     Represents the Discord client used for Discord-related operations.
    /// </summary>
    public static DiscordClient? Discord { get; set; }

    /// <summary>
    ///     Represents the  logger used for logging Discord-related operations.
    ///   It is set to the default logger.
    /// </summary>
    public static ILogger Logger
    {
        get => Discord is null ? LoggerFactory.Create(a => a.AddConsole()).CreateLogger("Discord") : Discord.Logger;
    }

    /// <summary>
    ///     Represents the system used for searching Discord members.
    /// </summary>
    public static DiscordMemberSearchSystem MemberSearchSystem { get; set; } = new(default);

    /// <summary>
    ///     Represents the Discord guild. It fetches the guild from the Discord client using the guild ID.
    /// </summary>
    public static DiscordGuild? Guild => Discord?.Guilds[708318629112053841];

    /// <summary>
    ///     Represents the readiness of the system. It is set to true when the system is ready for operations.
    /// </summary>
    public static bool SystemReady { get; set; }

    /// <summary>
    ///     Represents whether the application is in development mode.
    ///     It is set to true when the application is compiled in DEBUG mode, and false otherwise.
    /// </summary>
    #if DEBUG
    public static bool IsDev = true;
    #else
    public static bool IsDev = false;
    #endif

    /// <summary>
    ///     Represents the list of channels where the level system is active.
    /// </summary>
    public static List<DiscordChannel?> LevelSystemChannel =>
    [
        HubChannel.Chat,
        HubChannel.French,
        HubChannel.Turkish,
        HubChannel.Polish,
        HubChannel.German,
        HubChannel.Art,
        HubChannel.Stats,
        HubChannel.Looks,
        HubChannel.Selfie,
        HubChannel.Outfit,
        HubChannel.Dev
    ];
}