import apiService from './apiService';

class NotificationService {
  constructor() {
    this.STORAGE_KEY = 'pluto-toolbox-notifications';
    this.LAST_SEEN_VERSION_KEY = 'pluto-toolbox-last-seen-version';
    this.AUTO_FETCH_INTERVAL = 60000; // 1 minute
    this.intervalId = null;
    this.onUpdateCallback = null;
  }

  /**
   * Initialize the notification service
   * @param {Function} onUpdate - Callback function when notifications change
   * @param {Function} onHistoryUpdate - Callback function when update history should be refreshed
   */
  init(onUpdate, onHistoryUpdate) {
    this.onUpdateCallback = onUpdate;
    this.onHistoryUpdateCallback = onHistoryUpdate;
    this.startAutoFetch();

    // Initial check
    this.checkForNewUpdates();
  }

  /**
   * Start auto-fetching updates every minute
   */
  startAutoFetch() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }

    this.intervalId = setInterval(() => {
      this.checkForNewUpdates();
    }, this.AUTO_FETCH_INTERVAL);

    console.info('[NotificationService] Auto-fetch started (1 minute interval)');
  }

  /**
   * Stop auto-fetching
   */
  stopAutoFetch() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.info('[NotificationService] Auto-fetch stopped');
    }
  }

  /**
   * Check for new updates and update notification count
   */
  async checkForNewUpdates() {
    try {
      console.debug('[NotificationService] Checking for new updates...');

      // Get comprehensive changelog data (toolbox + tools)
      const comprehensiveData = await apiService.getComprehensiveChangelog(true);

      // Get last seen version from localStorage
      const lastSeenVersion = this.getLastSeenVersion();
      console.debug(`[NotificationService] Last seen version: ${lastSeenVersion || 'none'}`);

      // Find new updates in toolbox changelog
      const toolboxNewUpdates = this.findNewUpdates(comprehensiveData.toolbox.changelog, lastSeenVersion);

      // Find new updates in tool changelogs
      const toolNewUpdates = [];
      for (const tool of comprehensiveData.tools) {
        const toolUpdates = this.findNewUpdates(tool.changelog, lastSeenVersion);
        if (toolUpdates.length > 0) {
          toolNewUpdates.push({
            toolName: tool.name,
            toolVersion: tool.version,
            updates: toolUpdates
          });
        }
      }

      // Combine all new updates
      const allNewUpdates = [
        ...toolboxNewUpdates.map(update => ({ ...update, source: 'toolbox' })),
        ...toolNewUpdates.flatMap(tool =>
          tool.updates.map(update => ({
            ...update,
            source: 'tool',
            toolName: tool.toolName,
            toolVersion: tool.toolVersion
          }))
        )
      ];

      // Only update notifications if there are actually new updates
      if (allNewUpdates.length > 0) {
        console.debug(`[NotificationService] Found ${allNewUpdates.length} new updates since ${lastSeenVersion || 'never'}`);

        // Store notifications and update count
        this.storeNotifications(allNewUpdates);

        // Notify callback about update count change
        if (this.onUpdateCallback) {
          this.onUpdateCallback(allNewUpdates.length);
        }

        // Update the history with comprehensive data
        if (this.onHistoryUpdateCallback) {
          this.onHistoryUpdateCallback(comprehensiveData);
        }
      } else {
        console.debug('[NotificationService] No new updates found');

        // Still update history data even if no new notifications
        if (this.onHistoryUpdateCallback) {
          this.onHistoryUpdateCallback(comprehensiveData);
        }
      }

    } catch (error) {
      console.warn('[NotificationService] Failed to check for updates:', error.message);
    }
  }

  /**
   * Find new updates since the last seen version
   * @param {Array} changelog - Array of changelog entries
   * @param {string} lastSeenVersion - Last version the user has seen
   * @returns {Array} - Array of new updates
   */
  findNewUpdates(changelog, lastSeenVersion) {
    if (!changelog || changelog.length === 0) {
      return [];
    }

    // Sort changelog by version number first, then by release date (newest first)
    const sortedChangelog = changelog.sort((a, b) => {
      const versionA = a.Version || a.version;
      const versionB = b.Version || b.version;

      // First try version comparison
      if (versionA && versionB) {
        if (this.isVersionNewer(versionB, versionA)) return 1;
        if (this.isVersionNewer(versionA, versionB)) return -1;
      }

      // Fall back to date comparison
      const dateA = new Date(a.ReleasedAt || a.releasedAt || 0);
      const dateB = new Date(b.ReleasedAt || b.releasedAt || 0);
      return dateB - dateA;
    });

    // If no last seen version, consider only the latest version as new
    if (!lastSeenVersion) {
      const latestUpdate = sortedChangelog[0];
      console.debug(`[NotificationService] No last seen version, considering latest as new: ${latestUpdate?.Version || latestUpdate?.version}`);
      return latestUpdate ? [latestUpdate] : [];
    }

    // Find updates newer than the last seen version
    const newUpdates = [];
    for (const update of sortedChangelog) {
      const updateVersion = update.Version || update.version;
      if (updateVersion && this.isVersionNewer(updateVersion, lastSeenVersion)) {
        newUpdates.push(update);
        console.debug(`[NotificationService] Found new update: ${updateVersion} (newer than ${lastSeenVersion})`);
      }
    }

    return newUpdates;
  }

  /**
   * Simple version comparison (assumes semantic versioning like 2025.1.2)
   * @param {string} version1 - First version to compare
   * @param {string} version2 - Second version to compare
   * @returns {boolean} - True if version1 is newer than version2
   */
  isVersionNewer(version1, version2) {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      
      if (v1Part > v2Part) return true;
      if (v1Part < v2Part) return false;
    }
    
    return false; // Versions are equal
  }

  /**
   * Get the last seen version from localStorage
   * @returns {string|null} - Last seen version or null
   */
  getLastSeenVersion() {
    try {
      return localStorage.getItem(this.LAST_SEEN_VERSION_KEY);
    } catch (error) {
      console.warn('[NotificationService] Could not read last seen version:', error.message);
      return null;
    }
  }

  /**
   * Set the last seen version in localStorage
   * @param {string} version - Version to mark as seen
   */
  setLastSeenVersion(version) {
    try {
      localStorage.setItem(this.LAST_SEEN_VERSION_KEY, version);
      console.debug(`[NotificationService] Marked version ${version} as seen`);
    } catch (error) {
      console.warn('[NotificationService] Could not store last seen version:', error.message);
    }
  }

  /**
   * Store notifications in localStorage
   * @param {Array} notifications - Array of notification objects
   */
  storeNotifications(notifications) {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(notifications));
    } catch (error) {
      console.warn('[NotificationService] Could not store notifications:', error.message);
    }
  }

  /**
   * Get stored notifications from localStorage
   * @returns {Array} - Array of notification objects
   */
  getStoredNotifications() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('[NotificationService] Could not read stored notifications:', error.message);
      return [];
    }
  }

  /**
   * Mark updates tab as visited (clear notifications)
   */
  async markUpdatesAsViewed() {
    try {
      console.debug('[NotificationService] Marking updates as viewed...');

      // Get the latest comprehensive data to find the absolute latest version
      let absoluteLatestVersion = null;

      try {
        const comprehensiveData = await apiService.getComprehensiveChangelog(true);
        absoluteLatestVersion = this.findAbsoluteLatestVersion(comprehensiveData);
      } catch (error) {
        console.warn('[NotificationService] Could not fetch latest data, using stored notifications');

        // Fallback to stored notifications
        const notifications = this.getStoredNotifications();
        if (notifications.length > 0) {
          // Sort notifications by version to find the latest
          const sortedNotifications = notifications.sort((a, b) => {
            const versionA = a.Version || a.version;
            const versionB = b.Version || b.version;
            return this.isVersionNewer(versionB, versionA) ? 1 : -1;
          });

          absoluteLatestVersion = sortedNotifications[0].Version || sortedNotifications[0].version;
        }
      }

      // If we have a latest version, mark it as seen
      if (absoluteLatestVersion) {
        this.setLastSeenVersion(absoluteLatestVersion);
        console.debug(`[NotificationService] Marked version ${absoluteLatestVersion} as last seen`);
      }

      // Clear stored notifications
      this.storeNotifications([]);

      // Notify callback about count change
      if (this.onUpdateCallback) {
        this.onUpdateCallback(0);
      }

      console.debug('[NotificationService] Updates marked as viewed, notifications cleared');
    } catch (error) {
      console.warn('[NotificationService] Could not mark updates as viewed:', error.message);
    }
  }

  /**
   * Find the absolute latest version across all sources (toolbox + tools)
   * @param {Object} comprehensiveData - Comprehensive changelog data
   * @returns {string|null} - Latest version string
   */
  findAbsoluteLatestVersion(comprehensiveData) {
    let latestVersion = null;

    // Check toolbox versions
    if (comprehensiveData.toolbox && comprehensiveData.toolbox.changelog) {
      for (const entry of comprehensiveData.toolbox.changelog) {
        const version = entry.Version || entry.version;
        if (version && (!latestVersion || this.isVersionNewer(version, latestVersion))) {
          latestVersion = version;
        }
      }
    }

    // Check tool versions
    if (comprehensiveData.tools) {
      for (const tool of comprehensiveData.tools) {
        if (tool.changelog) {
          for (const entry of tool.changelog) {
            const version = entry.Version || entry.version;
            if (version && (!latestVersion || this.isVersionNewer(version, latestVersion))) {
              latestVersion = version;
            }
          }
        }
      }
    }

    console.debug(`[NotificationService] Found absolute latest version: ${latestVersion}`);
    return latestVersion;
  }

  /**
   * Get current notification count
   * @returns {number} - Number of unread notifications
   */
  getNotificationCount() {
    return this.getStoredNotifications().length;
  }

  /**
   * Cleanup when service is destroyed
   */
  destroy() {
    this.stopAutoFetch();
    this.onUpdateCallback = null;
    this.onHistoryUpdateCallback = null;
  }
}

export default new NotificationService();
