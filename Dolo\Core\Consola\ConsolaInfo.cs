﻿namespace Dolo.Core.Consola;

public class ConsolaInfo
{
    public bool? WithTime { get; set; } = true;
    public bool? IsNewLine { get; set; } = true;
    public ConsoleColor? ForegroundColor { get; set; }
    public ConsoleColor? BackgroundColor { get; set; }


    public ConsolaInfo NoNewLine()
    {
        IsNewLine = false;
        return this;
    }

    public ConsolaInfo UseForegroundColor(ConsoleColor color)
    {
        ForegroundColor = color;
        return this;
    }

    public ConsolaInfo UseBackgroundColor(ConsoleColor color)
    {
        BackgroundColor = color;
        return this;
    }

    public ConsolaInfo NoTime()
    {
        WithTime = false;
        return this;
    }
}