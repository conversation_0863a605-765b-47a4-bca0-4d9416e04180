﻿using Dolo.Core.Http;
using System.Reflection;
namespace Dolo.Core.OpenAi;

internal class OpenAIApi
{
    private readonly OpenAI _openAi;
    public OpenAIApi(OpenAI openAi) => _openAi = openAi;

    /// <summary>
    ///     Get the endpoint for the method
    /// </summary>
    private string GetEndPoint(string methodName, string? name = null, string? value = null)
    {
        var method = _openAi.GetType().GetMethod(methodName);
        var attribute = method?.GetCustomAttribute<OpenAIRequest>();

        if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(value))
            attribute!.Url = attribute.Url?.Replace($"{{{name}}}", value);

        return attribute?.Url ?? string.Empty;
    }

    /// <summary>
    ///     Lists the currently available models, and provides basic information
    ///     about each one such as the owner and availability.
    /// </summary>
    public async Task<string?> ListModelsAsync([CallerMemberName] string caller = null!)
    {
        var endpoint = GetEndPoint(caller);
        var http = await Http.Http.TrySendAsync(a => {
            a.Url = endpoint;
            a.Method = HttpMethod.Get;
            a.AuthToken = _openAi.GetConfig().ApiKey;
        });

        return await http.TryGetStringAsync();
    }


    /// <summary>
    ///     Retrieves a model instance, providing basic information
    ///     about the model such as the owner and permissioning.
    /// </summary>
    public async Task<string?> GetModelAsync(string? model, [CallerMemberName] string caller = null!)
    {
        var endpoint = GetEndPoint(caller, nameof(model), model);
        var http = await Http.Http.TrySendAsync(a => {
            a.Url = endpoint;
            a.Method = HttpMethod.Get;
            a.AuthToken = _openAi.GetConfig().ApiKey;
        });

        return await http.TryGetStringAsync();
    }

    /// <summary>
    ///     Creates a completion for the provided prompt and parameters.
    /// </summary>
    public async Task<string?> CreateCompletionAsync(string? prompt, [CallerMemberName] string caller = null!)
    {
        var endpoint = GetEndPoint(caller);
        var http = await Http.Http.TrySendAsync(a => {
            a.Url = endpoint;
            a.Method = HttpMethod.Post;
            a.AuthToken = _openAi.GetConfig().ApiKey;
            a.ContentType = HttpContentType.ApplicationJson;
            a.Content = new StringContent(new Dictionary<string, object?>
            {
                { "model", "text-davinci-003" },
                { "prompt", prompt },
                { "temperature", 0 },
                { "max_tokens", 7 },
                { "top_p", 1 },
                { "n", 1 },
                { "stream", false },
                { "logprobs", null },
                { "stop", "\n" }
            }.ToJson()!);
        });

        return await http.TryGetStringAsync();
    }
}