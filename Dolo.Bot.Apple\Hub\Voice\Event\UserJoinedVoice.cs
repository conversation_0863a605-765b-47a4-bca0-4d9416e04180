﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Voice.Event;

public static class UserJoinedVoice
{
    // will be triggered when a member joined a voice channel (not the creation channel)
    public static async Task InvokeAsync(VoiceArgs e)
    {
        if (e.Channel is null
            || e.Guild is null
            || e.User is null
            || Hub.Guild is null
            || !e.Guild.TryGetMember(e.User.Id, out var member))
            return;

        // get the database entry return when null
        var user = await Mongo.Voice.GetOneAsync(a => a.Channel == e.Channel.Id);
        if (user is null)
            return;

        // check if the user is banned from this voice channel
        if (user.Banned.Contains(e.User.Id))
        {
            // kick the banned user immediately
            await member.TryModifyAsync(a => a.VoiceChannel = null);
            return;
        }

        // get the text channel from server return if not exists
        var text = Hub.Guild.TryGetChannel(user.TextChannel);
        if (text is null)
            return;

        // add user permissions for the text channel
        await text.TryAddOverrideAsync(member, DiscordPermission.ViewChannel | DiscordPermission.ReadMessageHistory);

        // record activity
        VoiceActivityMonitor.RecordActivity(e.Channel.Id, e.User.Id, ActivityType.UserJoined);
    }
}
