using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Shard.Components.Toast;

public partial class ToastContainer : ComponentBase, IDisposable
{
    [Inject] public ToastService ToastService { get; set; } = null!;

    protected override void OnInitialized()
    {
        ToastService.OnToastsChanged += StateHasChanged;
    }

    private void HandleToastClose(Guid toastId)
    {
        ToastService.RemoveToast(toastId);
    }

    public void Dispose()
    {
        ToastService.OnToastsChanged -= StateHasChanged;
    }
}
