﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     <see cref="IOutputIterator">Output iterator</see> based on
///     <see cref="IModifiableCollection.Add">IModifiableCollection.Add.</see>
/// </summary>
internal class Inserter : IOutputIterator
{
    private readonly IModifiableCollection _collection;

    /// <summary>
    ///     Creates instance of inserter for given modifiable collection.
    /// </summary>
    public Inserter(IModifiableCollection collection) => _collection = collection;

    #region IOutputIterator Members

    /// <summary>
    ///     Adds passed object to the collection.
    /// </summary>
    public void Add(object obj)
    {
        _collection.Add(obj);
    }

    #endregion
}