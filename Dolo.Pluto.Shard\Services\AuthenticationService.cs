using Dolo.Pluto.Shard.Services.License;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Services;

public class AuthenticationService(ILicenseService licenseService, ILogger<AuthenticationService> logger)
{
    public async Task<LicenseValidationResult> ProcessTokenAsync(string token, IClientProxy client)
    {
        logger.LogInformation("Processing received token, length: {TokenLength}", token?.Length ?? 0);

        try
        {
            var result = await licenseService.IsAllowedToUseToolAsync(token);
            if (result.IsValid)
            {
                logger.LogInformation("Token validation successful, notifying client");
                await client.SendAsync("verificationComplete");
                await Task.Delay(1000);
                return result;
            }

            logger.LogWarning("Token validation failed: {ErrorMessage}", result.ErrorMessage);
            return result;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing token");
            return new LicenseValidationResult()
                .SetValid(false)
                .SetErrorMessage($"Processing error: {ex.Message}");
        }
    }
}