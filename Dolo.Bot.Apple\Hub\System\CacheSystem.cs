﻿using Dolo.Core.Consola;
namespace Dolo.Bot.Apple.Hub.System;

public static class CacheSystem
{
    public static async Task StartAsync()
    {
        Consola.Information("Caching system started.");
        if (Hub.Guild is null)
            return;

        // get the invites
        var invites = await Hub.Guild.TryGetInvitesAsync();

        // add all invites into the cache
        invites?.ToList().ForEach(a => {
            HubCache.TryAddInvite(a.Code, a);
        });
    }
}