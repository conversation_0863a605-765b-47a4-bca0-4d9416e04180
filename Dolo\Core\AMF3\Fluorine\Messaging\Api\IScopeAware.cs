﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     Marker interface for all objects that are aware of the scope they are located in.
/// </summary>
internal interface IScopeAware
{
    /// <summary>
    ///     Sets the scope the object is located in.
    /// </summary>
    /// <param name="scope">Scope for this object.</param>
    void SetScope(IScope scope);
}