﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Writers.AMF3;

internal class AMF3CacheableObjectWriter : IAMFWriter
{

    #region IAMFWriter Members

    public bool IsPrimitive => true;

    public void WriteData(AMFWriter writer, object data)
    {
        writer.WriteAMF3Data((data as CacheableObject).Object);
    }

    #endregion
}