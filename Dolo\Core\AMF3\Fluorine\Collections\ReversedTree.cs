﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Collections;

/// <summary>
///     Summary description for ReversedTree.
/// </summary>
internal sealed class ReversedTree : IEnumerable
{
    private readonly RbTree _tree;

    public ReversedTree(RbTree tree)
        => _tree = tree;

    public IEnumerator GetEnumerator()
        => new Enumerator(_tree);

    private sealed class Enumerator : IEnumerator
    {
        private readonly RbTree _tree;
        private RbTreeNode _currentNode;

        public Enumerator(RbTree tree)
            => _tree = tree;

        #region IEnumerator Members

        public void Reset()
        {
            _currentNode = null;
        }

        public object Current
            =>
                // if _currentNode is null, will throw an exception, which confirms to IEnumerable spec
                _currentNode.Value;

        public bool MoveNext()
        {
            if (_currentNode == null)
                _currentNode = _tree.First;
            else
                _currentNode = _tree.Next(_currentNode);

            return !_currentNode.IsNull;
        }

        #endregion
    }
}