﻿using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Shard.Services.License;
using Microsoft.AspNetCore.SignalR;

namespace Dolo.Pluto.Shard.Hub;

public class AuthenticationHub(AuthenticationService authenticationService) : Microsoft.AspNetCore.SignalR.Hub
{
    public static Action<LicenseValidationResult>? OnTokenProcessed { get; set; }

    public async Task ReceiveToken(string token)
    {
        Console.WriteLine($"AuthenticationHub: Received token from client, length: {token?.Length ?? 0}");

        if (token != null)
        {
            var result = await authenticationService.ProcessTokenAsync(token, Clients.Caller);
            OnTokenProcessed?.Invoke(result);
        }

        try
        {
            await Clients.Caller.SendAsync("ReceiveToken", token);
            Console.WriteLine($"AuthenticationHub: Acknowledged token receipt to client");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"AuthenticationHub: Error acknowledging token receipt: {ex.Message}");
        }
    }

    public async Task Ping() => await Clients.Caller.SendAsync("Ping");

    public async Task NotifyVerificationComplete()
    {
        try
        {
            Console.WriteLine("MauiHub: Sending VerificationComplete event to client");
            await Clients.Caller.SendAsync("verificationComplete");
            Console.WriteLine("MauiHub: VerificationComplete event sent successfully");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"MauiHub: Error sending VerificationComplete event: {ex.Message}");
            throw;
        }
    }
}