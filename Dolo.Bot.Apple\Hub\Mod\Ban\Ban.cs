﻿using Dolo.Database;
using System.Text;
namespace Dolo.Bot.Apple.Hub.Mod.Ban;

[Command("ban")]
[Description("ban management command")]
public partial class Ban
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("add")]
    [Description("ban a user from the server")]
    public async Task AddAsync(SlashCommandContext ctx, [Description("the user to ban from the server")] DiscordUser user, [Description("the reason of the ban")] BanReason reason, [Description("delete messages")] bool delMessages = true)
    {
        await ctx.Interaction.DeferAsync(true);

        // check if the user is null
        if (Hub.Guild is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You must specify a user to ban");
            return;
        }

        // check if the user is the user who is running the command
        if (user.Id == ctx.User.Id)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You cannot ban yourself");
            return;
        }

        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);

        // add new ban into database
        if (member is null)
        {
            if (user is DiscordMember discordMember)
            {
                await discordMember.TryGrantRoleAsync(HubRoles.Banned);
                await discordMember.TryTimeoutAsync(HubConstant.TimeoutTime);
            }

            await Mongo.ServerMembers.AddAsync(new ServerMember(user, true, reason.GetBanReason()));
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user `{user.Username}` was never a member but has been banned.");
            return;
        }
        // check if the server has this member
        if (!ctx.Guild!.TryGetMember(user.Id, out var guildMember))
        {
            // check if the user is already banned
            if (member is { State.IsBanned: true })
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user you specified is already banned");
                return;
            }

            // ban the member
            member.State.Ban(reason.GetBanReason());

            // send response
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user `{user.Username}` was not a member but has been banned.");
            await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, user.Id),
            Builders<ServerMember>.Update.Set(a => a.State, member.State));
            return;
        }


        // check if the user is already banned
        if (guildMember.Roles.Contains(HubRoles.Banned) || member.State.IsBanned)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user you specified is already banned");
            return;
        }

        // grant banned role
        await guildMember.TryGrantRoleAsync(HubRoles.Banned);
        await guildMember.TryTimeoutAsync(HubConstant.TimeoutTime);

        // print message to the user
        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » {user.Mention} has been banned from the server");
        await HubChannel.BanLog!.TrySendMessageAsync(new StringBuilder()
            .AppendLine($"» {user.Mention} has been banned from the server `{reason.GetBanReason()}`")
            .ToString());


        // deleted messages if not permanently ban
        if (delMessages && reason != BanReason.Permanently)
            await guildMember.TryDeleteManyMessageAsync(channels: HubChannel.TalkCategory!.Children
                .Concat(HubChannel.OtherCategory!.Children)
                .ToArray());

        // update the database with the new member
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, user.Id), Builders<ServerMember>.Update
            .Set(a => a.State.IsBanned, true)
            .Set(a => a.State.BanReason, reason.GetBanReason()));
    }
}