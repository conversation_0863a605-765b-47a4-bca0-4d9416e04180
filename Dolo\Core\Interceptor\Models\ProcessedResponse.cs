namespace Dolo.Core.Interceptor.Models;

public sealed class ProcessedResponse
{
    public int StatusCode { get; set; }
    public string? ReasonPhrase { get; set; }
    public Dictionary<string, string> Headers { get; set; } = new();
    public string? Method { get; set; }
    public Uri? Uri { get; set; }
    public string ContentType { get; set; } = "unknown";
    public string ContentEncoding { get; set; } = "none";
    public string ContentCategory { get; set; } = "unknown";
    public bool IsAmf { get; set; }
    public AmfInterceptedData? AmfData { get; set; }
    public string DisplayContent { get; set; } = string.Empty;
    public string? RawContent { get; set; }
    public byte[]? BinaryContent { get; set; }
}
