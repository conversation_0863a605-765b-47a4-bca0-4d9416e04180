﻿namespace Dolo.Core.Discord;

public class DiscordMemberSearchSystemConfig
{
    public string? Token { get; set; }
    public DiscordClient? Discord { get; set; }
    public DiscordGuild? Guild { get; set; }
    /// <summary>
    ///     Sets the authentication token.
    /// </summary>
    public DiscordMemberSearchSystemConfig SetToken(string? token)
    {
        Token = token;
        return this;
    }

    /// <summary>
    ///     Sets the guild for member searching.
    /// </summary>
    public DiscordMemberSearchSystemConfig SetGuild(DiscordGuild? guild)
    {
        Guild = guild;
        return this;
    }

    /// <summary>
    ///     Sets the discord client.
    /// </summary>
    public DiscordMemberSearchSystemConfig SetDiscord(DiscordClient? discord)
    {
        Discord = discord;
        return this;
    }
}