using System.Collections.Concurrent;
using Dolo.Core.Interceptor.Interfaces;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Services;

public sealed class ConnectionTransactionTracker : IDisposable
{
    private readonly ConcurrentDictionary<string, ConnectionRequestQueue> _connectionQueues = new();
    private readonly ILogger<ConnectionTransactionTracker> _logger;
    private bool _disposed;

    public ConnectionTransactionTracker(ILogger<ConnectionTransactionTracker> logger)
    {
        _logger = logger;
    }

    public string RecordRequest(string connectionId, string transactionId, IHttpInterceptedRequestMessage request, string hostname, int port)
    {
        if (_disposed) return string.Empty;

        var queue = _connectionQueues.GetOrAdd(connectionId, _ => new ConnectionRequestQueue(_logger));
        queue.EnqueueRequest(transactionId, request, hostname, port);

        _logger.LogDebug("📝 Recorded request {TransactionId} for connection {ConnectionId}: {Method} {Uri}",
            transactionId, connectionId, request.Method, request.Uri);

        return transactionId;
    }

    public string? PairResponse(string connectionId, IHttpInterceptedResponseMessage response, string hostname, int port)
    {
        if (_disposed) return null;

        if (!_connectionQueues.TryGetValue(connectionId, out var queue))
        {
            _logger.LogWarning("⚠️ No request queue found for connection {ConnectionId} when pairing response", connectionId);
            return null;
        }

        var transactionId = queue.DequeueResponsePair(hostname, port);

        if (!string.IsNullOrEmpty(transactionId))
        {
            _logger.LogDebug("✅ Paired response with request {TransactionId} for connection {ConnectionId}",
                transactionId, connectionId);
        }
        else
        {
            _logger.LogWarning("❌ Failed to pair response for connection {ConnectionId} - no pending requests", connectionId);
        }

        return transactionId;
    }

    public void CloseConnection(string connectionId)
    {
        if (_connectionQueues.TryRemove(connectionId, out var queue))
        {
            var pendingCount = queue.GetPendingRequestCount();
            if (pendingCount > 0)
            {
                _logger.LogWarning("🧹 Connection {ConnectionId} closed with {PendingCount} unmatched requests",
                    connectionId, pendingCount);
            }
            queue.Dispose();
        }
    }

    public (int ActiveConnections, int TotalPendingRequests) GetStatistics()
    {
        var connections = _connectionQueues.Values.ToList();
        var activeConnections = connections.Count;
        var totalPending = connections.Sum(c => c.GetPendingRequestCount());

        return (activeConnections, totalPending);
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        foreach (var queue in _connectionQueues.Values)
        {
            queue.Dispose();
        }
        _connectionQueues.Clear();
    }

    private sealed class ConnectionRequestQueue(ILogger<ConnectionTransactionTracker> logger) : IDisposable {
        private readonly Queue<PendingRequest> _pendingRequests = new();
        private readonly ILogger<ConnectionTransactionTracker> _logger = logger;
        private readonly Lock _lock = new();
        private bool _disposed;

        public void EnqueueRequest(string transactionId, IHttpInterceptedRequestMessage request, string hostname, int port)
        {
            if (_disposed) return;

            lock (_lock)
            {
                _pendingRequests.Enqueue(new PendingRequest
                {
                    TransactionId = transactionId,
                    Request = request,
                    Hostname = hostname,
                    Port = port,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        public string? DequeueResponsePair(string hostname, int port) {
            if (_disposed) return null;

            lock (_lock)
            {
                if (_pendingRequests.Count == 0)
                {
                    return null;
                }

                var nextRequest = _pendingRequests.Dequeue();

                if (nextRequest.Hostname != hostname || nextRequest.Port != port)
                {
                    _logger.LogWarning("⚠️ Response host:port {ResponseHost}:{ResponsePort} doesn't match request {RequestHost}:{RequestPort} for transaction {TransactionId}",
                        hostname, port, nextRequest.Hostname, nextRequest.Port, nextRequest.TransactionId);
                }

                return nextRequest.TransactionId;
            }
        }

        public int GetPendingRequestCount()
        {
            if (_disposed) return 0;

            lock (_lock)
            {
                return _pendingRequests.Count;
            }
        }

        public void Dispose()
        {
            if (_disposed) return;
            _disposed = true;

            lock (_lock)
            {
                _pendingRequests.Clear();
            }
        }

        private readonly struct PendingRequest
        {
            public string TransactionId { get; init; }
            public IHttpInterceptedRequestMessage Request { get; init; }
            public string Hostname { get; init; }
            public int Port { get; init; }
            public DateTime Timestamp { get; init; }
        }
    }
}
