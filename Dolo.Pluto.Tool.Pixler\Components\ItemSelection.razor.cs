using Microsoft.AspNetCore.Components;
using Dolo.Pluto.Tool.Pixler.Services;
using Dolo.Pluto.Shard.Components.Toast;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class ItemSelection : ComponentBase, IDisposable
{
    [Inject] public ExchangeLogicService ExchangeService { get; set; } = null!;
    [Inject] public InventoryService InventoryService { get; set; } = null!;
    [Inject] public ToastService ToastService { get; set; } = null!;

    // Reference to parent component for accessing account data
    [Parameter] public Func<(Planet.MspClient? AccountA, Planet.MspClient? AccountB)>? GetConnectedClients { get; set; }
    [Parameter] public Func<bool>? AreAccountsConnected { get; set; }
    [Parameter] public EventCallback<ExchangeError> OnExchangeError { get; set; }
    [Parameter] public EventCallback<bool> OnLiveExchangeVisibilityChanged { get; set; }
    public class GameItem
    {
        public string Name { get; set; } = "";
        public string Icon { get; set; } = "";
        public string ImagePath { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public ulong ItemId { get; set; }
    }

    public List<GameItem> Items { get; set; } = new()
    {
        new() { Name = "Pixel Top Girl", Icon = "👕", ImagePath = "assets/Pixel_Top_Girl.png", DisplayName = "Top Girl", ItemId = ********** }, // Replace with actual MSP item ID
        new() { Name = "Pixel Top Boy", Icon = "👕", ImagePath = "assets/Pixel_Top_Boy.png", DisplayName = "Top Boy", ItemId = ********** }, // Replace with actual MSP item ID
        new() { Name = "Pixel Pants Boy", Icon = "🩳", ImagePath = "assets/Pixel_Pants_Boy.png", DisplayName = "Pants Boy", ItemId = 1234567892 }, // Replace with actual MSP item ID
        new() { Name = "Pixel Skirt Girl", Icon = "👗", ImagePath = "assets/Pixel_Skirt_Girl.png", DisplayName = "Skirt Girl", ItemId = 1234567893 }, // Replace with actual MSP item ID
        new() { Name = "Pixel Cap Boy", Icon = "🧢", ImagePath = "assets/Pixel_Cap_Boy.png", DisplayName = "Cap Boy", ItemId = 1234567894 }, // Replace with actual MSP item ID
        new() { Name = "Pixel Hair Girl", Icon = "💇‍♀️", ImagePath = "assets/Pixel_Hair_Girl.png", DisplayName = "Hair Girl", ItemId = 1234567895 }, // Replace with actual MSP item ID
        new() { Name = "Pixel Hair Boy", Icon = "💇‍♂️", ImagePath = "assets/Pixel_Hair_Boy.png", DisplayName = "Hair Boy", ItemId = 1234567896 }, // Replace with actual MSP item ID
        new() { Name = "Pixel Bow Girl", Icon = "🎀", ImagePath = "assets/Pixel_Bow_Girl.png", DisplayName = "Bow Girl", ItemId = 1234567897 } // Replace with actual MSP item ID
    };

    public GameItem? SelectedItem { get; set; }

    private string GetItemClasses(GameItem item)
    {
        var baseClasses = "relative bg-bg-surface border border-border-l1 hover:bg-bg-surface-hover hover:border-border-l2";

        if (SelectedItem?.Name == item.Name)
        {
            return baseClasses + " bg-bg-surface-hover border-border-focus";
        }

        return baseClasses;
    }

    private void LoadItems()
    {
        // Simulate loading items - in real implementation this would load from game
        StateHasChanged();
    }

    private void SelectItem(GameItem item)
    {
        SelectedItem = item;
        StateHasChanged();
    }

    public bool IsExchangeInProgress { get; set; } = false;



    private async Task StartExchange()
    {
        if (SelectedItem == null || IsExchangeInProgress) return;

        // Check if accounts are connected
        if (AreAccountsConnected?.Invoke() != true)
        {
            ToastService.ShowError("Both accounts must be connected before starting exchange");
            return;
        }

        var clients = GetConnectedClients?.Invoke();
        if (clients?.AccountA == null || clients?.AccountB == null)
        {
            ToastService.ShowError("Unable to access account clients");
            return;
        }

        IsExchangeInProgress = true;
        StateHasChanged();

        // Show the LiveExchange window
        await OnLiveExchangeVisibilityChanged.InvokeAsync(true);

        try
        {
            // Start the real exchange
            var result = await ExchangeService.StartExchangeAsync(
                clients.Value.AccountA,
                clients.Value.AccountB,
                SelectedItem.ItemId,
                SelectedItem.Name);

            if (result.Success)
            {
                ToastService.ShowSuccess($"Exchange completed: {result.Message}");
            }
            else
            {
                ToastService.ShowError($"Exchange failed: {result.Message}");
            }
        }
        catch (Exception ex)
        {
            ToastService.ShowError($"Exchange error: {ex.Message}");
        }
        finally
        {
            IsExchangeInProgress = false;
            StateHasChanged();

            // Hide the LiveExchange window when exchange is complete or failed
            await OnLiveExchangeVisibilityChanged.InvokeAsync(false);
        }
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        // Subscribe to exchange service events
        ExchangeService.ExchangeStateChanged += OnExchangeStateChanged;
        ExchangeService.ExchangeErrorOccurred += OnExchangeErrorOccurred;

        // Set Pixel Top Girl as default selected item to match HTML
        SelectedItem = Items.FirstOrDefault(i => i.Name == "Pixel Top Girl");
    }

    private void OnExchangeStateChanged(ExchangeState state)
    {
        IsExchangeInProgress = state.IsActive;
        InvokeAsync(StateHasChanged);
    }

    private async void OnExchangeErrorOccurred(ExchangeError error)
    {
        // Hide the LiveExchange window when an error occurs
        await OnLiveExchangeVisibilityChanged.InvokeAsync(false);
        await OnExchangeError.InvokeAsync(error);
    }

    public void Dispose()
    {
        // Unsubscribe from events
        ExchangeService.ExchangeStateChanged -= OnExchangeStateChanged;
        ExchangeService.ExchangeErrorOccurred -= OnExchangeErrorOccurred;
    }
}
