using System.Collections.Concurrent;
using Dolo.Core.Discord;

namespace Dolo.Bot.Log.Hub.Services;

public static class AttachmentCloneService
{
    private static readonly HttpClient _httpClient = new();
    private static readonly ConcurrentQueue<AttachmentCloneTask> _cloneQueue = new();
    private static readonly SemaphoreSlim _cloneSemaphore = new(3, 3); // Max 3 concurrent downloads
    private static bool _isProcessing = false;

    // File size limits (Discord limits)
    private const long MaxFileSizeBytes = 25 * 1024 * 1024; // 25MB for regular servers
    private const long MaxFileSizeBytesNitro = 100 * 1024 * 1024; // 100MB for nitro servers

    public class AttachmentCloneTask
    {
        public ulong MessageId { get; set; }
        public HubCache.CachedAttachment Attachment { get; set; } = null!;
        public DiscordChannel LogChannel { get; set; } = null!;
    }

    static AttachmentCloneService()
    {
        _httpClient.Timeout = TimeSpan.FromMinutes(5);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "Dolo-Log-Bot/1.0");
    }

    /// <summary>
    /// Queue an attachment for cloning
    /// </summary>
    public static void QueueAttachmentClone(ulong messageId, HubCache.CachedAttachment attachment, DiscordChannel logChannel)
    {
        // Skip if file is too large
        if (attachment.Size > MaxFileSizeBytes)
        {
            Console.WriteLine($"[Attachment Clone] Skipping {attachment.FileName} - too large ({attachment.Size / 1024 / 1024}MB)");
            return;
        }

        _cloneQueue.Enqueue(new AttachmentCloneTask
        {
            MessageId = messageId,
            Attachment = attachment,
            LogChannel = logChannel
        });

        // Start processing if not already running
        if (!_isProcessing)
        {
            _ = Task.Run(ProcessCloneQueueAsync);
        }
    }

    /// <summary>
    /// Process the clone queue
    /// </summary>
    private static async Task ProcessCloneQueueAsync()
    {
        _isProcessing = true;

        try
        {
            while (_cloneQueue.TryDequeue(out var task))
            {
                await _cloneSemaphore.WaitAsync();
                
                try
                {
                    await CloneAttachmentAsync(task);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[Attachment Clone] Error cloning {task.Attachment.FileName}: {ex.Message}");
                }
                finally
                {
                    _cloneSemaphore.Release();
                }

                // Small delay to avoid rate limits
                await Task.Delay(500);
            }
        }
        finally
        {
            _isProcessing = false;
        }
    }

    /// <summary>
    /// Clone a single attachment
    /// </summary>
    private static async Task CloneAttachmentAsync(AttachmentCloneTask task)
    {
        try
        {
            Console.WriteLine($"[Attachment Clone] Downloading {task.Attachment.FileName} ({task.Attachment.Size / 1024}KB)");

            // Download the attachment
            using var response = await _httpClient.GetAsync(task.Attachment.OriginalUrl);
            response.EnsureSuccessStatusCode();

            using var stream = await response.Content.ReadAsStreamAsync();
            using var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            // Validate file size
            if (memoryStream.Length > MaxFileSizeBytes)
            {
                Console.WriteLine($"[Attachment Clone] File {task.Attachment.FileName} too large after download ({memoryStream.Length / 1024 / 1024}MB)");
                return;
            }

            // Create a safe filename
            var safeFileName = SanitizeFileName(task.Attachment.FileName);
            var timestampedFileName = $"{DateTimeOffset.UtcNow:yyyyMMdd_HHmmss}_{safeFileName}";

            // Upload to Discord as our own attachment
            var messageBuilder = new DiscordMessageBuilder()
                .WithContent($"📎 **Attachment Clone** - Original from message `{task.MessageId}`\n" +
                           $"**File:** {task.Attachment.FileName}\n" +
                           $"**Size:** {task.Attachment.Size / 1024.0:F1} KB\n" +
                           $"**Type:** {task.Attachment.ContentType}")
                .AddFile(timestampedFileName, memoryStream);

            var clonedMessage = await task.LogChannel.TrySendMessageAsync(messageBuilder);

            if (clonedMessage?.Attachments.Any() == true)
            {
                var clonedAttachment = clonedMessage.Attachments.First();
                task.Attachment.ClonedUrl = clonedAttachment.Url;

                // Update the cache with the cloned URL
                HubCache.UpdateClonedUrl(task.MessageId, task.Attachment.OriginalUrl, clonedAttachment.Url);

                Console.WriteLine($"[Attachment Clone] Successfully cloned {task.Attachment.FileName} -> {clonedAttachment.Url}");
            }
            else
            {
                Console.WriteLine($"[Attachment Clone] Failed to upload cloned attachment for {task.Attachment.FileName}");
            }
        }
        catch (HttpRequestException ex)
        {
            Console.WriteLine($"[Attachment Clone] HTTP error downloading {task.Attachment.FileName}: {ex.Message}");
        }
        catch (TaskCanceledException ex)
        {
            Console.WriteLine($"[Attachment Clone] Timeout downloading {task.Attachment.FileName}: {ex.Message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[Attachment Clone] Unexpected error cloning {task.Attachment.FileName}: {ex.Message}");
        }
    }

    /// <summary>
    /// Sanitize filename for safe storage
    /// </summary>
    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        
        // Limit length
        if (sanitized.Length > 100)
        {
            var extension = Path.GetExtension(sanitized);
            var nameWithoutExt = Path.GetFileNameWithoutExtension(sanitized);
            sanitized = nameWithoutExt[..Math.Min(nameWithoutExt.Length, 100 - extension.Length)] + extension;
        }

        return string.IsNullOrEmpty(sanitized) ? "unknown_file" : sanitized;
    }

    /// <summary>
    /// Get queue status for monitoring
    /// </summary>
    public static (int QueueCount, bool IsProcessing) GetStatus()
    {
        return (_cloneQueue.Count, _isProcessing);
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    public static void Dispose()
    {
        _httpClient?.Dispose();
        _cloneSemaphore?.Dispose();
    }
}
