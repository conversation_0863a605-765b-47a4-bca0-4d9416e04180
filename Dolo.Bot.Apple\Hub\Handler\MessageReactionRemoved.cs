﻿namespace Dolo.Bot.Apple.Hub.Handler;

public static class MessageReactionRemoved
{
    public static async Task InvokeAsync(this MessageReactionRemovedEventArgs e)
    {
        if (e.Channel == HubChannel.Roles && e.Guild.TryGetMember(e.User.Id, out var member))
        {
            // revoke the female role when the user has the female role
            if (e.Emoji == HubEmoji.Female)
                await member.TryRevokeAsync(HubRoles.Female);

            // revoke the male role when the user has the male role
            if (e.Emoji == HubEmoji.Male)
                await member.TryRevokeAsync(HubRoles.Male);

            // revoke the germany role
            if (e.Emoji == HubEmoji.Germany)
                await member.TryRevokeAsync(HubRoles.Germany);

            // revoke the french role
            if (e.Emoji == HubEmoji.France)
                await member.TryRevokeAsync(HubRoles.France);

            // revoke the turkish role
            if (e.Emoji == HubEmoji.Turkey)
                await member.TryRevokeAsync(HubRoles.Turkey);

            // revoke the polish role
            if (e.Emoji == HubEmoji.Poland)
                await member.TryRevokeAsync(HubRoles.Poland);

            // revoke the changelog notify role
            if (e.Emoji == HubEmoji.Changelog)
                await member.TryRevokeAsync(HubRoles.Changelog);

            // revoke the news notify role
            if (e.Emoji == HubEmoji.News)
                await member.TryRevokeAsync(HubRoles.News);

            // revoke the event notify role
            if (e.Emoji == HubEmoji.Tada)
                await member.TryRevokeAsync(HubRoles.Event);
        }
    }
}