using Dolo.Bot.Apple.Hub.Voice.Music.Services;
using Dolo.Database;

namespace Dolo.Bot.Apple.Hub.Voice.Music;

public partial class Music
{
    [Command("pause")]
    [Description("pause the current track")]
    public async Task PauseAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        if (!player.IsPlaying)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("Nothing is currently playing."));
            return;
        }

        MusicService.PausePlayer(player);
        await ctx.TryEditResponseAsync(MusicEmbeds.Success("⏸️ Playback paused."));
    }

    [Command("resume")]
    [Description("resume the paused track")]
    public async Task ResumeAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        if (!player.IsPaused)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("Playback is not paused."));
            return;
        }

        await MusicService.ResumePlayerAsync(player);
        await ctx.TryEditResponseAsync(MusicEmbeds.Success("▶️ Playback resumed."));
    }

    [Command("skip")]
    [Description("skip the current track")]
    public async Task SkipAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        if (player.CurrentTrack == null)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("Nothing is currently playing."));
            return;
        }

        var skippedTrack = player.CurrentTrack;
        await MusicService.SkipTrackAsync(player);
        
        await ctx.TryEditResponseAsync(MusicEmbeds.Success($"⏭️ Skipped: **{skippedTrack.FormattedTitle}**"));
    }

    [Command("stop")]
    [Description("stop playback and clear the queue")]
    public async Task StopAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        MusicService.StopPlayer(player);
        await ctx.TryEditResponseAsync(MusicEmbeds.Success("⏹️ Playback stopped and queue cleared."));
    }

    [Command("volume")]
    [Description("set the playback volume")]
    public async Task VolumeAsync(SlashCommandContext ctx, 
        [Description("volume level (0-100)")] int volume)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        if (volume < 0 || volume > 100)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("Volume must be between 0 and 100."));
            return;
        }

        var player = MusicService.GetPlayer(ctx.Guild.Id);
        if (player == null || !player.IsConnected)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No music player found."));
            return;
        }

        MusicService.SetVolume(player, volume / 100f);
        await ctx.TryEditResponseAsync(MusicEmbeds.Success($"🔊 Volume set to {volume}%."));
    }

    [Command("disconnect")]
    [Description("disconnect the bot from voice channel")]
    public async Task DisconnectAsync(SlashCommandContext ctx)
    {
        if (ctx.Channel.Parent != HubChannel.VoiceTopic)
        {
            await ctx.TryCreateResponseAsync("This command can only be used in voice channels.", true);
            return;
        }

        await ctx.Interaction.DeferAsync();

        var success = await MusicService.DisconnectPlayerAsync(ctx.Guild.Id);
        if (success)
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Success("👋 Disconnected from voice channel."));
        }
        else
        {
            await ctx.TryEditResponseAsync(MusicEmbeds.Error("No active music player found."));
        }
    }
}
