﻿namespace Dolo.Bot.Apple.Hub.Global.Roulette;

public partial class Roulette
{
    [Command("love")]
    [Description("roulette love % of two users")]
    public async Task LoveAsync(SlashCommandContext ctx, [Description("the first user")] DiscordUser first, [Description("the second user")] DiscordUser second)
    {
        await ctx.Interaction.DeferAsync();
        await ctx.TryEditResponseAsync(string.Format($"{HubEmoji.Astro} **{{0}}** {HubEmoji.WhiteHeart} `{RandomNumberGenerator.GetInt32(0, 101)}%` {HubEmoji.WhiteHeart} **{{1}}**",
        $"{first.Username}",
        $"{second.Username}"));
    }
}