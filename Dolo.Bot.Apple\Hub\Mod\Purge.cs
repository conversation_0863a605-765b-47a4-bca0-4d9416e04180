﻿namespace Dolo.Bot.Apple.Hub.Mod;

[Command("rm")]
[Description("remove messages from a user or a specific content")]
public class Purge
{
    [RequirePermissions(DiscordPermission.ManageMessages)]
    [Command("user")]
    [Description("remove messages from a member")]
    public async Task PurgeAsync(SlashCommandContext ctx, [Description("the user which messages should be deleted")] DiscordUser user, [Description("the amount of messages that should be deleted")]long amount = 100)
    {
        await ctx.Interaction.DeferAsync(true);

        // get the messages
        var msg = await ctx.Channel.TryGetMessagesAsync((int)amount);

        // get all messages that are not older than 14 days
        var msgFilter = msg?.Where(m => m.Author.Id == user.Id && m.Timestamp > DateTimeOffset.Now.AddDays(-14)).ToList();

        // check if the messages are not null and have any
        if (msgFilter is {} && !msgFilter.Any())
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » No messages found or messages are older than 14 days");
            return;
        }

        // bulk delete all messages
        await ctx.Channel.TryDeleteMessageAsync(msgFilter);

        // print the message
        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Deleted {msgFilter?.Count:N0} messages from {user.Username}");
    }

    [RequirePermissions(DiscordPermission.ManageMessages)]
    [Command("content")]
    [Description("remove messages with a specific content")]
    public async Task PurgeAsync(SlashCommandContext ctx, [Description("deletes all messages that contains this specific message")] string message, [Description("the amount of messages that should be deleted")] long amount = 100)
    {
        await ctx.Interaction.DeferAsync(true);

        // get the messages
        var msg = await ctx.Channel.TryGetMessagesAsync((int)amount);

        // get all messages that are not older than 14 days
        var msgFilter = msg?.Where(m => m.Content.ToLower().Contains(message.ToLower()) && m.Timestamp > DateTimeOffset.Now.AddDays(-14)).ToList();

        // check if the messages are not null and have any
        if (msgFilter is {} && !msgFilter.Any())
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » No messages found or messages are older than 14 days");
            return;
        }

        // bulk delete all messages
        await ctx.Channel.TryDeleteMessageAsync(msgFilter);

        // print the message
        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » Deleted {msgFilter?.Count:N0} messages with text {message}");
    }
}