﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Api.Persistence;
using Dolo.Core.AMF3.Fluorine.Messaging.Api.Service;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal interface IScopeContext
{
    /// <summary>
    ///     Gets the client registry. Client registry is a place where all clients are registered.
    /// </summary>
    IClientRegistry ClientRegistry { get; }
    /// <summary>
    ///     Gets persistence store object, a storage for persistent objects like
    ///     persistent SharedObjects.
    /// </summary>
    IPersistenceStore PersistenceStore { get; }
    /// <summary>
    ///     Gets the service invoker object. Service invokers are objects that make
    ///     service calls to client side NetConnection objects.
    /// </summary>
    IServiceInvoker ServiceInvoker { get; }
    /// <summary>
    ///     Gets the scope resolverobject.
    /// </summary>
    IScopeResolver ScopeResolver { get; }
    /// <summary>
    ///     Returns scope by path. You can think of IScope as of tree items, used to
    ///     separate context and resources between users.
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    IScope ResolveScope(string path);
    /// <summary>
    ///     Resolves scope from given root using scope resolver.
    /// </summary>
    /// <param name="root">Scope to start from.</param>
    /// <param name="path">Path to resolve.</param>
    /// <returns></returns>
    IScope ResolveScope(IScope root, string path);
    /// <summary>
    ///     Returns global scope reference.
    /// </summary>
    /// <returns></returns>
    IScope GetGlobalScope();
    /// <summary>
    ///     Returns scope handler (object that handler all actions related to the scope) by path.
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    IScopeHandler LookupScopeHandler(string path);
}