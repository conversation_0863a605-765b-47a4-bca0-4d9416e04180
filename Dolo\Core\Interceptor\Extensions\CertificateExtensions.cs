using System.Security.Cryptography.X509Certificates;
using Dolo.Core.Interceptor.Models;

namespace Dolo.Core.Interceptor.Extensions;

/// <summary>
/// Extension methods for certificate management
/// </summary>
public static class CertificateExtensions
{
    /// <summary>
    /// Exports certificate to PEM format
    /// </summary>
    public static string ToPemString(this X509Certificate2 certificate)
    {
        var certBytes = certificate.Export(X509ContentType.Cert);
        var base64 = System.Convert.ToBase64String(certBytes);
        
        var sb = new System.Text.StringBuilder();
        sb.AppendLine("-----BEGIN CERTIFICATE-----");
        
        for (int i = 0; i < base64.Length; i += 64)
        {
            var length = Math.Min(64, base64.Length - i);
            sb.AppendLine(base64.Substring(i, length));
        }
        
        sb.AppendLine("-----END CERTIFICATE-----");
        return sb.ToString();
    }

    /// <summary>
    /// Gets certificate info in a readable format
    /// </summary>
    public static string GetCertificateInfo(this X509Certificate2 certificate)
    {
        return $"""
            Subject: {certificate.Subject}
            Issuer: {certificate.Issuer}
            Serial: {certificate.SerialNumber}
            Thumbprint: {certificate.Thumbprint}
            Valid From: {certificate.NotBefore:yyyy-MM-dd HH:mm:ss}
            Valid To: {certificate.NotAfter:yyyy-MM-dd HH:mm:ss}
            Algorithm: {certificate.SignatureAlgorithm.FriendlyName}
            Key Size: {certificate.PublicKey.GetRSAPublicKey()?.KeySize} bits
            """;
    }

    /// <summary>
    /// Checks if certificate is about to expire
    /// </summary>
    public static bool IsExpiringSoon(this X509Certificate2 certificate, TimeSpan? threshold = null)
    {
        var checkThreshold = threshold ?? TimeSpan.FromDays(30);
        return certificate.NotAfter - DateTime.Now <= checkThreshold;
    }

    /// <summary>
    /// Gets the Subject Alternative Names (SANs) from the certificate
    /// </summary>
    public static List<string> GetSubjectAlternativeNames(this X509Certificate2 certificate)
    {
        var sanExtension = certificate.Extensions.OfType<X509Extension>()
            .FirstOrDefault(x => x.Oid?.Value == "2.5.29.17"); // SAN OID

        if (sanExtension == null)
            return [];

        var sanList = new List<string>();
        var asnData = new System.Security.Cryptography.AsnEncodedData(sanExtension.Oid!, sanExtension.RawData);
        var sanString = asnData.Format(false);
        
        // Parse the formatted SAN string
        if (!string.IsNullOrEmpty(sanString))
        {
            var lines = sanString.Split('\n', '\r');
            foreach (var line in lines)
            {
                var trimmed = line.Trim();
                if (trimmed.StartsWith("DNS Name="))
                {
                    sanList.Add(trimmed[9..]);
                }
                else if (trimmed.StartsWith("IP Address="))
                {
                    sanList.Add(trimmed[11..]);
                }
            }
        }

        return sanList;
    }
}
