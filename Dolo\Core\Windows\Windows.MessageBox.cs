﻿using System.Runtime.InteropServices;
using System.Runtime.Versioning;
namespace Dolo.Core.Windows;

public partial class Windows
{

    public enum MessageBoxIcon
    {
        None,
        Error,
        Information,
        Warning,
        Question
    }

    [SupportedOSPlatform("windows")]
    [DllImport("user32.dll", CharSet = CharSet.Unicode)]
    private static extern int MessageBox(nint hWnd, string text, string caption, uint type);

    [SupportedOSPlatform("windows")]
    public static void ShowMessageBox(string text, string caption, MessageBoxIcon icon = MessageBoxIcon.None)
    {
        MessageBox(nint.Zero, text, caption, icon switch
        {
            MessageBoxIcon.Error       => 0x10,
            MessageBoxIcon.Information => 0x40,
            MessageBoxIcon.Warning     => 0x30,
            MessageBoxIcon.Question    => 0x20,
            _                          => 0
        });
    }
}