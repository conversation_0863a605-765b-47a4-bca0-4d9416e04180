using System.Reflection;

namespace Dolo.Core.Cryption;

public static class Encrypt
{
    /// <summary>
    ///     Method to encrypt RC2 with a custom made password
    /// </summary>
    /// <param name="input"></param>
    /// <param name="password"></param>
    /// <returns></returns>
    [Obfuscation(Feature = "renaming", Exclude = true)]
    [Obfuscation(Feature = "virtualization", Exclude = true)]
    [Obfuscation(Feature = "constants", Exclude = true)]
    public static string? RC2Encrypt(string? input, string? password)
    {
        try
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(password)) return default;

            var inputArray = Encoding.UTF8.GetBytes(input);
            var keyArray = Encoding.UTF8.GetBytes(password);

            using var tripleDES = TripleDES.Create();

            tripleDES.Key = MD5.HashData(keyArray);
            tripleDES.Mode = CipherMode.ECB;
            tripleDES.Padding = PaddingMode.PKCS7;

            using var cTransform = tripleDES.CreateEncryptor();
            return System.Convert.ToBase64String(cTransform.TransformFinalBlock(inputArray, 0, inputArray.Length))
                .Replace("=", "$5")
                .Replace("+", "$1")
                .Replace("/", "$3");
        }
        catch
        {
            return default;
        }
    }
}