﻿#nullable disable
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
namespace Dolo.Core.AMF3.Fluorine.Context;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal sealed class AMFWebContext : AMFContext
{
    public static Dictionary<string, object> ItemsContext = new();

    /// <summary>
    ///     Gets a key-value collection that can be used to organize and share data between an IHttpModule and an IHttpHandler
    ///     during an HTTP request.
    /// </summary>
    public override IDictionary Items => ItemsContext;

    public override IConnection Connection => Items[AMFConnectionKey] as IConnection;

    public override IClient Client => Items[AMFClientKey] as IClient;

    internal static void Initialize()
    {
        ItemsContext[AMFContextKey] = new AMFWebContext();
    }

    internal void SetConnection(IConnection connection)
    {
        Items[AMFConnectionKey] = connection;
    }

    internal override void SetCurrentClient(IClient client)
    {
        Items[AMFClientKey] = client;
    }
}