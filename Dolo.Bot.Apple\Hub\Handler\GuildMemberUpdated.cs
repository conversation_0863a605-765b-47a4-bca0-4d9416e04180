﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Handler;

public static class GuildMemberUpdated
{
    public static async Task InvokeAsync(this GuildMemberUpdatedEventArgs e)
    {
        // get the member from the database
        var serverMember = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == e.Member.Id);
        if (serverMember is null) return;

        if (serverMember.State.IsBanned && e.Member.CommunicationDisabledUntil is null)
            await e.Member.TryTimeoutAsync(HubConstant.TimeoutTime, serverMember.State.BanReason);

        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter
            .Eq(a => a.Member.DiscordId, e.Member.Id), Builders<ServerMember>.Update
            .Set(a => a.State.IsBooster, e.Member.Roles.Contains(HubRoles.Boost))
            .Set(a => a.State.IsOG, e.Member.Roles.Contains(HubRoles.OG))
            .Set(a => a.Member.AvatarUrl, e.Member.AvatarUrl)
            .Set(a => a.Member.Username, e.Member.Username));
    }
}