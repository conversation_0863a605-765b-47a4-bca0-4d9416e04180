﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api;

/// <summary>
///     MessageClient interface.
/// </summary>
internal interface IMessageClient
{
    /// <summary>
    ///     Gets an object that can be used to synchronize access.
    /// </summary>
    object SyncRoot { get; }
    /// <summary>
    ///     Gets the MessageClient identity.
    /// </summary>
    string ClientId { get; }
    /// <summary>
    ///     Gets whether the connection is being disconnected.
    /// </summary>
    bool IsDisconnecting { get; }
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <returns></returns>
    byte[] GetBinaryId();
}