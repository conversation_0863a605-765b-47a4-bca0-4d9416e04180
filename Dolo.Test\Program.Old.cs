using Dolo.Core;
using Dolo.Nebula;
using Dolo.Nebula.Enum;
using Dolo.Planet;
using Dolo.Planet.Entities;
using Dolo.Planet.Enums;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Dolo.Test;


public partial class Program {
    public static async Task PrintMspAync() {
        var msp = new MspClient(a => {
            a.Username = "winUI";
            a.Password = "DuFotze123";
            a.Server = Dolo.Planet.Enums.Server.France;
            a.UseOriginalBehaviour();

        });
        await msp.LoginAsync();

        var nebula = new NebulaClient(config => {
            config.SetClient(msp);
            config.Game = GameType.MovieStarPlanet;
            config.UseAuthInformation = true;
        });

        await nebula.TryInitializeAsync();

        var users = new List<string> { "oGrunge", "j4ck daniels", "m!k4", "isùa", "Ii$a" };
        var Wallposts = new MspList<MspWallPost>();
        foreach (var user in users)
        {
            var actor = await msp.GetActorAsync(user, [FetchActorInfo.Status]);
            var lastlogin = await nebula.GetProfileIdentityAsync(actor.ProfileId);
            Console.WriteLine(actor.Username + " - " + lastlogin?.GetLastLogin(GameType.MovieStarPlanet));
            Console.WriteLine("Status:  " + actor.Status.Text);
            Console.WriteLine("CreatedAt:  " + actor.Status.LastUpdatedAt);

            var images = await msp.GetActorPictures(actor.Id);
            foreach (var img in images) Console.WriteLine(img.PictureUrl);
            var posts = await msp.GetWallPostsAsync(actor.Id);
            Wallposts.AddRange(posts);
        }

        var wallposts = Wallposts.OrderByDescending(a => a.CreatedAt).ToList();
        foreach (var wallpost in wallposts)
            Console.WriteLine(
                $"[{wallpost.WallActorName}] [[{wallpost.CreatedAt}]] {wallpost.PostActorName} » {wallpost.Message}");

    }
}
