﻿namespace Dolo.Core;

public static class Jsony
{
    /// <summary>
    ///     Tries to parse the json string to the given type
    /// </summary>
    public static T? TryParse<T>(this string? json) => !string.IsNullOrEmpty(json) ? TryIt.This(() => JsonConvert.DeserializeObject<T>(json)) : default;

    /// <summary>
    ///     Tries to parse the json string to the given type
    /// </summary>
    public static T? TryParse<T>(this string? json, Action<Exception>? exceptionHandler) => !string.IsNullOrEmpty(json) ? TryIt.This(() => JsonConvert.DeserializeObject<T>(json), exceptionHandler) : default;


    /// <summary>
    ///     Try to parse the json string to the given type using the given settings
    /// </summary>
    public static T? TryParse<T>(this string? json, JsonSerializerSettings? settings)
        where T : class => !string.IsNullOrEmpty(json) ? TryIt.This(() => JsonConvert.DeserializeObject<T>(json, settings)) : default;

    /// <summary>
    ///     Try to parse the json string to the given type using the given settings
    ///     Exception handling is done by the TryIt class
    /// </summary>
    public static T? TryParse<T>(this string? json, JsonSerializerSettings? settings, Action<Exception>? exceptionHandler)
        where T : class => !string.IsNullOrEmpty(json) ? TryIt.This(() => JsonConvert.DeserializeObject<T>(json, settings), exceptionHandler) : default;


    /// <summary>
    ///     Converts the object to a json string using the given settings
    /// </summary>
    /// <param name="obj">the object</param>
    /// <param name="settings">the settings</param>
    /// <typeparam name="T">the type</typeparam>
    /// <returns></returns>
    public static string? ToJson<T>(this T? obj, JsonSerializerSettings? settings )
        where T : class => obj is {} ? JsonConvert.SerializeObject(obj, settings) : default;

    /// <summary>
    ///     Converts the object to a json string
    /// </summary>
    /// <param name="obj">the object</param>
    /// <returns></returns>
    public static string? ToJson(this object? obj) => obj is {} ? JsonConvert.SerializeObject(obj) : default;


    /// <summary>
    ///     Converts the object to a json string
    /// </summary>
    /// <param name="obj">the object</param>
    /// <param name="formatting"></param>
    /// <returns></returns>
    public static string? ToJson(this object? obj, Formatting formatting) => obj is {} ? JsonConvert.SerializeObject(obj, formatting) : default;

    /// <summary>
    ///     Checks if the json is valid
    /// </summary>
    /// <param name="json">the json string</param>
    /// <returns></returns>
    public static bool IsValidJson(this string? json) => !string.IsNullOrEmpty(json) &&
                                                         TryIt.This(() => JToken.Parse(json)) is {};

    /// <summary>
    ///     Checks if the json is valid and returns the token
    /// </summary>
    /// <param name="json">the json string</param>
    /// <param name="token">JToken otherwise null</param>
    /// <returns></returns>
    public static bool IsValidJson(this string? json, out JToken? token)
    {
        token = !string.IsNullOrEmpty(json) ? TryIt.This(() => JToken.Parse(json)) : default;
        return token is {};
    }
}