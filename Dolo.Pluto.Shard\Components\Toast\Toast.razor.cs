using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Shard.Components.Toast;

public partial class Toast : ComponentBase, IDisposable
{
    [Parameter] public string Message { get; set; } = "";
    [Parameter] public string Title { get; set; } = "";
    [Parameter] public ToastType Type { get; set; } = ToastType.Info;
    [Parameter] public bool IsVisible { get; set; } = true;
    [Parameter] public EventCallback OnClose { get; set; }
    [Parameter] public int AutoCloseDelay { get; set; } = 5000; // 5 seconds

    private Timer? _autoCloseTimer;

    protected override void OnInitialized()
    {
        if (AutoCloseDelay > 0)
        {
            _autoCloseTimer = new Timer(async _ => await AutoClose(), null, AutoCloseDelay, Timeout.Infinite);
        }
    }

    private string GetToastClasses()
    {
        var baseClasses = "bg-bg-surface border border-border-l1 rounded-lg shadow-lg p-4 transition-all duration-300 ease-in-out max-w-sm w-full";

        if (!IsVisible)
        {
            return baseClasses + " transform translate-x-full opacity-0";
        }

        return baseClasses + " transform translate-x-0 opacity-100";
    }

    private async Task Close()
    {
        IsVisible = false;
        StateHasChanged();
        
        // Wait for animation to complete before notifying parent
        await Task.Delay(300);
        await OnClose.InvokeAsync();
    }

    private async Task AutoClose()
    {
        await InvokeAsync(async () => await Close());
    }

    public void Dispose()
    {
        _autoCloseTimer?.Dispose();
    }
}
