﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.DependencyInjection;
using Dolo.Core.AMF3.Fluorine.Exceptions;
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Endpoints;
using Dolo.Core.AMF3.Fluorine.Messaging.Services;
using Dolo.Core.AMF3.Fluorine.Util;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal sealed class MessageServer : DisposableBase
{
    /// <summary>
    ///     Initializes a new instance of the MessageServer class.
    /// </summary>
    public MessageServer()
    {}

    internal ServiceConfigSettings ServiceConfigSettings { get; private set; }

    /// <summary>
    ///     Gets the message broker started by this server.
    /// </summary>
    public MessageBroker MessageBroker { get; private set; }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public void Init()
    {
        MessageBroker = new(this);

        ServiceConfigSettings = ServiceConfigSettings.Load();
        foreach (ChannelSettings channelSettings in ServiceConfigSettings.ChannelsSettings)
        {
            var type = ObjectFactory.Locate(channelSettings.Class);
            if (type != null)
            {
                var endpoint =
                    ObjectFactory.CreateInstance(type, [MessageBroker, channelSettings]) as IEndpoint;
                if (endpoint != null)
                    MessageBroker.AddEndpoint(endpoint);
            }
        }

        foreach (FactorySettings factorySettings in ServiceConfigSettings.FactoriesSettings)
        {
            var type = ObjectFactory.Locate(factorySettings.ClassId);
            if (type != null)
            {
                var flexFactory = ObjectFactory.CreateInstance(type, new object[0]) as IFlexFactory;
                if (flexFactory != null)
                    MessageBroker.AddFactory(factorySettings.Id, flexFactory);
            }
        }

        //Add the dotnet Factory
        MessageBroker.AddFactory("dotnet", new DotNetFactory());

        foreach (ServiceSettings serviceSettings in ServiceConfigSettings.ServiceSettings)
        {
            var type = ObjectFactory.Locate(serviceSettings.Class);//current assembly only
            if (type != null)
            {
                var service =
                    ObjectFactory.CreateInstance(type, [MessageBroker, serviceSettings]) as IService;
                if (service != null)
                    MessageBroker.AddService(service);
            }
        }
    }

    private void InstallServiceBrowserDestinations(ServiceSettings serviceSettings, AdapterSettings adapterSettings)
    {
        //ServiceBrowser destinations
        var destinationSettings = new DestinationSettings(serviceSettings,
        DestinationSettings.AMFServiceBrowserDestination, adapterSettings,
        DestinationSettings.AMFServiceBrowserDestination);
        serviceSettings.DestinationSettings.Add(destinationSettings);

        destinationSettings = new(serviceSettings, DestinationSettings.AMFManagementDestination,
        adapterSettings, DestinationSettings.AMFManagementDestination);
        serviceSettings.DestinationSettings.Add(destinationSettings);

        destinationSettings = new(serviceSettings, DestinationSettings.AMFCodeGeneratorDestination,
        adapterSettings, DestinationSettings.AMFCodeGeneratorDestination);
        serviceSettings.DestinationSettings.Add(destinationSettings);
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public void Start()
    {
        MessageBroker?.Start();
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public void Stop()
    {
        if (MessageBroker != null)
        {
            MessageBroker.Stop();
            MessageBroker = null;
        }
    }

    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    public async Task Service()
    {
        if (MessageBroker == null)
        {
            var msg = __Res.GetString(__Res.MessageBroker_NotAvailable);
            throw new AMFException(msg);
        }

        //This is equivalent to request.getContextPath() (Java) or the HttpRequest.ApplicationPath (.Net).
        var contextPath = HttpContextManager.ContextPath;
        var endpointPath = contextPath + "/Gateway";
        var isSecure = HttpContextManager.IsSecure;

        //http://www.adobe.com/cfusion/knowledgebase/index.cfm?id=e329643d&pss=rss_flex_e329643d
        var endpoint = MessageBroker.GetEndpoint(endpointPath, contextPath, isSecure);
        if (endpoint != null)
            await endpoint.Service();
        else
        {
            var msg = __Res.GetString(__Res.Endpoint_BindFail, endpointPath, contextPath);
            MessageBroker.TraceChannelSettings();
            throw new AMFException(msg);
        }
    }

    #region IDisposable Members

    protected override void Free()
    {
        if (MessageBroker != null) Stop();
    }

    protected override void FreeUnmanaged()
    {
        if (MessageBroker != null) Stop();
    }

    #endregion
}