using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Services.Logging;

public class SimpleFileLogger : ILogger
{
    private const int MaxFileSizeBytes = 10 * 1024 * 1024; // 10MB
    private readonly string _categoryName;
    private readonly Lock _lock = new();
    private readonly string _logFile;

    public SimpleFileLogger(string categoryName, string logDirectory)
    {
        _categoryName = categoryName;
        var fileName = $"shard-{DateTime.Now:yyyy-MM-dd}.log";
        _logFile = Path.Combine(logDirectory, fileName);
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return null;
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        return logLevel >= LogLevel.Debug;
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception,
        Func<TState, Exception?, string> formatter)
    {
        if (!IsEnabled(logLevel)) return;

        try
        {
            var message = formatter(state, exception);
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var level = GetLogLevelString(logLevel);
            var threadId = Thread.CurrentThread.ManagedThreadId;

            var logEntry = $"[{timestamp}] [{level}] [T{threadId:D2}] [{_categoryName}] {message}";

            if (exception != null)
            {
                logEntry += $"{Environment.NewLine}Exception: {exception.GetType().Name}: {exception.Message}";
                if (!string.IsNullOrEmpty(exception.StackTrace))
                    logEntry += $"{Environment.NewLine}StackTrace:{Environment.NewLine}{exception.StackTrace}";

                // Log inner exceptions
                var innerException = exception.InnerException;
                while (innerException != null)
                {
                    logEntry +=
                        $"{Environment.NewLine}Inner Exception: {innerException.GetType().Name}: {innerException.Message}";
                    if (!string.IsNullOrEmpty(innerException.StackTrace))
                        logEntry +=
                            $"{Environment.NewLine}Inner StackTrace:{Environment.NewLine}{innerException.StackTrace}";
                    innerException = innerException.InnerException;
                }
            }

            WriteToFile(logEntry);
        }
        catch
        {
            // Don't throw exceptions from logger
        }
    }

    private void WriteToFile(string logEntry)
    {
        lock (_lock)
        {
            try
            {
                // Check if log rotation is needed
                if (File.Exists(_logFile))
                {
                    var fileInfo = new FileInfo(_logFile);
                    if (fileInfo.Length > MaxFileSizeBytes) RotateLogFile();
                }

                File.AppendAllText(_logFile, logEntry + Environment.NewLine);
            }
            catch
            {
                // Silently ignore write errors
            }
        }
    }

    private void RotateLogFile()
    {
        try
        {
            var directory = Path.GetDirectoryName(_logFile);
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(_logFile);
            var extension = Path.GetExtension(_logFile);
            var timestamp = DateTime.Now.ToString("HHmmss");

            var rotatedFileName = $"{fileNameWithoutExtension}-{timestamp}{extension}";
            var rotatedFilePath = Path.Combine(directory!, rotatedFileName);

            File.Move(_logFile, rotatedFilePath);
        }
        catch
        {
            // If rotation fails, try to delete the current file to prevent it from growing indefinitely
            try
            {
                File.Delete(_logFile);
            }
            catch
            {
                // Ignore deletion errors
            }
        }
    }

    private static string GetLogLevelString(LogLevel logLevel)
    {
        return logLevel switch
        {
            LogLevel.Trace => "TRACE",
            LogLevel.Debug => "DEBUG",
            LogLevel.Information => "INFO ",
            LogLevel.Warning => "WARN ",
            LogLevel.Error => "ERROR",
            LogLevel.Critical => "FATAL",
            LogLevel.None => "NONE ",
            _ => "UNKN "
        };
    }
}