﻿namespace Dolo.Bot.Apple.Hub.Global.Apple;

public partial class Apple
{
    [Command("vote")]

[Description("start a voting the chat")]
    public async Task VoteAsync(SlashCommandContext ctx, [Description("the voting text")] string text)
    {
        await ctx.Interaction.DeferAsync();

        // send the message to the chat
        var msg = await ctx.TryEditResponseAsync($"{HubEmoji.GhostLove} <@{ctx.Member.Id}> » {text}");

        // print error when the message is null
        if (msg is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.GhostLove} » Message could not be created ..");
            return;
        }

        // create the reactions to a message
        await msg.TryCreateReactionAsync(HubEmoji.Yes);
        await msg.TryCreateReactionAsync(HubEmoji.No);
    }
}