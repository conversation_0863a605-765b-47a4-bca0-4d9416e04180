﻿namespace Dolo.Bot.Apple.Hub;

public static class HubRoles
{
    public static DiscordRole? PlutoBeta = Hub.Guild?.Roles[993474722820010074];
    public static DiscordRole? Admin => Hub.Guild?.Roles[748244053916647424];
    public static DiscordRole? Astro => Hub.Guild?.Roles[755454338700411011];
    public static DiscordRole? OG => Hub.Guild?.Roles[828578977258012672];
    public static DiscordRole? Boost => Hub.Guild?.Roles[710946487185571891];
    public static DiscordRole? Low => Hub.Guild?.Roles[771822178299019334];
    public static DiscordRole? High => Hub.Guild?.Roles[771822666067869716];
    public static DiscordRole? Epic => Hub.Guild?.Roles[771822664829894697];
    public static DiscordRole? Banned => Hub.Guild?.Roles[827495833452412939];
    public static DiscordRole? Birthday => Hub.Guild?.Roles[843867154230018118];
    public static DiscordRole? Friend => Hub.Guild?.Roles[716966833189683271];
    public static DiscordRole? Energy => Hub.Guild?.Roles[763820888717656155];
    public static DiscordRole? Female => Hub.Guild?.Roles[828641198469283850];
    public static DiscordRole? Male => Hub.Guild?.Roles[828641518452736000];
    public static DiscordRole? France => Hub.Guild?.Roles[916372363698909254];
    public static DiscordRole? Poland => Hub.Guild?.Roles[916372849529348146];
    public static DiscordRole? Turkey => Hub.Guild?.Roles[916372862846242846];
    public static DiscordRole? Germany => Hub.Guild?.Roles[912042148185116683];
    public static DiscordRole? Changelog => Hub.Guild?.Roles[934285467812241489];
    public static DiscordRole? News => Hub.Guild?.Roles[934285598049574973];
    public static DiscordRole? Event => Hub.Guild?.Roles[934285653426991164];
    public static DiscordRole? Beta => Hub.Guild?.Roles[1066757209578536970];
    public static DiscordRole? Everyone => Hub.Guild?.EveryoneRole;
}