﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using System.Xml;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Contains the properties for configuring destination metadata.
///     This is the <b>metadata</b> element in the services-config.xml file.
/// </summary>
internal sealed class MetadataSettings : Hashtable
{
    private readonly ArrayList _associations = new();

    private MetadataSettings()
    {}

    internal MetadataSettings(XmlNode metadataDefinitionNode)
    {
        foreach (XmlNode propertyNode in metadataDefinitionNode.SelectNodes("*"))
            if (propertyNode.InnerXml != null && propertyNode.InnerXml != string.Empty)
                this[propertyNode.Name] = propertyNode.InnerXml;
            else
            {
                if (propertyNode.Name == "identity")
                    foreach (XmlAttribute attribute in propertyNode.Attributes)
                        Identity.Add(attribute.Value);

                if (propertyNode.Name == "many-to-one")
                {
                    var association = new Hashtable(3);
                    foreach (XmlAttribute attribute in propertyNode.Attributes)
                        association[attribute.Name] = attribute.Value;
                    _associations.Add(association);
                }
            }
    }

    /// <summary>
    ///     Gets the properties to be used to guarantee unique identity among items in a collection of data.
    /// </summary>
    public ArrayList Identity { get; } = new();
}