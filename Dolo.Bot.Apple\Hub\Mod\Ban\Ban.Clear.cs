﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Mod.Ban;

public partial class Ban
{
    [RequirePermissions(DiscordPermission.BanMembers)]
    [Command("clear")]
    [Description("clear all bans from the server")]
    public async Task ClearAsync(SlashCommandContext ctx, [Description("adds all entries in the server-ban list")] bool isBannable)
    {
        await ctx.Interaction.DeferAsync(true);
        await ctx.TryEditResponseAsync("Clearing bans...");

        // load all bans
        var bans = await Hub.Guild!.GetBansAsync();
        if (!bans.Any())
        {
            await ctx.TryEditResponseAsync("No bans to clear");
            return;
        }

        // if not bannable, clear all bans
        if (!isBannable)
        {
            foreach (var member in bans)
                await Hub.Guild.UnbanMemberAsync(member.User.Id);

            await ctx.TryEditResponseAsync("Cleared all bans");
            return;
        }

        foreach (var member in bans)
        {
            var serverMember = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == member.User.Id);
            if (serverMember is { State.IsBanned: true })
            {
                await Hub.Guild.UnbanMemberAsync(member.User.Id);
                continue;
            }

            if (serverMember is { State.IsBanned: false })
            {
                await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, member.User.Id), Builders<ServerMember>.Update
                    .Set(a => a.State.IsBanned, true)
                    .Set(a => a.State.BanReason, "advertise"));

                await Hub.Guild.UnbanMemberAsync(member.User.Id);
                continue;
            }

            await Mongo.ServerMembers.AddAsync(new ServerMember(member.User, true));
            await Hub.Guild.UnbanMemberAsync(member.User.Id);
        }

        await ctx.TryEditResponseAsync("Cleared all with ban roles");
    }
}