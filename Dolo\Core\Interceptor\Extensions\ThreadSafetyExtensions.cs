using System.Runtime.CompilerServices;
using Dolo.Core.Interceptor.Services;
using Microsoft.Extensions.Logging;

namespace Dolo.Core.Interceptor.Extensions;

/// <summary>
/// THREAD SAFETY EXTENSIONS for easy integration of thread safety validation
/// Provides extension methods and helpers for monitoring thread safety across the interceptor
/// </summary>
public static class ThreadSafetyExtensions
{
    private static ThreadSafetyValidator? _globalValidator;
    private static readonly object _validatorLock = new();

    /// <summary>
    /// Initializes global thread safety validation
    /// </summary>
    public static void InitializeThreadSafetyValidation(ILogger<ThreadSafetyValidator> logger)
    {
        lock (_validatorLock)
        {
            _globalValidator?.Dispose();
            _globalValidator = new ThreadSafetyValidator(logger);
        }
    }

    /// <summary>
    /// Gets the global thread safety validator instance
    /// </summary>
    public static ThreadSafetyValidator? GetValidator()
    {
        return _globalValidator;
    }

    /// <summary>
    /// THREAD-SAFE: Validates shared state access with automatic resource name detection
    /// </summary>
    public static void ValidateAccess(this object obj, bool isWrite = false, [CallerMemberName] string operation = "")
    {
        _globalValidator?.ValidateSharedAccess(obj.GetType().Name, operation, isWrite);
    }

    /// <summary>
    /// THREAD-SAFE: Validates shared state access with custom resource name
    /// </summary>
    public static void ValidateAccess(this object obj, string resourceName, bool isWrite = false, [CallerMemberName] string operation = "")
    {
        _globalValidator?.ValidateSharedAccess(resourceName, operation, isWrite);
    }

    /// <summary>
    /// THREAD-SAFE: Validates that a lock is held when accessing shared state
    /// </summary>
    public static void ValidateLockHeld(this object obj, object lockObject, [CallerMemberName] string operation = "")
    {
        _globalValidator?.ValidateLockHeld($"{obj.GetType().Name}.{operation}", lockObject);
    }

    /// <summary>
    /// THREAD-SAFE: Validates atomic counter operations
    /// </summary>
    public static void ValidateAtomicCounter(this object obj, string counterName, long expectedValue, long actualValue)
    {
        _globalValidator?.ValidateAtomicOperation($"{obj.GetType().Name}.{counterName}", expectedValue, actualValue);
    }

    /// <summary>
    /// THREAD-SAFE: Safe increment with validation
    /// </summary>
    public static long SafeIncrement(ref long location, string counterName = "", [CallerMemberName] string operation = "")
    {
        var result = Interlocked.Increment(ref location);
        _globalValidator?.ValidateSharedAccess(counterName.IsNullOrEmpty() ? operation : counterName, "Increment", true);
        return result;
    }

    /// <summary>
    /// THREAD-SAFE: Safe decrement with validation
    /// </summary>
    public static long SafeDecrement(ref long location, string counterName = "", [CallerMemberName] string operation = "")
    {
        var result = Interlocked.Decrement(ref location);
        _globalValidator?.ValidateSharedAccess(counterName.IsNullOrEmpty() ? operation : counterName, "Decrement", true);
        return result;
    }

    /// <summary>
    /// THREAD-SAFE: Safe read with validation
    /// </summary>
    public static long SafeRead(ref long location, string counterName = "", [CallerMemberName] string operation = "")
    {
        var result = Interlocked.Read(ref location);
        _globalValidator?.ValidateSharedAccess(counterName.IsNullOrEmpty() ? operation : counterName, "Read", false);
        return result;
    }

    /// <summary>
    /// THREAD-SAFE: Safe exchange with validation
    /// </summary>
    public static long SafeExchange(ref long location, long value, string counterName = "", [CallerMemberName] string operation = "")
    {
        var result = Interlocked.Exchange(ref location, value);
        _globalValidator?.ValidateSharedAccess(counterName.IsNullOrEmpty() ? operation : counterName, "Exchange", true);
        return result;
    }

    /// <summary>
    /// THREAD-SAFE: Safe compare and exchange with validation
    /// </summary>
    public static long SafeCompareExchange(ref long location, long value, long comparand, string counterName = "", [CallerMemberName] string operation = "")
    {
        var result = Interlocked.CompareExchange(ref location, value, comparand);
        _globalValidator?.ValidateSharedAccess(counterName.IsNullOrEmpty() ? operation : counterName, "CompareExchange", true);
        return result;
    }

    /// <summary>
    /// THREAD-SAFE: Safe add with validation
    /// </summary>
    public static long SafeAdd(ref long location, long value, string counterName = "", [CallerMemberName] string operation = "")
    {
        var result = Interlocked.Add(ref location, value);
        _globalValidator?.ValidateSharedAccess(counterName.IsNullOrEmpty() ? operation : counterName, "Add", true);
        return result;
    }

    /// <summary>
    /// THREAD-SAFE: Executes action with lock validation
    /// </summary>
    public static void WithLock(this object obj, object lockObject, Action action, [CallerMemberName] string operation = "")
    {
        lock (lockObject)
        {
            obj.ValidateLockHeld(lockObject, operation);
            action();
        }
    }

    /// <summary>
    /// THREAD-SAFE: Executes function with lock validation
    /// </summary>
    public static T WithLock<T>(this object obj, object lockObject, Func<T> func, [CallerMemberName] string operation = "")
    {
        lock (lockObject)
        {
            obj.ValidateLockHeld(lockObject, operation);
            return func();
        }
    }

    /// <summary>
    /// THREAD-SAFE: Executes async action with semaphore validation
    /// </summary>
    public static async Task WithSemaphoreAsync(this object obj, SemaphoreSlim semaphore, Func<Task> action, [CallerMemberName] string operation = "")
    {
        await semaphore.WaitAsync().ConfigureAwait(false);
        try
        {
            obj.ValidateAccess($"Semaphore.{operation}", true);
            await action().ConfigureAwait(false);
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// THREAD-SAFE: Executes async function with semaphore validation
    /// </summary>
    public static async Task<T> WithSemaphoreAsync<T>(this object obj, SemaphoreSlim semaphore, Func<Task<T>> func, [CallerMemberName] string operation = "")
    {
        await semaphore.WaitAsync().ConfigureAwait(false);
        try
        {
            obj.ValidateAccess($"Semaphore.{operation}", true);
            return await func().ConfigureAwait(false);
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <summary>
    /// THREAD-SAFE: Validates collection access patterns
    /// </summary>
    public static void ValidateCollectionAccess<T>(this ICollection<T> collection, bool isWrite = false, [CallerMemberName] string operation = "")
    {
        var resourceName = $"{collection.GetType().Name}[{typeof(T).Name}]";
        _globalValidator?.ValidateSharedAccess(resourceName, operation, isWrite);
    }

    /// <summary>
    /// THREAD-SAFE: Validates dictionary access patterns
    /// </summary>
    public static void ValidateDictionaryAccess<TKey, TValue>(this IDictionary<TKey, TValue> dictionary, bool isWrite = false, [CallerMemberName] string operation = "")
    {
        var resourceName = $"{dictionary.GetType().Name}[{typeof(TKey).Name},{typeof(TValue).Name}]";
        _globalValidator?.ValidateSharedAccess(resourceName, operation, isWrite);
    }

    /// <summary>
    /// THREAD-SAFE: Validates event handler access
    /// </summary>
    public static void ValidateEventAccess(this object obj, string eventName, bool isSubscription = false)
    {
        var operation = isSubscription ? "Subscribe" : "Invoke";
        _globalValidator?.ValidateSharedAccess($"{obj.GetType().Name}.{eventName}", operation, isSubscription);
    }

    /// <summary>
    /// Helper method to check if string is null or empty
    /// </summary>
    private static bool IsNullOrEmpty(this string? value)
    {
        return string.IsNullOrEmpty(value);
    }

    /// <summary>
    /// Disposes the global thread safety validator
    /// </summary>
    public static void DisposeThreadSafetyValidation()
    {
        lock (_validatorLock)
        {
            _globalValidator?.Dispose();
            _globalValidator = null;
        }
    }
}

/// <summary>
/// THREAD-SAFE WRAPPER for objects that need thread safety validation
/// </summary>
public sealed class ThreadSafeWrapper<T> : IDisposable where T : class
{
    private readonly T _wrapped;
    private readonly object _lock = new();
    private readonly string _resourceName;
    private bool _disposed;

    public ThreadSafeWrapper(T wrapped, string? resourceName = null)
    {
        _wrapped = wrapped;
        _resourceName = resourceName ?? typeof(T).Name;
    }

    /// <summary>
    /// Executes action with thread safety validation
    /// </summary>
    public void Execute(Action<T> action, [CallerMemberName] string operation = "")
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ThreadSafeWrapper<T>));

        this.WithLock(_lock, () =>
        {
            this.ValidateAccess(_resourceName, true, operation);
            action(_wrapped);
        }, operation);
    }

    /// <summary>
    /// Executes function with thread safety validation
    /// </summary>
    public TResult Execute<TResult>(Func<T, TResult> func, [CallerMemberName] string operation = "")
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ThreadSafeWrapper<T>));

        return this.WithLock(_lock, () =>
        {
            this.ValidateAccess(_resourceName, false, operation);
            return func(_wrapped);
        }, operation);
    }

    /// <summary>
    /// Gets the wrapped object for read-only access
    /// </summary>
    public T ReadOnly([CallerMemberName] string operation = "")
    {
        if (_disposed) throw new ObjectDisposedException(nameof(ThreadSafeWrapper<T>));
        
        this.ValidateAccess(_resourceName, false, operation);
        return _wrapped;
    }

    public void Dispose()
    {
        if (_disposed) return;
        _disposed = true;

        if (_wrapped is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}
