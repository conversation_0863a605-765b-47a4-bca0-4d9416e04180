﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Messaging;

/// <summary>
///     A listener that wants to listen to events when provider/consumer connects to or disconnects from a specific pipe.
/// </summary>
internal interface IPipeConnectionListener
{
    /// <summary>
    ///     Pipe connection event handler.
    /// </summary>
    /// <param name="evt">Pipe connection event.</param>
    void OnPipeConnectionEvent(PipeConnectionEvent evt);
}