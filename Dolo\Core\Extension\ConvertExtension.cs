﻿namespace Dolo.Core.Extension;

public static class ConvertExtension
{
    /// <summary>
    ///     Is a specific type
    /// </summary>
    /// <param name="value"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static bool Is<T>(this object? value) => value?.GetType() == typeof(T);

    /// <summary>
    ///     Parse string value into any type
    /// </summary>
    /// <param name="value"></param>
    /// <param name="formatProvider"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static T Parse<T>(this string? value, IFormatProvider? formatProvider = null) where T : IParsable<T> => T.Parse(value!, formatProvider);

    /// <summary>
    ///     Converts to given type
    /// </summary>
    /// <param name="value"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static T? To<T>(this object? value) => TryIt.This(() => {
        return typeof(T) switch
        {
            var t when t == typeof(string)   => (T)(object)value?.ToString(),
            var t when t == typeof(int)      => int.TryParse(value?.ToString(), out var intValue) ? (T)(object)intValue : default,
            var t when t == typeof(long)     => long.TryParse(value?.ToString(), out var longValue) ? (T)(object)longValue : default,
            var t when t == typeof(double)   => double.TryParse(value?.ToString(), out var doubleValue) ? (T)(object)doubleValue : default,
            var t when t == typeof(float)    => float.TryParse(value?.ToString(), out var floatValue) ? (T)(object)floatValue : default,
            var t when t == typeof(decimal)  => decimal.TryParse(value?.ToString(), out var decimalValue) ? (T)(object)decimalValue : default,
            var t when t == typeof(bool)     => bool.TryParse(value?.ToString(), out var boolValue) ? (T)(object)boolValue : default,
            var t when t == typeof(DateTime) => DateTime.TryParse(value?.ToString(), out var dateTimeValue) ? (T)(object)dateTimeValue : default,
            var t when t == typeof(TimeSpan) => TimeSpan.TryParse(value?.ToString(), out var timeSpanValue) ? (T)(object)timeSpanValue : default,
            var t when t == typeof(Guid)     => Guid.TryParse(value?.ToString(), out var guidValue) ? (T)(object)guidValue : default,
            var t when t == typeof(char)     => char.TryParse(value?.ToString(), out var charValue) ? (T)(object)charValue : default,
            var t when t == typeof(byte)     => byte.TryParse(value?.ToString(), out var byteValue) ? (T)(object)byteValue : default,
            var t when t == typeof(sbyte)    => sbyte.TryParse(value?.ToString(), out var sbyteValue) ? (T)(object)sbyteValue : default,
            var t when t == typeof(short)    => short.TryParse(value?.ToString(), out var shortValue) ? (T)(object)shortValue : default,
            var t when t == typeof(ushort)   => ushort.TryParse(value?.ToString(), out var ushortValue) ? (T)(object)ushortValue : default,
            var t when t == typeof(uint)     => uint.TryParse(value?.ToString(), out var uintValue) ? (T)(object)uintValue : default,
            var t when t == typeof(ulong)    => ulong.TryParse(value?.ToString(), out var ulongValue) ? (T)(object)ulongValue : default,
            var t when t == typeof(object)   => (T)value,

            _ => default
        };
    }, Console.WriteLine);
}