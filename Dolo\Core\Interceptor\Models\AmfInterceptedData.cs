using Dolo.Core.AMF3.Fluorine.IO;
using Dolo.Core.Interceptor.Interfaces;

namespace Dolo.Core.Interceptor.Models;

public sealed class AmfInterceptedData : IAmfInterceptedData
{
    public string Direction { get; init; } = string.Empty;
    public string Method { get; init; } = string.Empty;
    public string Uri { get; init; } = string.Empty;
    public string ContentType { get; init; } = string.Empty;
    public int StatusCode { get; init; }
    public AMFContent? DecodedContent { get; init; }
    public byte[]? RawContent { get; init; }
    public Dictionary<string, string[]> Headers { get; init; } = new();
    public DateTime Timestamp { get; init; }
    public string? Error { get; init; }
    public bool IsSuccess => string.IsNullOrEmpty(Error);
    public int ContentLength => RawContent?.Length ?? 0;
    
    public string GetHeaderValue(string name) =>
        Headers.TryGetValue(name, out var values) ? values.FirstOrDefault() ?? string.Empty : string.Empty;
}
