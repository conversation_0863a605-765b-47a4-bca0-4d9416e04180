﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Services;
using Dolo.Core.AMF3.Fluorine.Messaging.Services.Remoting;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Config;

/// <summary>
///     Represents a configuration class that contains information about the services-config.xml file.
/// </summary>
internal sealed class ServiceConfigSettings
{

    internal ServiceConfigSettings()
    {
        ChannelsSettings = new();
        FactoriesSettings = new();
        ServiceSettings = new();
    }

    /// <summary>
    ///     Gets flex client settings.
    /// </summary>
    public FlexClientSettings FlexClientSettings
    {
        get;
        private set;
    }
    /// <summary>
    ///     Gets channel definitions.
    /// </summary>
    public ChannelSettingsCollection ChannelsSettings
    {
        get;
    }
    /// <summary>
    ///     Gets factory definitions.
    /// </summary>
    public FactorySettingsCollection FactoriesSettings
    {
        get;
    }
    /// <summary>
    ///     Gets service settings.
    /// </summary>
    public ServiceSettingsCollection ServiceSettings
    {
        get;
    }

    /// <summary>
    ///     Loads a services-config.xml file.
    /// </summary>
    /// <param name="configPath">Path to the file.</param>
    /// <param name="configFileName">Service configuration file name.</param>
    /// <returns>A ServiceConfigSettings instance loaded from the specified file.</returns>
    public static ServiceConfigSettings Load()
    {
        var serviceConfigSettings = new ServiceConfigSettings();

        //Create a default amf channel
        var channelSettings = new ChannelSettings("my-amf", "flex.messaging.endpoints.AMFEndpoint", @"http://{server.name}:{server.port}/{context.root}/gateway");
        serviceConfigSettings.ChannelsSettings.Add(channelSettings);

        var serviceSettings = new ServiceSettings(serviceConfigSettings, RemotingService.RemotingServiceId, typeof(RemotingService).FullName);
        var messageType = "flex.messaging.messages.RemotingMessage";
        var typeName = AMFConfiguration.Instance.ClassMappings.GetType(messageType);
        serviceSettings.SupportedMessageTypes[messageType] = typeName;
        serviceConfigSettings.ServiceSettings.Add(serviceSettings);

        var adapterSettings = new AdapterSettings("dotnet", typeof(RemotingAdapter).FullName, true);
        serviceSettings.DefaultAdapter = adapterSettings;

        var destinationSettings = new DestinationSettings(serviceSettings, DestinationSettings.AMFDestination, adapterSettings, "*");
        serviceSettings.DestinationSettings.Add(destinationSettings);

        serviceConfigSettings.FlexClientSettings = new();

        return serviceConfigSettings;
    }
}