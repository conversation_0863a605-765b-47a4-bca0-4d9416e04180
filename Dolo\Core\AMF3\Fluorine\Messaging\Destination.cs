﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Services;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

/// <summary>
///     The Destination class is a source and sink for messages sent through
///     a service destination and uses an adapter to process messages.
/// </summary>
internal class Destination
{
    /// <summary>
    ///     ServiceAdapter for the Destination.
    /// </summary>
    protected ServiceAdapter _adapter;

    private FactoryInstance _factoryInstance;

    /// <summary>
    ///     Service managing this Destination.
    /// </summary>
    protected IService _service;

    /// <summary>
    ///     Destination settings.
    /// </summary>
    protected DestinationSettings _settings;

    private Destination()
    {}

    internal Destination(IService service, DestinationSettings settings)
    {
        _service = service;
        _settings = settings;
    }

    /// <summary>
    ///     Gets the Destination identity.
    /// </summary>
    public string Id => _settings.Id;

    /// <summary>
    ///     Gets the Destination's factory property.
    /// </summary>
    public string FactoryId
    {
        get
        {
            if (_settings.Properties.Contains("factory"))
                return _settings.Properties["factory"] as string;
            return "dotnet";
        }
    }

    /// <summary>
    ///     Returns the Service managing this Destination.
    /// </summary>
    public Dolo.Core.AMF3.Fluorine.Messaging.Services.IService Service => _service;

    /// <summary>
    ///     Gets the ServiceAdapter used by the Destination for message processing.
    /// </summary>
    public ServiceAdapter ServiceAdapter => _adapter;

    /// <summary>
    ///     Gets the Destination settings.
    /// </summary>
    public DestinationSettings DestinationSettings => _settings;

    /// <summary>
    ///     Gets the source property where applicable.
    /// </summary>
    public string Source
    {
        get
        {
            if (_settings is { Properties: {} }) return _settings.Properties["source"] as string;
            return null;
        }
    }

    /// <summary>
    ///     Gets the scope property where applicable.
    /// </summary>
    public string Scope
    {
        get
        {
            if (_settings is { Properties: {} }) return _settings.Properties["scope"] as string;
            return "request";
        }
    }

    internal void Init(AdapterSettings adapterSettings)
    {
        if (adapterSettings != null)
        {
            var typeName = adapterSettings.Class;
            var type = ObjectFactory.Locate(typeName);
            if (type != null)
            {
                _adapter = ObjectFactory.CreateInstance(type) as ServiceAdapter;
                _adapter.SetDestination(this);
                _adapter.SetAdapterSettings(adapterSettings);
                _adapter.SetDestinationSettings(_settings);
                _adapter.Init();
            }
        }

        var messageBroker = Service.GetMessageBroker();
        messageBroker.RegisterDestination(this, _service);

        //If the source has application scope create an instance here, so the service can listen for SessionCreated events for the first request
        if (Scope == "application")
        {
            var factoryInstance = GetFactoryInstance();
            var inst = factoryInstance.Lookup();
        }
    }

    /// <summary>
    ///     Returns the FactoryInstance used by the Destination to create object instances.
    /// </summary>
    /// <returns></returns>
    public FactoryInstance GetFactoryInstance()
    {
        if (_factoryInstance != null)
            return _factoryInstance;

        var messageBroker = Service.GetMessageBroker();
        var factory = messageBroker.GetFactory(FactoryId);
        var properties = _settings.Properties;
        _factoryInstance = factory.CreateFactoryInstance(Id, properties);
        return _factoryInstance;
    }
}