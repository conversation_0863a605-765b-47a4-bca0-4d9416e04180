﻿using Dolo.Database;
using System.Text;
namespace Dolo.Bot.Apple.Hub.Global.Birthday;

[Command("bday")]
[Description("Birthday commands")]
public partial class Birthday 
{
    [Command("list")]
[Description("list all birthdays")]
    public async Task BdayAsync(SlashCommandContext ctx)
    {
        await ctx.Interaction.DeferAsync(ctx.Channel == HubChannel.Birthday!);

        // get server members that has birthdays set
        var serverMembers = await Mongo.ServerMembers.GetAsync(a => a.Birthday != null);
        var members = serverMembers.Where(m => m.Birthday is {} && Hub.Guild!.Members.ContainsKey(m.Member.DiscordId)).ToArray();
        if (!members.Any())
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » No birthdays found.");
            return;
        }

        // sort the members birthday and only take 20
        var sorted = members.OrderBy(m => m.Birthday?.DayOfBirth).Take(20).ToArray();
        if (!sorted.Any())
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » No birthdays found.");
            return;
        }

        // builder
        var sb = new StringBuilder()
            .AppendLine($"**{sorted.Length}** / **{members.Length}**");

        // illiterate through the members and build the response
        foreach (var member in sorted)
            sb.AppendLine($"{HubEmoji.Cake?.ToString()} » **{member.Member.Username}** » `{member.Birthday?.DayOfBirth.AddHours(2).GetTimeString()}`");

        // get how many are left remaining
        if (members.Length - 20 > 0)
            sb.AppendLine($"... {members.Length - 20} more users");

        await ctx.TryEditResponseAsync(sb.ToString());
    }
}