namespace Dolo.Core.Cryption;

public static class CustomEncryptor
{
    // Base64-encoded AES-encrypted dictionary (replace with actual encrypted string)
    private const string Algorithm = "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";

    private static readonly byte[] Alphabet =
        Encoding.UTF8.GetBytes("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");

    private static Dictionary<string, string>? _encryptDictionary;
    private static Dictionary<string, string>? _decryptDictionary;
    private static readonly object _lockObject = new();

    /// <summary>
    /// Initializes the encryption and decryption dictionaries by decrypting the Algorithm string.
    /// </summary>
    private static void InitializeDictionaries()
    {
        if (_encryptDictionary != null) return;

        lock (_lockObject)
        {
            if (_encryptDictionary != null) return;

            var reversedAlphabet = new string(Encoding.UTF8.GetString(Alphabet).Reverse().ToArray());
            var key = Encoding.UTF8.GetBytes(reversedAlphabet).Take(16).ToArray();
            var iv = new byte[16]; // Fixed IV (all zeros)

            var encryptedBytes = System.Convert.FromBase64String(Algorithm);
            var decryptedJson = AesDecrypt(encryptedBytes, key, iv) 
                ?? throw new InvalidOperationException("Failed to decrypt the dictionary.");

            _encryptDictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(decryptedJson) 
                ?? throw new InvalidOperationException("Failed to deserialize the dictionary.");

            _decryptDictionary = _encryptDictionary.ToDictionary(x => x.Value, x => x.Key);
        }
    }

    /// <summary>
    /// Decrypts the provided byte array using AES.
    /// </summary>
    private static string? AesDecrypt(byte[] encrypted, byte[] key, byte[] iv)
    {
        try
        {
            using var aes = Aes.Create();
            aes.Key = key;
            aes.IV = iv;
            
            using var decryptor = aes.CreateDecryptor();
            using var ms = new MemoryStream(encrypted);
            using var cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read);
            using var sr = new StreamReader(cs);
            
            return sr.ReadToEnd();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Encrypts the input string using the custom dictionary.
    /// </summary>
    public static string? DoEncrypt(string? input)
    {
        if (string.IsNullOrWhiteSpace(input)) return null;

        InitializeDictionaries();

        var builder = new StringBuilder(input.Length * 2); // Pre-allocate capacity
        
        foreach (char c in input)
        {
            if (_encryptDictionary!.TryGetValue(c.ToString(), out var code))
                builder.Append(code);
            else
                builder.Append(c); // Keep unsupported characters as-is
        }
        
        return builder.ToString();
    }

    /// <summary>
    /// Decrypts the input string using the custom dictionary.
    /// </summary>
    public static string? DoDecrypt(string? input)
    {
        if (string.IsNullOrWhiteSpace(input)) return null;

        InitializeDictionaries();

        var builder = new StringBuilder();
        int i = 0;

        while (i < input.Length)
        {
            if (input[i] == ':')
            {
                bool matched = false;

                // Try longest possible match first
                foreach (var key in _decryptDictionary!.Keys.OrderByDescending(k => k.Length))
                {
                    if (i + key.Length <= input.Length && 
                        input.Substring(i, key.Length) == key)
                    {
                        builder.Append(_decryptDictionary[key]);
                        i += key.Length;
                        matched = true;
                        break;
                    }
                }

                if (!matched)
                {
                    // Colon not part of any emoji sequence, treat as raw character
                    builder.Append(input[i]);
                    i++;
                }
            }
            else
            {
                // Not an emoji code, just append
                builder.Append(input[i]);
                i++;
            }
        }

        return builder.ToString();
    }
}
