namespace Dolo.Core.Extension;

public static class ListExtension
{
    /// <summary>
    ///     Empty the list
    /// </summary>
    /// <param name="list"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static List<T> Empty<T>(this List<T> list)
    {
        list.Empty();
        return list;
    }

    /// <summary>
    ///     Extension method to take and remove items from a list
    /// </summary>
    /// <param name="list"></param>
    /// <param name="count"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static List<T> TakeAndRemove<T>(this List<T> list, int count)
    {
        var countToTake = count > list.Count ? list.Count : count;
        var result = list.Take(countToTake).ToList();
        if (result.Count > 0)
            list.RemoveRange(0, result.Count);
        else
            list.Clear();

        return result;
    }

    /// <summary>
    ///     Prepends an item to a list and returns it
    /// </summary>
    /// <param name="list"></param>
    /// <param name="item"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static List<T> Prepending<T>(this List<T> list, T item)
    {
        list.Insert(0, item);
        return list;
    }

    /// <summary>
    ///     Reverse an list and return it
    /// </summary>
    /// <param name="list"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static List<T> Reversing<T>(this List<T> list)
    {
        list.Reverse();
        return list;
    }

    /// <summary>
    ///     Reverse a byte array and return it
    /// </summary>
    /// <param name="list"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static T[] Reversing<T>(this T[] array)
    {
        array.Reverse();
        return array;
    }

    /// <summary>
    ///     Extension method to check if many items contains in a list
    /// </summary>
    /// <param name="list"></param>
    /// <param name="values"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static bool ContainsMany<T>(this IEnumerable<T> list, params T[] values)
        => values.Any(list.Contains);

    /// <summary>
    ///     Extension to randomize a list of items
    /// </summary>
    public static List<T> Shuffle<T>(this List<T> list)
        => list.OrderBy(_ => RandomNumberGenerator.GetInt32(0, list.Count)).ToList();

    /// <summary>
    ///     Extension method to try to replace an item in a list
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="list"></param>
    /// <param name="oldItem"></param>
    /// <param name="newItem"></param>
    /// <returns></returns>
    public static bool TryReplace<T>(this List<T> list, T oldItem, T newItem)
    {
        var index = list.IndexOf(oldItem);
        if (index != -1)
            list[index] = newItem;

        return index != -1;
    }

    /// <summary>
    ///     Tries to get a item in a list using the index
    /// </summary>
    /// <param name="list"></param>
    /// <param name="index"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static T? TryGet<T>(this List<T> list, int index)
        => list.Count > index ? list[index] : default;

    /// <summary>
    ///     Tries to add a range of items to a list
    /// </summary>
    public static List<T> TryAddRange<T>(this List<T> list, IEnumerable<T> items)
    {
        var uniqueItems = items.Where(x => !list.Contains(x)).ToList();
        if (uniqueItems.Any())
            list.AddRange(uniqueItems);

        return list.Distinct().ToList();
    }

    /// <summary>
    ///     Converts a IAsyncEnumerable to a List
    /// </summary>
    public static async Task<List<T>> ToListAsync<T>(this IAsyncEnumerable<T> asyncEnumerable)
    {
        var list = new List<T>();
        await foreach (var item in asyncEnumerable)
            list.Add(item);

        return list;
    }
}