﻿using Dolo.Core.Discord.Entities;

namespace Dolo.Core.Discord;

public static class DiscordGuildExtension
{
    /// <summary>
    ///     Try to get a member from the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="id">The member's ID</param>
    /// <param name="member">The output member if found</param>
    /// <returns>True if member was found, false otherwise</returns>
    public static bool TryGetMember(this DiscordGuild guild, ulong id, out DiscordMember member)
    {
        member = guild.Members.FirstOrDefault(a => a.Value.Id == id).Value;
        return member != null;
    }

    /// <summary>
    ///     Try to get a member
    /// </summary>
    public static DiscordMember? TryGetMember(this DiscordGuild guild, ulong id)
    {
        var member = guild.Members.FirstOrDefault(a => a.Value.Id == id).Value;
        return member;
    }

    /// <summary>
    ///     Try to get the member from the server
    /// </summary>
    public static async Task<DiscordMember?> TryGetMemberAsync(this DiscordGuild guild, ulong id)
        => guild.TryGetMember(id, out var member) ? member : await guild.GetMemberAsync(id);

    /// <summary>
    ///     Try to get all member from the server
    /// </summary>
    public static async Task<IReadOnlyCollection<DiscordMember>> TryGetAllMemberAsync(this DiscordGuild guild)
    {
        var members = new List<DiscordMember>();
        await foreach (var member in guild.GetAllMembersAsync())
            members.Add(member);
        return members;
    }
  
   /// <summary>
    ///     Try to get a channel from the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="id">The channel ID</param>
    /// <returns>The channel if found, null otherwise</returns> 
   public static DiscordChannel? TryGetChannel(this DiscordGuild guild, ulong? id)
        => guild.Channels.FirstOrDefault(a => a.Key == id).Value;

    /// <summary>
    ///     Try to get a channel from the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="id">The channel ID</param>
    /// <param name="onError">Optional action to handle exceptions</param>
    /// <returns>The channel if found, null otherwise</returns>
    public static async Task<DiscordChannel?> TryGetChannelAsync(this DiscordGuild guild,
        ulong? id,
        Action<Exception>? onError = null)
        => await Task.FromResult(guild.Channels.FirstOrDefault(a => a.Key == id).Value).TryAsync(onError);

    /// <summary>
    ///     Try to get invites from the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="onError">Optional action to handle exceptions</param>
    /// <returns>List of invites if successful, null otherwise</returns>
    public static async Task<IReadOnlyList<DiscordInvite>?> TryGetInvitesAsync(this DiscordGuild guild,
        Action<Exception>? onError = null)
        => await guild.GetInvitesAsync().TryAsync(onError);

    /// <summary>
    ///     Try to create a category channel in the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="name">The category name</param>
    /// <param name="onError">Optional action to handle exceptions</param>
    /// <returns>The created category if successful, null otherwise</returns>
    public static async Task<DiscordChannel?> TryCreateCategoryAsync(this DiscordGuild guild,
        string name,
        Action<Exception>? onError = null)
        => await guild.CreateChannelCategoryAsync(name).TryAsync(onError);

    /// <summary>
    ///     Try to create a voice channel
    /// </summary>
    /// <returns></returns>
    public static async Task<DiscordChannel?> TryCreateVoiceChannelAsync(this DiscordGuild guild, string name,
        DiscordChannel? parent = null, int? bitrate = null, int? userLimit = null,
        IEnumerable<DiscordOverwriteBuilder>? overwrites = null, DiscordVideoQualityMode? qualityMode = null,
        int? position = null, string? reason = null)
        => await guild.CreateVoiceChannelAsync(name, parent, bitrate, userLimit, overwrites, qualityMode, position, reason).TryAsync();

    /// <summary>
    ///     Try to create a voice channel in the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="options">The voice channel options</param>
    /// <param name="onError">Optional action to handle exceptions</param>
    /// <returns>The created voice channel if successful, null otherwise</returns>
    public static async Task<DiscordChannel?> TryCreateVoiceChannelAsync(this DiscordGuild guild,
        Action<DiscordVoiceChannelOption> options,
        Action<Exception>? onError = null)
    {
        var opt = options.GetAction();
        return await guild.CreateVoiceChannelAsync(
            opt.Name,
            opt.Parent,
            opt.Bitrate,
            opt.UserLimit,
            opt.Overwrites,
            opt.QualityMode,
            opt.Position,
            opt.Reason
        ).TryAsync(onError);
    }

    /// <summary>
    ///     Try to create a text channel
    /// </summary>
    /// <returns></returns>
    public static async Task<DiscordChannel?> TryCreateTextChannelAsync(this DiscordGuild guild, string name, DiscordChannel? parent = null, DSharpPlus.Entities.Optional<string> topic = default, IEnumerable<DiscordOverwriteBuilder>? overwrites = null, bool? nsfw = null, DSharpPlus.Entities.Optional<int?> perUserRateLimit = default, int? position = null, string? reason = null)
        => await guild.CreateTextChannelAsync(name, parent, topic, overwrites, nsfw, perUserRateLimit, position, reason).TryAsync();

    /// <summary>
    ///     Try to create a text channel in the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="options">The text channel options</param>
    /// <param name="onError">Optional action to handle exceptions</param>
    /// <returns>The created text channel if successful, null otherwise</returns>
    public static async Task<DiscordChannel?> TryCreateTextChannelAsync(this DiscordGuild guild,
        Action<DiscordTextChannelOption> options,
        Action<Exception>? onError = null)
    {
        var opt = options.GetAction();
        return await guild.CreateTextChannelAsync(
            opt.Name,
            opt.Parent,
            opt.Topic,
            opt.Overwrites,
            opt.Nsfw,
            opt.PerUserRateLimit,
            opt.Position,
            opt.Reason
        ).TryAsync(onError);
    }

    /// <summary>
    ///   Try to get a ban from the server
    ///  </summary>
    public static async Task<DiscordBan?> TryGetBanAsync(this DiscordGuild guild, DiscordUser user)
        => await guild.GetBanAsync(user).TryAsync();

    /// <summary>
    ///     Try to get a ban from the guild
    /// </summary>
    /// <param name="guild">The Discord guild</param>
    /// <param name="user">The user to check for ban</param>
    /// <param name="onError">Optional action to handle exceptions</param>
    /// <returns>The ban information if found, null otherwise</returns>
    public static async Task<DiscordBan?> TryGetBanAsync(this DiscordGuild guild,
        DiscordUser user,
        Action<Exception>? onError = null)
        => await guild.GetBanAsync(user).TryAsync(onError);
}