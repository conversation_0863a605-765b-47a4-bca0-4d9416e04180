﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.IO.Bytecode;

/// <summary>
///     Reflection optimizer interface.
/// </summary>
internal interface IReflectionOptimizer
{
    /// <summary>
    ///     Performs instantiation of an instance of the underlying class.
    /// </summary>
    /// <returns>The new instance.</returns>
    object CreateInstance();
    /// <summary>
    ///     This method supports the AMFCore infrastructure and is not intended to be used directly from your code.
    /// </summary>
    /// <param name="reader"></param>
    /// <param name="classDefinition"></param>
    /// <returns></returns>
    object ReadData(AMFReader reader, ClassDefinition classDefinition);
}