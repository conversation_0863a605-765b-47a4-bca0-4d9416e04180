using Dolo.Pluto.Shard.Configuration;
using Dolo.Pluto.Shard.Hub;
using Dolo.Pluto.Shard.Services.Api;
using Dolo.Pluto.Shard.Services.License;

namespace Dolo.Pluto.Shard.Services.Initialization;

public class InitializationWorkflow(
    IToolboxApiService toolboxApiService,
    ILicenseService licenseService,
    IMainService mainService,
    IAppConfiguration appConfiguration,
    IInitializationStateManager stateManager,
    IInitializationProgressTracker progressTracker,
    AuthenticationService authenticationService) : IInitializationWorkflow
{
    public event Action? OnInitializationCompleted;

    public async Task<bool> ExecuteInitializationAsync(string toolName)
    {
        try
        {
            progressTracker.UpdateProgress("Connecting to server...", 15);
            await Task.Delay(1000);

            progressTracker.UpdateProgress("Fetching toolbox data...", 35);
            var toolbox = await toolboxApiService.GetToolboxAsync();
            if (toolbox == null)
            {
                stateManager.ShowMaintenanceState("Unable to connect to the server. Please try again later.");
                return false;
            }

            progressTracker.UpdateProgress($"Looking for {toolName} tool...", 65);
            await Task.Delay(800);

            mainService.Tool = toolbox.Tools?.FirstOrDefault(t => t.Name?.ToLower() == toolName.ToLower());
            if (mainService.Tool?.Windows == null)
            {
                stateManager.ShowMaintenanceState($"{toolName} tool is not available. Please contact support.");
                return false;
            }

            if (mainService.Tool.Windows.Maintenance.Enabled)
            {
                stateManager.ShowMaintenanceState(mainService.Tool.Windows.Maintenance.Message ?? "The tool is currently under maintenance. Please check back later.");
                return false;
            }

            progressTracker.UpdateProgress("Checking for updates...", 85);
            await Task.Delay(800);

            if (mainService.Tool.Windows.Version != null &&
                new Version(appConfiguration.AppVersion) < new Version(mainService.Tool.Windows.Version))
            {
                stateManager.ShowUpdateState($"Your current version ({appConfiguration.AppVersion}) is outdated. Please update to version {mainService.Tool.Windows.Version} or later.");
                return false;
            }

            progressTracker.UpdateProgress("Verifying license...", 95);
            var licenseResult = await licenseService.IsAllowedToUseToolAsync();
            if (!licenseResult.IsValid)
            {
                stateManager.ShowLicenseState(licenseResult.ErrorMessage ?? $"Please verify your license to continue using Pluto {toolName}.");
                return false;
            }

            mainService.StateHasChangedAsync();
            stateManager.Hide();
            OnInitializationCompleted?.Invoke();
            return true;
        }
        catch (Exception ex)
        {
            stateManager.ShowErrorState($"Initialization failed: {ex.Message}");
            return false;
        }
    }

    public async Task StartVerificationAsync()
    {
        try
        {
            stateManager.IsVerifying = true;
            stateManager.StatusBadgeText = "Verifying...";
            stateManager.OperationDetail = "Please check your browser.";
            stateManager.MessageArea = "The authentication process has started in your web browser. Please complete it there.";

            AuthenticationHub.OnTokenProcessed = async (result) =>
            {
                if (result.IsValid)
                {
                    stateManager.IsVerifying = false;
                    mainService.StateHasChangedAsync();
                    stateManager.Hide();
                    OnInitializationCompleted?.Invoke();
                }
                else
                {
                    stateManager.ShowLicenseState(result.ErrorMessage ?? "License verification failed. Please try again.");
                    stateManager.IsVerifying = false;
                }
            };

            await licenseService.LaunchAuthenticationAsync();
        }
        catch (Exception ex)
        {
            stateManager.ShowErrorState($"Verification failed to start: {ex.Message}");
            stateManager.IsVerifying = false;
        }
    }
}
