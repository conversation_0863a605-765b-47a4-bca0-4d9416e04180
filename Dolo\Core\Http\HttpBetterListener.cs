﻿namespace Dolo.Core.Http;

public class HttpBetterListener
{
    private readonly HttpListener _listener = new();
    public AsyncEventHandler<HttpListener, HttpListenerContext>? OnContext;

    /// <summary>
    ///     Add a prefix to the listener
    /// </summary>
    public HttpBetterListener AddPrefix(string prefix)
    {
        _listener.Prefixes.Add(prefix);
        return this;
    }

    /// <summary>
    ///     Start the listener
    /// </summary>
    /// <returns></returns>
    public async Task<HttpBetterListener> StartAsync(Action<HttpListener, HttpListenerContext>? action = null)
    {
        _listener.Start();
        await ListenAsync(action);
        return this;
    }

    /// <summary>
    ///     Listen for incoming requests
    /// </summary>
    /// <returns></returns>
    private Task ListenAsync(Action<HttpListener, HttpListenerContext>? action = null)
        => Task.Factory.StartNew(async () => {
            while (_listener.IsListening)
            {
                var context = await _listener.GetContextAsync();
                if (OnContext != null)
                    await OnContext.InvokeAsync(_listener, context);

                action?.Invoke(_listener, context);
            }
        });
}