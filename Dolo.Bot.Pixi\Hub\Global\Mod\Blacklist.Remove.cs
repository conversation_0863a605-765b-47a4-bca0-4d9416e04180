﻿using Dolo.Core.Discord;
using Dolo.Database;
using Dolo.Pluto.Shard.Bot.Pixi;
using MongoDB.Driver;
namespace Dolo.Bot.Pixi.Hub.Global.Mod;

public partial class Mod
{
    public partial class Blacklist
    {
        [Command("unban")]
        [Description("remove a user from the blacklist")]
        public async Task RemoveAsync(SlashCommandContext ctx,
            [Description("the user that should be removed from the blacklist")] DiscordUser user)
        {
            if (ctx.User.Id != 440584675740876810) return;

            await ctx.TryDeferAsync(true);
            await ctx.TryEditResponseAsync("Trying to remove blacklist user ..");
            var config = await Mongo.PixiSettings.GetFirstAsync();
            if (config is null) return;

            if (!config.HasBlockUser(user.Id))
            {
                await ctx.TryEditResponseAsync("User is not blacklisted");
                return;
            }

            config.RemoveBlockUser(user.Id);
            await Mongo.PixiSettings.UpdateAsync(Builders<PixiSettings>.Filter.Empty,
            Builders<PixiSettings>.Update.Set(x => x.Users, config.Users));
            await ctx.TryEditResponseAsync($"User `{user.Mention}` has been removed from the blacklist");
        }
    }
}