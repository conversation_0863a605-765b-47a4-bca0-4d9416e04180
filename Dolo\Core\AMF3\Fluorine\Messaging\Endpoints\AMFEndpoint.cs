﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Messaging.Config;
using Dolo.Core.AMF3.Fluorine.Messaging.Endpoints.Filter;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
using Dolo.Core.AMF3.Fluorine.Util;
namespace Dolo.Core.AMF3.Fluorine.Messaging.Endpoints;

/// <summary>
///     This type supports the AMFCore infrastructure and is not intended to be used directly from your code.
/// </summary>
internal class AMFEndpoint : EndpointBase
{
    private const string LegacyCollectionKey = "legacy-collection";
    private readonly AtomicInteger _waitingPollRequests;
    private FilterChain _filterChain;

    public AMFEndpoint(MessageBroker messageBroker, ChannelSettings channelSettings) : base(messageBroker, channelSettings) => _waitingPollRequests = new();

    public override void Start()
    {
        var deserializationFilter = new DeserializationFilter();
        deserializationFilter.UseLegacyCollection = GetIsLegacyCollection();
        var serviceMapFilter = new ServiceMapFilter(this);
        var processFilter = new ProcessFilter(this);
        var messageFilter = new MessageFilter(this);
        var serializationFilter = new SerializationFilter();
        serializationFilter.UseLegacyCollection = GetIsLegacyCollection();

        deserializationFilter.Next = serviceMapFilter;
        serviceMapFilter.Next = processFilter;
        processFilter.Next = messageFilter;
        messageFilter.Next = serializationFilter;

        _filterChain = new(deserializationFilter);
    }

    public bool GetIsLegacyCollection()
    {
        if (!_channelSettings.Contains(LegacyCollectionKey))
            return false;
        var value = _channelSettings[LegacyCollectionKey] as string;
        var isLegacy = Util.Convert.ToBoolean(value);
        return isLegacy;
    }

    public override void Stop()
    {
        _filterChain = null;
        base.Stop();
    }

    public override async Task Service()
    {
        var amfContext = new AMFContext(HttpContextManager.HttpContext.GetInputStream(), HttpContextManager.HttpContext.GetOutputStream());
        await _filterChain.InvokeFilters(amfContext);
    }

    public override Task<IMessage> ServiceMessage(IMessage message)
    {
        if (message is CommandMessage)
        {
            var commandMessage = message as CommandMessage;
            switch (commandMessage.operation)
            {
                case CommandMessage.PollOperation:
                {
                    if (Context.AMFContext.Current.Client != null)
                        Context.AMFContext.Current.Client.Renew();

                    IMessage[] messages = null;
                    _waitingPollRequests.Increment();
                    var waitIntervalMillis = _channelSettings.WaitIntervalMillis != -1 ? _channelSettings.WaitIntervalMillis : 60000;// int.MaxValue;

                    if (Context.AMFContext.Current.Client != null)
                        Context.AMFContext.Current.Client.Renew();

                    if (commandMessage.HeaderExists(CommandMessage.AMFSuppressPollWaitHeader))
                        waitIntervalMillis = 0;
                    //If async handling was not set long polling is not supported
                    if (!AMFConfiguration.Instance.AMFSettings.Runtime.AsyncHandler)
                        waitIntervalMillis = 0;
                    if (_channelSettings.MaxWaitingPollRequests <= 0 || _waitingPollRequests.Value >= _channelSettings.MaxWaitingPollRequests)
                        waitIntervalMillis = 0;

                    if (message.destination != null && message.destination != string.Empty)
                    {
                        var clientId = commandMessage.clientId as string;
                    }
                    else
                    {
                        if (Context.AMFContext.Current.Client != null)
                            messages = Context.AMFContext.Current.Client.GetPendingMessages(waitIntervalMillis);
                    }
                    _waitingPollRequests.Decrement();

                    if (messages == null || messages.Length == 0)
                        return Task.FromResult<IMessage>(new AcknowledgeMessage());
                    var resultMessage = Task.FromResult<IMessage>(new CommandMessage());
                    (resultMessage.Result as CommandMessage).operation = CommandMessage.ClientSyncOperation;
                    (resultMessage.Result as CommandMessage).body = messages;
                    return resultMessage;
                }
            }
        }
        return base.ServiceMessage(message);
    }

    public override void Push(IMessage message, MessageClient messageClient)
    {
        if (_channelSettings is { IsPollingEnabled: true })
        {
            var messageClone = message.Clone() as IMessage;
            messageClone.SetHeader(MessageBase.DestinationClientIdHeader, messageClient.ClientId);
            messageClone.clientId = messageClient.ClientId;
        }
    }
}