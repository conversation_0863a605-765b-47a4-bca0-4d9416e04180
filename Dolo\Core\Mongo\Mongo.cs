﻿namespace Dolo.Core.Mongo;

public class Mongo<T> : IMongo<T>
{
    private string? _collection;
    private string? _database;
    private string? _host;
    private IMongoCollection<T>? _mongoCollection;
    private IMongoDatabase? _mongoDatabase;
    private string? _password;
    private int _port;
    private string? _username;
    private static readonly ConcurrentDictionary<string, MongoClient> _clientCache = new();


    /// <summary>
    ///     Get all documents from collection using filter definition
    /// </summary>
    public async Task<List<T>> GetAsync(FilterDefinition<T> filterDefinition)
        => await _mongoCollection.Find(filterDefinition).ToListAsync().ConfigureAwait(false);

    /// <summary>
    ///     Get all documents from collection using filter expression
    /// </summary>
    public async Task<List<T>> GetAsync(Expression<Func<T, bool>> filter)
        => await _mongoCollection.Find(filter).ToListAsync().ConfigureAwait(false);

    /// <summary>
    ///     Get all documents from collection
    /// </summary>
    public async Task<List<T>> GetAsync()
        => await _mongoCollection!.Find(_ => true).ToListAsync().ConfigureAwait(false);

    /// <summary>
    ///     Get all documents using a filter option
    /// </summary>
    public async Task<List<T>> GetAsync(Action<MongoFilter<T>> options) {
        var filterOptions = options.GetAction().GetOptions();
        var cursor = _mongoCollection!.Find(FilterDefinition<T>.Empty);
        if (filterOptions?.Limit.HasValue == true) cursor = cursor.Limit(filterOptions.Limit);
        if (filterOptions?.Skip.HasValue == true) cursor = cursor.Skip(filterOptions.Skip);
        if (filterOptions?.Sort != null) cursor = cursor.Sort(filterOptions.Sort);
        return await cursor.ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    ///     Get all documents using a filter option
    /// </summary>
    public async Task<List<T>> GetAsync(FilterDefinition<T> filterDefinition, MongoFilter<T> options) {
        var filterOptions = options.GetOptions();
        var cursor = _mongoCollection!.Find(filterDefinition);
        if (filterOptions?.Limit.HasValue == true) cursor = cursor.Limit(filterOptions.Limit);
        if (filterOptions?.Skip.HasValue == true) cursor = cursor.Skip(filterOptions.Skip);
        if (filterOptions?.Sort != null) cursor = cursor.Sort(filterOptions.Sort);
        return await cursor.ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    ///     Get all documents using a filter option
    /// </summary>
    public async Task<List<T>> GetAsync(Expression<Func<T, bool>> filter, MongoFilter<T> options) {
        var filterOptions = options.GetOptions();
        var cursor = _mongoCollection!.Find(filter);
        if (filterOptions?.Limit.HasValue == true) cursor = cursor.Limit(filterOptions.Limit);
        if (filterOptions?.Skip.HasValue == true) cursor = cursor.Skip(filterOptions.Skip);
        if (filterOptions?.Sort != null) cursor = cursor.Sort(filterOptions.Sort);
        return await cursor.ToListAsync().ConfigureAwait(false);
    }

    /// <summary>
    ///     Get single document from collection using filter definition
    /// </summary>
    public async Task<T?> GetOneAsync(FilterDefinition<T> filterDefinition)
        => await _mongoCollection!.Find(filterDefinition).FirstOrDefaultAsync().TryAsync().ConfigureAwait(false);

    /// <summary>
    ///     Get single document from collection using filter expression
    /// </summary>
    public async Task<T?> GetOneAsync(Expression<Func<T, bool>> filter)
        => await _mongoCollection!.Find(filter).FirstOrDefaultAsync().TryAsync().ConfigureAwait(false);

    /// <summary>
    ///     Get the first document from collection using filter definition
    /// </summary>
    public async Task<T?> GetFirstAsync()
        => await _mongoCollection!.Find(Builders<T>.Filter.Empty).FirstOrDefaultAsync().TryAsync().ConfigureAwait(false);

    /// <summary>
    ///     Count all documents from collection using filter definition
    /// </summary>
    public async Task<long> CountAsync(FilterDefinition<T> filterDefinition)
        => await _mongoCollection!.CountDocumentsAsync(filterDefinition).ConfigureAwait(false);

    /// <summary>
    ///     Count all documents from collection using filter expression
    /// </summary>
    public async Task<long> CountAsync(Expression<Func<T, bool>> filter)
        => await _mongoCollection!.CountDocumentsAsync(filter).ConfigureAwait(false);

    /// <summary>
    ///     Count all documents from collection
    /// </summary>
    public async Task<long> CountAsync()
        => await _mongoCollection!.CountDocumentsAsync(Builders<T>.Filter.Empty).ConfigureAwait(false);

    /// <summary>
    ///     Delete a document from collection using filter definition
    /// </summary>
    public async Task DeleteAsync(FilterDefinition<T> filterDefinition)
        => await _mongoCollection!.DeleteOneAsync(filterDefinition).ConfigureAwait(false);

    /// <summary>
    ///     Delete a document from collection using filter expression
    /// </summary>
    public async Task DeleteAsync(Expression<Func<T, bool>> filter)
        => await _mongoCollection!.DeleteOneAsync(filter).ConfigureAwait(false);

    /// <summary>
    ///     Delete many documents from collection using filter definition
    /// </summary>
    public async Task DeleteManyAsync(FilterDefinition<T> filterDefinition)
        => await _mongoCollection!.DeleteManyAsync(filterDefinition).ConfigureAwait(false);

    /// <summary>
    ///     Delete many documents from collection using filter expression
    /// </summary>
    public async Task DeleteManyAsync(Expression<Func<T, bool>> filter)
        => await _mongoCollection!.DeleteManyAsync(filter).ConfigureAwait(false);

    /// <summary>
    ///     Update many documents from collection using filter definition
    /// </summary>
    public async Task<UpdateResult> UpdateManyAsync(FilterDefinition<T> filterDefinition, UpdateDefinition<T> updateDefinition)
        => await _mongoCollection!.UpdateManyAsync(filterDefinition, updateDefinition).ConfigureAwait(false);

    /// <summary>
    ///     Update a document from collection using filter definition
    /// </summary>
    public async Task<T?> UpdateAsync(FilterDefinition<T> filterDefinition, UpdateDefinition<T> updateDefinition, FindOneAndUpdateOptions<T>? options = null)
        => await _mongoCollection!.FindOneAndUpdateAsync(filterDefinition, updateDefinition, options ?? new FindOneAndUpdateOptions<T>
        {
            ReturnDocument = ReturnDocument.After
        }).ConfigureAwait(false);

    /// <summary>
    ///     Add a new document to collection
    /// </summary>
    public async Task AddAsync(T entity)
        => await _mongoCollection!.InsertOneAsync(entity).ConfigureAwait(false);

    /// <summary>
    ///     Add many new documents to collection
    /// </summary>
    public async Task AddAsync(IEnumerable<T> entities)
        => await _mongoCollection!.InsertManyAsync(entities).ConfigureAwait(false);

    /// <summary>
    ///     Clear all documents from collection
    /// </summary>
    public async Task ClearAsync()
        => await _mongoCollection!.DeleteManyAsync(Builders<T>.Filter.Empty).ConfigureAwait(false);

    /// <summary>
    ///     distinct values of the given field name
    /// </summary>
    public async Task<List<T>> DistinctAsync(Expression<Func<T, T>> field, FilterDefinition<T>? filter = null)
        => await (await _mongoCollection!.DistinctAsync(field, filter ?? Builders<T>.Filter.Empty).ConfigureAwait(false)).ToListAsync().ConfigureAwait(false);

    /// <summary>
    ///     distinct values of the given field name
    /// </summary>
    public async Task<List<T>> DistinctAsync(Expression<Func<T, T>> field, string filter)
        => await (await _mongoCollection!.DistinctAsync(field, filter).ConfigureAwait(false)).ToListAsync().ConfigureAwait(false);

    /// <summary>
    ///     Update a document from collection using filter definition
    /// </summary>
    public async Task<T?> UpdateAsync(UpdateDefinition<T> updateDefinition)
        => await _mongoCollection!.FindOneAndUpdateAsync(FilterDefinition<T>.Empty, updateDefinition, new FindOneAndUpdateOptions<T>
        {
            ReturnDocument = ReturnDocument.After
        }).ConfigureAwait(false);

    /// <summary>
    ///     Replace the whole document with the new one
    /// </summary>
    public async Task ReplaceAsync(FilterDefinition<T> filterDefinition, T document)
        => await _mongoCollection!.ReplaceOneAsync(filterDefinition, document).ConfigureAwait(false);


    /// <summary>
    ///     Initializes mongodb connection
    /// </summary>
    public Mongo<T> Initialize()
    {
        var connectionString = $"mongodb://{_username}:{_password}@{_host}:{_port}";
        var client = _clientCache.GetOrAdd(connectionString, _ => new MongoClient(connectionString));

        _mongoCollection = client
            .GetDatabase(_database ?? "admin")
            .GetCollection<T>(_collection);

        _mongoDatabase = _mongoCollection.Database;
        return this;
    }

    /// <summary>
    ///     Set Port for Mongo
    /// </summary>
    public Mongo<T> SetPort(int port)
    {
        _port = port;
        return this;
    }

    /// <summary>
    ///     Set Host for Mongo
    /// </summary>
    public Mongo<T> SetHost(string? host)
    {
        _host = host;
        return this;
    }

    /// <summary>
    ///     Set username for authentication
    /// </summary>
    public Mongo<T> SetUsername(string? username)
    {
        _username = username;
        return this;
    }

    /// <summary>
    ///     Set password for authentication
    /// </summary>
    public Mongo<T> SetPassword(string? password)
    {
        _password = password;
        return this;
    }

    /// <summary>
    ///     Set database name for Mongo
    /// </summary>
    public Mongo<T> SetDatabase(string? database)
    {
        _database = database;
        return this;
    }

    /// <summary>
    ///     Set collection name for Mongo
    /// </summary>
    public Mongo<T> SetCollection(string? collection)
    {
        _collection = collection;
        return this;
    }
}
