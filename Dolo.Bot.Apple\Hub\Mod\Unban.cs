﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Mod;

public class Unban
{
    [RequirePermissions(DiscordPermission.MuteMembers)]
    [Command("unban")]
    [Description("unban a user from the server")]
    public async Task UnbanAsync(SlashCommandContext ctx, [Description("the user to unban from the server")] DiscordUser user)
    {
        await ctx.Interaction.DeferAsync(true);

        // check if the user is null
        if (Hub.Guild is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You must specify a user to unban");
            return;
        }

        // check if the user is the user who is running the command
        if (user.Id == ctx.User.Id)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » You cannot unban yourself");
            return;
        }

        var ban = await ctx.Guild!.TryGetBanAsync(user);
        if(ban is {})
           await ctx.Guild!.UnbanMemberAsync(user);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);

        // check if the user is in the database
        if (member is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user you specified is not in the database");
            return;
        }

        if (!ctx.Guild!.TryGetMember(user.Id, out var guildMember))
        {
            // check if already unbanned
            if (!member.State.IsBanned)
            {
                await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user you specified is already unbanned");
                return;
            }

            await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, user.Id),
            Builders<ServerMember>.Update.Set(a => a.State.IsBanned, false));

            // send the response
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » {user.Mention} has been unbanned from the server");
            return;
        }

        // check if the user is already unbanned
        if (!guildMember.Roles.Contains(HubRoles.Banned) && !member.State.IsBanned)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » The user you specified is already unbanned");
            return;
        }

        // revoke the role from the member
        await guildMember.TryRevokeAsync(HubRoles.Banned);

        // remove timeout
        await guildMember.TryRemoveTimeoutAsync();

        // print message to the user
        await ctx.TryEditResponseAsync($"{HubEmoji.Pluto} » {user.Mention} has been unbanned from the server");

        // unban from database
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, user.Id),
        Builders<ServerMember>.Update.Set(a => a.State.IsBanned, false));
    }
}