﻿using Microsoft.JSInterop.Implementation;
namespace Dolo.Core;

public abstract class JsModule : IAsyncDisposable
{
    private readonly Task<JSObjectReference> _runtime;
    protected JsModule(IJSRuntime runtime, string modulePath)
        => _runtime = runtime.InvokeAsync<JSObjectReference>("import", modulePath).AsTask();

    public ValueTask DisposeAsync()
        => _runtime.Result.DisposeAsync();

    /// <summary>
    ///     Invoke a function exported from the module
    /// </summary>
    public async ValueTask<T> InvokeAsync<T>(string identifier, params object[] args)
        => await (await _runtime).InvokeAsync<T>(identifier, args);

    /// <summary>
    ///     Invoke a function exported from the module
    /// </summary>
    protected async ValueTask InvokeVoidAsync(string identifier, params object[] args)
        => await (await _runtime).InvokeVoidAsync(identifier, args);
}