﻿namespace Dolo.Core.Mongo;

public class MongoFilter<T>
{
    private readonly FindOptions<T>? _options;
    public MongoFilter() => _options = new();

    /// <summary>
    ///     Limit the number of documents returned
    /// </summary>
    /// <param name="limit"></param>
    /// <returns></returns>
    public MongoFilter<T> Limit(int limit)
    {
        _options!.Limit = limit;
        return this;
    }

    /// <summary>
    ///     Skip the specified number of documents before returning the rest.
    /// </summary>
    /// <param name="skip"></param>
    /// <returns></returns>
    public MongoFilter<T> Skip(int skip)
    {
        _options!.Skip = skip;
        return this;
    }


    /// <summary>
    ///     Order by the specified field.
    /// </summary>
    /// <param name="expression"></param>
    /// <returns></returns>
    public MongoFilter<T> OrderBy(Expression<Func<T, object>> expression)
    {
        _options!.Sort = Builders<T>.Sort.Ascending(expression);
        return this;
    }

    /// <summary>
    ///     Order by descending
    /// </summary>
    /// <param name="expression"></param>
    /// <returns></returns>
    public MongoFilter<T> OrderByDescending(Expression<Func<T, object>> expression)
    {
        _options!.Sort = Builders<T>.Sort.Descending(expression);
        return this;
    }


    /// <summary>
    ///     Get the filter options
    /// </summary>
    /// <returns></returns>
    public FindOptions<T>? GetOptions() => _options;
}