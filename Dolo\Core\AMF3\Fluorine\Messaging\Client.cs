﻿#nullable disable// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
using Dolo.Core.AMF3.Fluorine.Collections;
using Dolo.Core.AMF3.Fluorine.Messaging.Api;
using Dolo.Core.AMF3.Fluorine.Messaging.Messages;
namespace Dolo.Core.AMF3.Fluorine.Messaging;

internal class Client : AttributeStore, IClient
{
    private static readonly object _syncLock = new();
    private readonly ClientManager _clientManager;
    protected CopyOnWriteDictionary _connectionToScope = new();

    private CopyOnWriteArray _messageClients;
    private bool _polling;

    internal Client(ClientManager clientManager, string id)
    {
        _clientManager = clientManager;
        Id = id;
        ClientLeaseTime = 1;
        _polling = false;
    }

    internal IList MessageClients
    {
        get
        {
            if (_messageClients == null)
                lock (SyncRoot)
                    if (_messageClients == null)
                        _messageClients = new();

            return _messageClients;
        }
    }

    public void Register(IConnection connection)
    {
        _connectionToScope.Add(connection, connection.Scope);
    }

    public void Unregister(IConnection connection)
    {
        _connectionToScope.Remove(connection);
        if (_connectionToScope.Count == 0)
            // This client is not connected to any scopes, remove from registry.
            Disconnect();
    }

    internal void SetClientLeaseTime(int value)
    {
        ClientLeaseTime = value;
    }


    #region IClient Members

    public string Id { get; }

    public int ClientLeaseTime { get; private set; }

    public object SyncRoot => _syncLock;

    public ICollection Scopes => _connectionToScope.Values;

    public ICollection Connections => _connectionToScope.Keys;

    public void RegisterMessageClient(IMessageClient messageClient)
    {
        if (!MessageClients.Contains(messageClient)) MessageClients.Add(messageClient);
    }

    public void UnregisterMessageClient(IMessageClient messageClient)
    {
        //This operation was possibly initiated by this client
        if (messageClient.IsDisconnecting)
            return;
        if (MessageClients.Contains(messageClient)) MessageClients.Remove(messageClient);
        if (MessageClients.Count == 0) Disconnect();
    }

    public void Disconnect(bool timeout)
    {
        lock (SyncRoot)
        {
            //restore context
            IConnection currentConnection = null;
            if (Connections is { Count: > 0 })
            {
                var enumerator = Connections.GetEnumerator();
                enumerator.MoveNext();
                currentConnection = enumerator.Current as IConnection;
            }

            _clientManager.RemoveSubscriber(this);
            if (_messageClients != null)
            {
                foreach (MessageClient messageClient in _messageClients)
                    if (timeout)
                        messageClient.Timeout();
                    else
                        messageClient.Disconnect();
                _messageClients.Clear();
            }

            foreach (IConnection connection in Connections)
            {
                if (timeout)
                    connection.Timeout();
                connection.Close();
            }
        }
    }

    public void Disconnect()
    {
        Disconnect(false);
    }

    public void Timeout()
    {
        Disconnect(true);
    }

    public IMessage[] GetPendingMessages(int waitIntervalMillis)
    {
        var messages = new ArrayList();
        _polling = true;
        do
        {
            _clientManager.LookupClient(Id);//renew

            if (waitIntervalMillis == 0)
            {
                _polling = false;
                return messages.ToArray(typeof(IMessage)) as IMessage[];
            }

            if (messages.Count > 0)
            {
                _polling = false;
                return messages.ToArray(typeof(IMessage)) as IMessage[];
            }

            Thread.Sleep(500);
            waitIntervalMillis -= 500;
            if (waitIntervalMillis <= 0)
                _polling = false;
        } while (_polling);

        return messages.ToArray(typeof(IMessage)) as IMessage[];
    }

    /// <summary>
    ///     Renews a lease.
    /// </summary>
    public void Renew()
    {
        _clientManager.LookupClient(Id);
    }

    /// <summary>
    ///     Renews a lease.
    /// </summary>
    /// <param name="clientLeaseTime">The amount of time in minutes before client times out.</param>
    public void Renew(int clientLeaseTime)
    {
        _clientManager.Renew(this, clientLeaseTime);
    }

    #endregion
}