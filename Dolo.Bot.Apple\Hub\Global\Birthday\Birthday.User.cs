﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.Global.Birthday;

public partial class Birthday
{
    [Command("user")]

[Description("show someones birthday")]
    public async Task UserAsync(SlashCommandContext ctx, [Description("the user")] DiscordUser user)
    {
        await ctx.Interaction.DeferAsync(ctx.Channel == HubChannel.Birthday);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == user.Id);
        if (member is null)
            return;

        // member have to add the birthday 
        if (member.Birthday is null)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » {user.Username} has not added his birthday yet.");
            return;
        }

        // print the birthday of the member
        await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » {user.Username}'s birthday is in `{member.Birthday.DayOfBirth.AddHours(2).GetTimeString()}`");
    }
}