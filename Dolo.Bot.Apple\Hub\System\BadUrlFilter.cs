﻿using Dolo.Database;
namespace Dolo.Bot.Apple.Hub.System;

public static partial class BadUrlFilter
{
    public static async Task<bool> IsOkAsync(MessageArgs e)
    {
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == e.Author.Id);
        var serverSettings = await Mongo.ServerSettings.GetFirstAsync();
        if (member is null || serverSettings is null)
            return true;

        if (!e.Guild.TryGetMember(e.Author.Id, out var guildMember)
            || e.Author.IsBot
            || guildMember.Roles.ContainsMany(HubRoles.Friend, HubRoles.Admin)
            || HubChannel.DevLog is null)
            return true;


        // check if the message is a discord link
        if (!IsDiscordUrl(e))
            return true;

        // delete the message first
        await e.Message.TryDeleteMessageAsync();

        // timeout the member
        await guildMember.TryTimeoutAsync(HubConstant.TimeoutTime, $"Sending a discord link that is not from this server. {e.Message.Content}");

        // grant the ban role
        await guildMember.TryGrantRoleAsync(HubRoles.Banned);

        // send message into chat
        await e.Message.Channel.TrySendMessageAsync($"{HubEmoji.Pluto} » Banned <@{e.Author.Id}> for sending a discord link into the chat.");

        // send message to dev log
        await HubChannel.DevLog.TrySendMessageAsync(HubEmbed.DevLog(e.Author, 0, member, e));

        // kick member out of the server
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, e.Author.Id),
        Builders<ServerMember>.Update
            .Set(a => a.State.IsBanned, true)
            .Set(a => a.State.BanReason, $"User sent a discord link that is not from this server. {e.Message.Content}"));
        return false;
    }

    private static bool IsDiscordUrl(MessageArgs msg)
    {
        // check if the invite code is in the message
        // check if the message does not contains discord.gg/dolo
        // check if the message is a discord link using regex
        var hasInvite = HubCache.Invite.Any(a => msg.Message.Content.ToLower().Contains(a.Key.ToLower()));
        var hasDolo = msg.Message.Content.ToLower().ContainsAny("discord.gg/dolo", "discord.com/invite/dolo", "discord.com/gift", "discord.gg/mspshop");
        var hasMatch = RegexDiscordUrl().IsMatch(msg.Message.Content.ToLower());
        var hasRole = msg.Guild.TryGetMember(msg.Author.Id, out var member) && member.Roles.ContainsMany(HubRoles.High, HubRoles.Epic);

        return !hasInvite && !hasDolo && !hasRole && hasMatch;
    }

    [GeneratedRegex("(?:https?:\\/\\/)?(?:www\\.)?(discord\\.gg\\/|discord(?:app)?\\.com\\/invite\\/)([a-zA-Z0-9]+)")]
    private static partial Regex RegexDiscordUrl();
}