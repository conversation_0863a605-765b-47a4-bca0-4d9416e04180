import React, { useEffect, useRef } from 'react';

/**
 * UpdateNotificationModal Component
 *
 * A simple notification modal that appears when the toolbox has been updated.
 * Shows a brief message about the update with a "Got it" button to dismiss.
 * Styled to match the Blazor web app design with gradients and logo.
 *
 * @param {boolean} isVisible - Whether the modal is visible
 * @param {Function} onClose - Callback function when modal is closed
 * @param {string} version - Current toolbox version
 */
const UpdateNotificationModal = ({ isVisible, onClose, version }) => {
  const modalRef = useRef(null);
  const buttonRef = useRef(null);

  // Handle keyboard events
  useEffect(() => {
    if (!isVisible) return;

    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Focus the button when modal opens
    if (buttonRef.current) {
      buttonRef.current.focus();
    }

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isVisible, onClose]);

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 z-[99999] flex items-center justify-center transition-all duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
    >
      {/* Overlay with gradient similar to Tools.razor */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-indigo-900/80 to-blue-900/80"></div>
      <div
        className="absolute inset-0 opacity-[0.07]"
        style={{
          backgroundImage: "url('https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/grid-pattern.png')"
        }}
      ></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(93,123,255,0.15),transparent_50%)]"></div>

      {/* Update notification card - Smaller width */}
      <div className="relative z-[9999] w-[90%] max-w-md transform transition-all duration-300 scale-100">
        <div className="relative overflow-hidden rounded-2xl shadow-2xl">
          {/* Background gradient with similar pattern to Tools.razor navbar */}
          <div className="absolute inset-0">
            <div
              className="w-full h-full bg-repeat opacity-20"
              style={{
                backgroundImage: "url(https://raw.githubusercontent.com/cydolo/assets/main/pluto/background.png)"
              }}
            ></div>
            <div className="absolute inset-0 bg-gradient-to-r from-[#2658C7] to-[#2456C3]"></div>
          </div>

          {/* Simplified content */}
          <div className="relative pt-6 px-6 pb-6">
            <div className="flex items-center gap-3 mb-4">
              {/* Logo with glow effect */}
              <div className="relative">
                <div className="absolute inset-0 bg-blue-500/30 blur-sm rounded-full"></div>
                <img
                  src="https://raw.githubusercontent.com/cydolo/assets/main/moviestarplanet/logo.png"
                  alt="Pluto Logo"
                  className="relative h-8 w-8"
                />
              </div>
              <div className="flex-1">
                <span className="text-base font-bold text-white tracking-wide">Toolbox Updated</span>
              </div>
            </div>

            {/* Simple message */}
            <p className="text-blue-100/90 text-sm leading-relaxed mb-6">
              We've updated our toolbox to version {version} with new features and improvements for a better experience. Thanks for using Pluto! ❤️
            </p>

            {/* Button */}
            <div className="flex justify-center">
              <button
                ref={buttonRef}
                onClick={onClose}
                className="group flex items-center gap-2 px-6 py-2 rounded-lg bg-[#2E66D8]/80 hover:bg-[#2E66D8] border border-white/10 transition-all duration-200 transform hover:scale-105 shadow-lg focus:outline-none focus:ring-2 focus:ring-white/20"
              >
                <span className="text-sm font-medium text-white">Got it</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpdateNotificationModal;
