using Dolo.Core.Interceptor.Models;
using Dolo.Core.Interceptor.Services;
using Microsoft.Extensions.Logging;
namespace Dolo.Core.Interceptor.Extensions;

/// <summary>
/// Extension methods for the HTTPS interceptor
/// </summary>
public static class InterceptorExtensions
{
    /// <summary>
    /// Configures the interceptor for specific Movie Star Planet domains
    /// </summary>
    public static InterceptorConfig ForMovieStarPlanet(this InterceptorConfig config)
    {
        config.TargetDomains =
        [
            "mspapis.com",
            "mspcdns.com",
            "*.mspapis.com",
            "*.mspcdns.com"
        ];
        config.CertificateOrganization = "Movie Star Planet";
        return config;
    }  

    /// <summary>
    /// Adds a domain pattern to intercept
    /// </summary>
    public static InterceptorConfig AddDomain(this InterceptorConfig config, string domain)
    {
        if (!config.TargetDomains.Contains(domain))
        {
            config.TargetDomains.Add(domain);
        }
        return config;
    }

    /// <summary>
    /// Sets a custom port for the proxy
    /// </summary>
    public static InterceptorConfig UsePort(this InterceptorConfig config, int port)
    {
        config.ProxyPort = port;
        return config;
    }

    /// <summary>
    /// Configures OCSP validation
    /// </summary>
    public static InterceptorConfig UseOCSP(this InterceptorConfig config)
    {
        config.EnableOcspValidation = true;
        return config;
    }

    /// <summary>
    /// Configures logging for the interceptor
    /// </summary>
    public static InterceptorConfig UseLogger(this InterceptorConfig config, Action<ILoggingBuilder> configure)
    {
        return config.UseLogger(configure);
    }

    /// <summary>
    /// Configures console logging for the interceptor
    /// </summary>
    public static InterceptorConfig UseConsoleLogging(this InterceptorConfig config)
    {
        return config.UseLogger(builder => builder.AddConsole());
    }

    /// <summary>
    /// Configures console and debug logging for the interceptor
    /// </summary>
    public static InterceptorConfig UseConsoleAndDebugLogging(this InterceptorConfig config)
    {
        return config.UseLogger(builder => 
        {
            builder.AddConsole();
            builder.AddDebug();
        });
    }

    /// <summary>
    /// Determines if a hostname matches any of the configured target domains
    /// </summary>
    public static bool MatchesAnyTargetDomain(this string hostname, InterceptorConfig config)
    {
        foreach (var domain in config.TargetDomains)
        {
            if (hostname.EndsWith(domain.TrimStart('*'), StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }
        }
        return false;
    }
}
