using Dolo.Planet;
using Dolo.Planet.Enums;
using Dolo.Pluto.Shard.Components.Toast;
using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class AccountSetup : ComponentBase, IAsyncDisposable
{
    [Inject] public ToastService ToastService { get; set; } = null!;

    public AccountData AccountA { get; set; } = new();
    public AccountData AccountB { get; set; } = new();

    public Server SelectedServer { get; set; } = Server.UnitedStates;

    public Dictionary<Server, string> ServerFlags { get; set; } = new()
    {
        {
            Server.UnitedStates,
            """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#B22234"/><rect width="24" height="1.23" y="1.23" fill="white"/><rect width="24" height="1.23" y="3.69" fill="white"/><rect width="24" height="1.23" y="6.15" fill="white"/><rect width="24" height="1.23" y="8.62" fill="white"/><rect width="24" height="1.23" y="11.08" fill="white"/><rect width="24" height="1.23" y="13.54" fill="white"/><rect width="9.6" height="8.62" fill="#3C3B6E"/></svg>"""
        },
        {
            Server.UnitedKingdom,
            """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#012169"/><path d="M0 0l24 16M24 0L0 16" stroke="white" stroke-width="1.6"/><path d="M0 0l24 16M24 0L0 16" stroke="#C8102E" stroke-width="1"/></svg>"""
        },
        {
            Server.Germany,
            """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="5.33" fill="black"/><rect width="24" height="5.33" y="5.33" fill="#DD0000"/><rect width="24" height="5.33" y="10.67" fill="#FFCE00"/></svg>"""
        },
        {
            Server.France,
            """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="8" height="16" fill="#002395"/><rect width="8" height="16" x="8" fill="white"/><rect width="8" height="16" x="16" fill="#ED2939"/></svg>"""
        },
        {
            Server.Spain,
            """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="4" fill="#AA151B"/><rect width="24" height="8" y="4" fill="#F1BF00"/><rect width="24" height="4" y="12" fill="#AA151B"/></svg>"""
        },
        {
            Server.Sweden,
            """<svg class="w-3 h-2" viewBox="0 0 24 16" fill="none"><rect width="24" height="16" fill="#006AA7"/><rect width="24" height="2" y="7" fill="#FECC00"/><rect width="2" height="16" x="7" fill="#FECC00"/></svg>"""
        },
        { Server.Netherlands, "🇳🇱" },
        { Server.Australia, "🇦🇺" },
        { Server.Denmark, "🇩🇰" },
        { Server.Norway, "🇳🇴" },
        { Server.Finland, "🇫🇮" },
        { Server.Poland, "🇵🇱" },
        { Server.Turkey, "🇹🇷" },
        { Server.Canada, "🇨🇦" },
        { Server.Ireland, "🇮🇪" },
        { Server.NewZealand, "🇳🇿" }
    };

    public string CurrentServerFlag =>
        ServerFlags.TryGetValue(SelectedServer, out var flag) ? flag : ServerFlags[Server.UnitedStates];

    public bool IsConnecting { get; set; }
    public string ConnectingAccount { get; set; } = "";

    public async ValueTask DisposeAsync()
    {
        // Dispose of MspClients when component is disposed
        if (AccountA.MspClient != null)
        {
            await AccountA.MspClient.DisposeAsync();
            AccountA.MspClient = null;
        }

        if (AccountB.MspClient != null)
        {
            await AccountB.MspClient.DisposeAsync();
            AccountB.MspClient = null;
        }

        GC.SuppressFinalize(this);
    }

    public string GetServerName(Server server)
    {
        return server.ToString();
    }

    private async Task ToggleConnectionAsync(string account)
    {
        var accountData = account == "A" ? AccountA : AccountB;

        if (accountData.IsConnected)
        {
            // Use the centralized logout method
            await LogoutAccountAsync(account);
        }
        else
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(accountData.Username) || string.IsNullOrWhiteSpace(accountData.Password))
            {
                await ShowValidationMessage("Please enter both username and password");
                return;
            }

            // Check for duplicate account credentials
            if (IsDuplicateAccount(account, accountData.Username, accountData.Password))
            {
                var otherAccountName = account == "A" ? "Account B" : "Account A";
                await ShowValidationMessage(
                    $"Cannot login with the same account credentials as {otherAccountName}. Please use different login details.");
                return;
            }

            // Set connecting state
            IsConnecting = true;
            ConnectingAccount = account;
            StateHasChanged();

            try
            {
                // Create MspClient with configuration
                var mspClient = new MspClient(config =>
                {
                    config.Username = accountData.Username;
                    config.Password = accountData.Password;
                    config.Server = SelectedServer;
                    config.UseOriginalBehaviour();
                });

                // Attempt login
                var loginResult = await mspClient.LoginAsync();

                if (loginResult.Success)
                {
                    // Login successful
                    accountData.MspClient = mspClient;
                    accountData.IsConnected = true;

                    // Check gift limit after successful login
                    await CheckGiftLimitAsync(accountData, account);
                }
                else
                {
                    // Login failed
                    await mspClient.DisposeAsync();
                    await ShowValidationMessage($"Login failed: {loginResult.Status ?? "Unknown error"}");
                }
            }
            catch (Exception ex)
            {
                // Handle connection errors
                await ShowValidationMessage($"Connection error: {ex.Message}");
            }
            finally
            {
                // Reset connecting state
                IsConnecting = false;
                ConnectingAccount = "";
                StateHasChanged();
            }
        }
    }

    private async Task ShowValidationMessage(string message)
    {
        // Only show toast notifications for errors and warnings, not success messages
        if (message.Contains("failed") || message.Contains("error") || message.Contains("Error"))
            ToastService.ShowError(message, "Login Error");
        else if (message.Contains("same account") || message.Contains("duplicate") || message.Contains("already"))
            ToastService.ShowWarning(message, "Account Warning");
        else if (!message.Contains("Successfully"))
            // Show other non-success messages as warnings
            ToastService.ShowWarning(message, "Account Notice");
        // Success messages are not shown as toasts - only logged
        Console.WriteLine($"Account Message: {message}");
        await Task.CompletedTask;
    }

    public bool IsAccountConnecting(string account)
    {
        return IsConnecting && ConnectingAccount == account;
    }

    private bool IsDuplicateAccount(string account, string username, string password)
    {
        var otherAccount = account == "A" ? AccountB : AccountA;

        // Check if trying to login with same credentials as the other account
        if (otherAccount.IsConnected &&
            string.Equals(otherAccount.Username, username, StringComparison.OrdinalIgnoreCase) &&
            otherAccount.Password == password)
            return true;

        return false;
    }

    private async Task CheckGiftLimitAsync(AccountData accountData, string account)
    {
        try
        {
            if (accountData.MspClient == null) return;

            var giftLimitResult = await accountData.MspClient.HasGiftLimitAsync();
            accountData.HasGiftLimit = giftLimitResult.Value;

            if (accountData.HasGiftLimit)
            {
                await ShowValidationMessage($"⚠️ Account {account} has reached daily gift limit. Consider using a different account for exchange.");
            }
            else
            {
                await ShowValidationMessage($"✅ Account {account} connected successfully and ready for exchange!");
            }
        }
        catch (Exception ex)
        {
            await ShowValidationMessage($"⚠️ Account {account} connected but couldn't check gift limit: {ex.Message}");
        }
    }

    public bool AreAccountsReadyForExchange()
    {
        return AccountA.IsConnected && AccountB.IsConnected &&
               !AccountA.HasGiftLimit && !AccountB.HasGiftLimit;
    }

    public (bool HasLimit, string AccountName) GetAccountWithGiftLimit()
    {
        if (AccountA.HasGiftLimit) return (true, "A");
        if (AccountB.HasGiftLimit) return (true, "B");
        return (false, "");
    }

    public async Task LogoutAccountAsync(string account)
    {
        var accountData = account == "A" ? AccountA : AccountB;

        if (accountData.MspClient != null)
        {
            await accountData.MspClient.DisposeAsync();
        }

        accountData.Username = "";
        accountData.Password = "";
        accountData.IsConnected = false;
        accountData.HasGiftLimit = false;
        accountData.MspClient = null;

        await ShowValidationMessage($"Account {account} has been logged out.");
        StateHasChanged();
    }

    public class AccountData
    {
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
        public bool IsConnected { get; set; }
        public bool HasGiftLimit { get; set; }
        public MspClient? MspClient { get; set; }
    }
}