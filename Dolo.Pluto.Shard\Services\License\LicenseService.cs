using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Shard.Services.License;

public class LicenseService(
    ILicenseFileService licenseFileService,
    ILicenseValidationService licenseValidationService,
    UrlBuilderService urlBuilderService,
    ILogger<LicenseService> logger) : ILicenseService
{
    public string? Token { get; set; }

    public async Task<LicenseValidationResult> IsAllowedToUseToolAsync(string? token = null)
    {
        if (licenseFileService.LicenseFileExists())
        {
            var fileContent = await licenseFileService.ReadLicenseFileAsync();

            if (string.IsNullOrEmpty(fileContent))
            {
                logger.LogWarning("License file is empty");
                return new LicenseValidationResult().SetValid(false).SetErrorMessage("License file is empty.");
            }

            var isLicenseFileValid = await licenseValidationService.ValidateTokenAsync(fileContent);
            if (!isLicenseFileValid)
            {
                try
                {
                    await licenseFileService.DeleteLicenseFileAsync();
                }
                catch (IOException ex)
                {
                    logger.LogError(ex, "Could not delete invalid license file");
                }
                return new LicenseValidationResult().SetValid(false).SetErrorMessage("License file is invalid.");
            }

            Token = fileContent;
            logger.LogInformation("License file validation successful");
            return new LicenseValidationResult().SetValid(true);
        }

        if (string.IsNullOrEmpty(token))
        {
            logger.LogWarning("No license file found and no token provided");
            return new LicenseValidationResult().SetValid(false).SetErrorMessage("Authentication required");
        }

        var isLicenseValid = await licenseValidationService.ValidateTokenAsync(token);
        if (!isLicenseValid)
        {
            logger.LogWarning("License validation failed for provided token");
            return new LicenseValidationResult().SetValid(false).SetErrorMessage("License validation failed.");
        }

        try
        {
            await licenseFileService.WriteLicenseFileAsync(token);
            Token = token;
            return new LicenseValidationResult().SetValid(true);
        }
        catch (IOException ex)
        {
            logger.LogError(ex, "Error writing license file");
            Token = token;
            return new LicenseValidationResult().SetValid(true).SetErrorMessage("Note: License is valid but couldn't be saved to disk.");
        }
    }

    public async Task LaunchAuthenticationAsync()
    {
        var authUrl = new Uri(urlBuilderService.AuthUrl());
        await Launcher.OpenAsync(authUrl);
    }
}
