﻿namespace Dolo.Core.Mongo;

public sealed class MongoConfig
{
    /// <summary>
    ///     Hostname or IP-Adress
    /// </summary>
    public string? Host { get; set; }
    /// <summary>
    ///     Username for Credentials
    /// </summary>
    public string? Username { get; set; }
    /// <summary>
    ///     Passowrd for Credentials
    /// </summary>
    public string? Password { get; set; }
    /// <summary>
    ///     Name of the database
    /// </summary>
    public string? Database { get; set; }
    /// <summary>
    ///     Name of the collection
    /// </summary>
    public string? Collection { get; set; }
    /// <summary>
    ///     Port of the Server
    /// </summary>
    public int Port { get; set; }
}