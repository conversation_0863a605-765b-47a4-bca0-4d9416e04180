﻿using System.Reflection;
using Dolo.Core.AMF3.Fluorine;
using Dolo.Core.AMF3.Fluorine.IO;
using MethodTimer;
namespace Dolo.Planet.NET.Utils;

[Obfuscation(Feature = "apply to member * when method: renaming", Exclude = true)]
[Obfuscation(Feature = "internalization", Exclude = true)]
internal static class AMFBuilder {
    /// <summary>
    ///     Encode the Objects into a ActionMessageFormat
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="header"></param>
    /// <param name="host"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    [Time]
    internal static AMFMessage Encode<T>(List<AMFHeader>? header, string? host, params object?[] data)
        => new AMFMessage((ushort)ObjectEncoding.AMF3)
            .AddMany(new(host?.Split('=')[1], typeof(T).GetHashCode().ToString(), data), header);

    /// <summary>
    ///     Decode the ActionMessageFormat as Stream into a Object
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    [Time]
    internal static async Task<AMFContent> DecodeAsync(Stream data)
        => await new AMFDeserializer(data)
               .DecodeAsync(data);

    /// <summary>
    ///     Decode the ActionMessageFormat as Stream into a Object
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    [Time]
    internal static async Task<AMFContent> DecodeAsync(byte[] data)
    {
        using var stream = new MemoryStream(data);
        return await new AMFDeserializer(stream).DecodeAsync(stream);
    }

    private static async Task<AMFContent> DecodeAsync(this AMFDeserializer deserializer, Stream data) {
        var message = deserializer.ReadAMFMessage();
        var body = message.TryGetBodyAt(0);
        var header = message.GetHeaders();

        // Reset stream position to beginning for raw content reading
        if (data.CanSeek)
        {
            data.Position = 0;
        }

        var content = await body.TryGetContentAsync(data);
        content.Header = message.TryGetHeaderAt(0);
        content.Headers = header;
        content.Bodies = message.Bodies;

        return content;
    }
}
