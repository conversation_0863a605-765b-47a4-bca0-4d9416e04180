using System.Text;
using Dolo.Core.AMF3.Fluorine;
using Dolo.Core.AMF3.Fluorine.IO;
using Dolo.Core.Interceptor.Models;
using Dolo.Planet.NET.Utils;

namespace Dolo.Core.Interceptor.Services;

public class AmfService
{
    private const string AMF_CONTENT_TYPE = "application/x-amf";
    private const string AMF_CONTENT_TYPE_ALT = "application/amf";

    public bool IsAmfContent(HttpInterceptedRequestMessage? request)
    {
        if (request?.Content == null || request.Content.Length == 0) 
            return false;
        
        // AMF requests are always POST/PUT, never GET/HEAD/OPTIONS
        if (string.Equals(request.Method, "GET", StringComparison.OrdinalIgnoreCase) ||
            string.Equals(request.Method, "HEAD", StringComparison.OrdinalIgnoreCase) ||
            string.Equals(request.Method, "OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return false;
        }
        
        // Priority 1: Check for explicit AMF content type headers
        if (HasAmfContentType(request.ContentType))
        {
            return true;
        }
        
        // Priority 2: Check for AMF binary signature as fallback
        return HasAmfSignature(request.Content);
    }

    public bool IsAmfContent(HttpInterceptedResponseMessage? response)
    {
        if (response?.Content == null || response.Content.Length == 0) 
            return false;
        
        // Priority 1: Check for explicit AMF content type headers
        if (HasAmfContentType(response.ContentType))
        {
            return true;
        }
        
        // Priority 2: Check for AMF binary signature as fallback
        return HasAmfSignature(response.Content);
    }

    private static bool HasAmfContentType(string? contentType)
    {
        if (string.IsNullOrEmpty(contentType))
            return false;
            
        // Check for standard AMF content types
        return contentType.Contains(AMF_CONTENT_TYPE, StringComparison.OrdinalIgnoreCase) ||
               contentType.Contains(AMF_CONTENT_TYPE_ALT, StringComparison.OrdinalIgnoreCase) ||
               contentType.Contains("application/vnd.adobe.amf", StringComparison.OrdinalIgnoreCase) ||
               contentType.Contains("application/octet-stream", StringComparison.OrdinalIgnoreCase) ||
               contentType.Contains("binary/octet-stream", StringComparison.OrdinalIgnoreCase);
    }

    private static bool HasAmfSignature(byte[] content)
    {
        if (content.Length < 6)
            return false;
        
        // Fast exclusions for obvious non-AMF content
        var firstByte = content[0];
        var secondByte = content[1];
        
        // JSON content starts with { (0x7B) or [ (0x5B) or whitespace + { or [
        if (firstByte == 0x7B || firstByte == 0x5B)
            return false;
            
        // XML/HTML content starts with < (0x3C)
        if (firstByte == 0x3C)
            return false;
            
        // Check for whitespace-prefixed JSON/XML
        if (IsWhitespace(firstByte) && content.Length > 1)
        {
            for (int i = 1; i < Math.Min(content.Length, 10); i++)
            {
                if (content[i] == 0x7B || content[i] == 0x5B || content[i] == 0x3C)
                    return false;
                if (!IsWhitespace(content[i]))
                    break;
            }
        }
            
        // Plain text starting with printable ASCII (letters, numbers) is likely not AMF
        if (firstByte >= 0x30 && firstByte <= 0x7E) // 0-9, A-Z, a-z and common symbols
            return false;
              // ONLY accept very specific AMF signatures
        
        // AMF3 signature: MUST be exactly 0x00 0x03 with proper message structure
        if (firstByte == 0x00 && secondByte == 0x03)
        {
            // Additional validation: check if it looks like real AMF3 structure
            if (content.Length >= 6)
            {
                // AMF3 messages typically have 0x00 as third byte, and various values for fourth byte
                var thirdByte = content[2];
                if (thirdByte == 0x00)
                {
                    return true;
                }
            }
        }
            
        // AMF0 signature: MUST be exactly 0x00 0x00 with proper message structure  
        if (firstByte == 0x00 && secondByte == 0x00)
        {
            // Additional validation for AMF0
            if (content.Length >= 6)
            {
                var thirdByte = content[2];
                var fourthByte = content[3];
                if (thirdByte == 0x00 && (fourthByte == 0x01 || fourthByte == 0x02))
                {
                    return true;
                }
            }
        }
            
        // No other patterns are considered AMF
        return false;
    }
    
    private static bool IsWhitespace(byte b)
    {
        return b == 0x20 || b == 0x09 || b == 0x0A || b == 0x0D; // space, tab, LF, CR
    }
      public async Task<AmfInterceptedData?> DecodeAmfRequestAsync(HttpInterceptedRequestMessage request)
    {
        if (request.Content == null || !IsAmfContent(request)) 
            return null;
            
        try
        {
            // Add validation for content length
            if (request.Content.Length < 10)
            {
                return new AmfInterceptedData
                {
                    Direction = "Request",
                    Method = request.Method,
                    Uri = request.Uri?.ToString() ?? string.Empty,
                    ContentType = request.ContentType ?? string.Empty,
                    RawContent = request.Content,
                    Headers = request.Headers,
                    Timestamp = DateTime.UtcNow,
                    Error = "AMF content too short to parse"
                };
            }            var amfContent = await AMFBuilder.DecodeAsync(request.Content).ConfigureAwait(false);
            
            if (amfContent == null)
            {
                // Try to get more specific information about why decode failed
                var errorDetails = "AMF decoder returned null";
                try
                {
                    using var stream = new MemoryStream(request.Content);
                    var deserializer = new AMFDeserializer(stream);
                    var message = deserializer.ReadAMFMessage();
                    if (message == null)
                    {
                        errorDetails = "Failed to read AMF message structure";
                    }
                    else if (message.BodyCount == 0)
                    {
                        errorDetails = "AMF message has no body content";
                    }
                    else
                    {
                        errorDetails = $"AMF message parsed but body extraction failed (BodyCount: {message.BodyCount})";
                    }
                }
                catch (Exception diagnoseEx)
                {
                    errorDetails = $"AMF structure analysis failed: {diagnoseEx.Message}";
                }
                
                return new AmfInterceptedData
                {
                    Direction = "Request",
                    Method = request.Method,
                    Uri = request.Uri?.ToString() ?? string.Empty,
                    ContentType = request.ContentType ?? string.Empty,
                    RawContent = request.Content,
                    Headers = request.Headers,
                    Timestamp = DateTime.UtcNow,
                    Error = errorDetails
                };
            }
            
            return new AmfInterceptedData
            {
                Direction = "Request",
                Method = request.Method,
                Uri = request.Uri?.ToString() ?? string.Empty,
                ContentType = request.ContentType ?? string.Empty,
                DecodedContent = amfContent,
                RawContent = request.Content,
                Headers = request.Headers,
                Timestamp = DateTime.UtcNow
            };
        }        catch (EndOfStreamException ex)
        {
            return new AmfInterceptedData
            {
                Direction = "Request",
                Method = request.Method,
                Uri = request.Uri?.ToString() ?? string.Empty,
                ContentType = request.ContentType ?? string.Empty,
                RawContent = request.Content,
                Headers = request.Headers,
                Timestamp = DateTime.UtcNow,
                Error = $"AMF parsing failed - incomplete data: {ex.Message}"
            };
        }
        catch (Exception ex)
        {
            return new AmfInterceptedData
            {
                Direction = "Request",
                Method = request.Method,
                Uri = request.Uri?.ToString() ?? string.Empty,
                ContentType = request.ContentType ?? string.Empty,
                RawContent = request.Content,
                Headers = request.Headers,
                Timestamp = DateTime.UtcNow,
                Error = ex.Message
            };
        }
    }

    public async Task<AmfInterceptedData?> DecodeAmfResponseAsync(HttpInterceptedResponseMessage response, string uri = "") {
        if (response.Content == null) {
            return null;
        }

        if (!IsAmfContent(response)) {
            return null;
        }

        try
        {
            // Add validation for content length
            if (response.Content.Length < 10)
            {
                return new AmfInterceptedData
                {
                    Direction = "Response",
                    StatusCode = (int)response.StatusCode,
                    Uri = uri,
                    ContentType = response.ContentType ?? string.Empty,
                    RawContent = response.Content,
                    Headers = response.Headers,
                    Timestamp = DateTime.UtcNow,
                    Error = "AMF content too short to parse"
                };
            }

            var amfContent = await AMFBuilder.DecodeAsync(response.Content).ConfigureAwait(false);

            if (amfContent == null)
            {
                var errorDetails = "AMFBuilder.DecodeAsync returned null";

                return new AmfInterceptedData
                {
                    Direction = "Response",
                    StatusCode = (int)response.StatusCode,
                    Uri = uri,
                    ContentType = response.ContentType ?? string.Empty,
                    RawContent = response.Content,
                    Headers = response.Headers,
                    Timestamp = DateTime.UtcNow,
                    Error = errorDetails
                };
            }

            // AMFContent is never null, but Content can be null
            if (amfContent.Content == null)
            {
                // If we have raw content but no parsed content, check if it's a null response
                if (amfContent.HasValue && !string.IsNullOrEmpty(amfContent.RawContent))
                {
                    // For short content or specific patterns, treat as null response
                    if (amfContent.RawContent.Length < 50 || IsNullAmfResponse(amfContent.RawContent))
                    {
                        return new AmfInterceptedData
                        {
                            Direction = "Response",
                            StatusCode = (int)response.StatusCode,
                            Uri = uri,
                            ContentType = response.ContentType ?? string.Empty,
                            DecodedContent = amfContent,
                            RawContent = response.Content,
                            Headers = response.Headers,
                            Timestamp = DateTime.UtcNow,
                            Error = "null"
                        };
                    }
                }

                var errorDetails = amfContent.HasException ? "AMF parsing exception occurred" : "null";

                return new AmfInterceptedData
                {
                    Direction = "Response",
                    StatusCode = (int)response.StatusCode,
                    Uri = uri,
                    ContentType = response.ContentType ?? string.Empty,
                    RawContent = response.Content,
                    Headers = response.Headers,
                    Timestamp = DateTime.UtcNow,
                    Error = errorDetails
                };
            }

            // Success: AMFContent.Content is not null
            return new AmfInterceptedData
            {
                Direction = "Response",
                StatusCode = (int)response.StatusCode,
                Uri = uri,
                ContentType = response.ContentType ?? string.Empty,
                DecodedContent = amfContent,
                RawContent = response.Content,
                Headers = response.Headers,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (EndOfStreamException ex)
        {
            return new AmfInterceptedData
            {
                Direction = "Response",
                StatusCode = (int)response.StatusCode,
                Uri = uri,
                ContentType = response.ContentType ?? string.Empty,
                RawContent = response.Content,
                Headers = response.Headers,
                Timestamp = DateTime.UtcNow,
                Error = $"AMF parsing failed - incomplete data: {ex.Message}"
            };
        }        catch (Exception ex)
        {
            return new AmfInterceptedData
            {
                Direction = "Response",
                StatusCode = (int)response.StatusCode,
                Uri = uri,
                ContentType = response.ContentType ?? string.Empty,
                RawContent = response.Content,
                Headers = response.Headers,
                Timestamp = DateTime.UtcNow,
                Error = $"AMF parsing failed: {ex.Message}"
            };
        }
    }

    public async Task<AmfInterceptedData?> TryDecodeAmfFromBytesAsync(byte[] content, string direction, string uri = "", string? contentType = null)
    {
        if (string.IsNullOrEmpty(contentType) || !contentType.Contains(AMF_CONTENT_TYPE, StringComparison.OrdinalIgnoreCase))
        {
            if (!HasAmfSignature(content))
                return null;
        }
            
        try
        {
            var amfContent = await AMFBuilder.DecodeAsync(content).ConfigureAwait(false);
            return new AmfInterceptedData
            {
                Direction = direction,
                Uri = uri,
                ContentType = contentType ?? "unknown",
                DecodedContent = amfContent,
                RawContent = content,
                Timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new AmfInterceptedData
            {
                Direction = direction,
                Uri = uri,
                ContentType = contentType ?? "unknown",
                RawContent = content,
                Timestamp = DateTime.UtcNow,
                Error = ex.Message
            };
        }
    }

    private static bool IsNullAmfResponse(string rawContent)
    {
        // Check for common patterns that indicate a null AMF response
        // AMF null responses often have specific byte patterns
        if (string.IsNullOrEmpty(rawContent)) return true;

        // Check for very short responses that are likely null
        if (rawContent.Length < 20) return true;

        // You can add more specific patterns here based on actual null AMF responses
        return false;
    }
}
