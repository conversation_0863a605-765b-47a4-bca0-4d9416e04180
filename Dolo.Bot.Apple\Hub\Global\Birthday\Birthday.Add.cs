﻿using Dolo.Database;
using DSharpPlus.Commands.ArgumentModifiers;
namespace Dolo.Bot.Apple.Hub.Global.Birthday;

public partial class Birthday
{
    [Command("add")]
    [Description("add a new birthday")]
    public async Task AddAsync(SlashCommandContext ctx, [Description("define the day of your birthday")][MinMaxValue(1, 31)] int day, [Description("define the month of your birthday")][MinMaxValue(1, 12)] int month)
    {
        await ctx.Interaction.DeferAsync(ctx.Channel == HubChannel.Birthday);

        // check if the user has today birthday
        if (DateTime.Now.Day == day && DateTime.Now.Month == month)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » Happy Birthday! :)");
            return;
        }

        // check if the day is higher than the current day and the month is the same
        if (DateTime.Now.Day >= day && DateTime.Now.Month >= month)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » You can't add a birthday in the future");
            return;
        }

        // check if the month is higher than the current month
        if (DateTime.Now.Month > month)
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » You can't add a birthday in the future");
            return;
        }

        var birthday = new DateTime(DateTime.Now.Year, (int)month, (int)day);
        if (birthday < DateTime.Now)
            birthday = birthday.AddYears(1);

        // get the member from the database
        var member = await Mongo.ServerMembers.GetOneAsync(a => a.Member.DiscordId == ctx.User.Id);
        if (member is null)
            return;

        // print the birthday if it's already added
        if (member.Birthday is { })
        {
            await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » Your birthday is in {member.Birthday.DayOfBirth.AddHours(2).GetTimeString()}");
            return;
        }

        // add the new birthday to the entity
        member.Birthday = new(birthday);

        // send the message to the channel
        await ctx.TryEditResponseAsync($"{HubEmoji.Cake} » Your birthday has been added");

        // update the user in the mongodb collection with the filter of the user id
        await Mongo.ServerMembers.UpdateAsync(Builders<ServerMember>.Filter.Eq(a => a.Member.DiscordId, member.Member.DiscordId),
        Builders<ServerMember>.Update.Set(a => a.Birthday, member.Birthday));
    }
}