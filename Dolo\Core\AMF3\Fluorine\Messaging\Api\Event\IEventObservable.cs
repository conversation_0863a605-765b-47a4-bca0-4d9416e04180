﻿#pragma warning disable CS8618// Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
namespace Dolo.Core.AMF3.Fluorine.Messaging.Api.Event;

/// <summary>
///     Provides an Observer pattern, that is it has a list of objects that listen to events.
/// </summary>
internal interface IEventObservable
{
    /// <summary>
    ///     Add event listener to this observable.
    /// </summary>
    /// <param name="listener">Event listener.</param>
    void AddEventListener(IEventListener listener);
    /// <summary>
    ///     Remove event listener from this observable.
    /// </summary>
    /// <param name="listener">Event listener.</param>
    void RemoveEventListener(IEventListener listener);
    /// <summary>
    ///     Get the event listeners collection.
    /// </summary>
    /// <returns>Collection of event listeners.</returns>
    ICollection GetEventListeners();
}