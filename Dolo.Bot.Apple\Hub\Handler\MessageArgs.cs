﻿namespace Dolo.Bot.Apple.Hub.Handler;

public class MessageArgs
{

    public MessageArgs(MessageCreatedEventArgs e)
    {
        Guild = e.Guild;
        Message = e.Message;
        Channel = e.Channel;
        Author = e.Author;
    }
    public MessageArgs(MessageUpdatedEventArgs e)
    {
        Guild = e.Guild;
        Message = e.Message;
        Channel = e.Channel;
        Author = e.Author;
    }
    public DiscordGuild Guild { get; set; }
    public DiscordMessage Message { get; set; }
    public DiscordChannel Channel { get; set; }
    public DiscordUser Author { get; set; }
}